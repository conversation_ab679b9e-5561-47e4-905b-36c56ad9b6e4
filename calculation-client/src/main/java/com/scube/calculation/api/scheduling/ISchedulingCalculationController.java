package com.scube.calculation.api.scheduling;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.AddToServiceClient;
import com.scube.client.annotation.GenerateHttpExchangeProxy;
import com.scube.client.annotation.HttpExchangeWebClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.PostExchange;

@HttpExchangeWebClient(ServiceUrlConstant.CALCULATION_SERVICE)
@AddToServiceClient(ServiceUrlConstant.CALCULATION_SERVICE)
@GenerateHttpExchangeProxy
public interface ISchedulingCalculationController {

    @GetExchange("/scheduler")
    Object getAll();

    @PostExchange("/scheduler/{name}/refresh")
    void refreshScheduler(@PathVariable String name);

    @PostExchange("/scheduler/{name}/start")
    void startScheduler(@PathVariable String name);

    @PostExchange("/scheduler/{name}/stop")
    void stopScheduler(@PathVariable String name);
}
package com.scube.licensing.features.license.status;

import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatusCodeEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.test.context.ActiveProfiles;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

@ActiveProfiles("test")
class LicenseStatusRequestTest {
    @Mock
    private LicenseStatusRequest licenseStatusRequest;

    @BeforeEach
    void setUp() {
        licenseStatusRequest = new LicenseStatusRequest();
    }

    @Test
    void testSetterAndGetters() {
        licenseStatusRequest.setCode(LicenseStatusCodeEnum.CLOSED);
        licenseStatusRequest.setName("test");

        assertEquals(LicenseStatusCodeEnum.CLOSED, licenseStatusRequest.getCode());
        assertEquals("test", licenseStatusRequest.getName());
    }

    @Test
    void testToMethod() {
        licenseStatusRequest.setCode(LicenseStatusCodeEnum.CLOSED);
        licenseStatusRequest.setName("test");

        LicenseStatus licenseStatus = LicenseStatusRequest.to(licenseStatusRequest);

        Assertions.assertEquals(LicenseStatusCodeEnum.CLOSED, licenseStatus.getCode());
        assertEquals("test", licenseStatus.getName());
    }

    @Test
    void testPartialValues() {
        licenseStatusRequest.setCode(LicenseStatusCodeEnum.PENDING);
        licenseStatusRequest.setName("partialTest");

        LicenseStatus licenseStatus = LicenseStatusRequest.to(licenseStatusRequest);

        assertEquals(true, licenseStatus.isPending());
        assertEquals("partialTest", licenseStatus.getName());
    }

    @Test
    void testNameIsNull() {
        licenseStatusRequest.setCode(LicenseStatusCodeEnum.PENDING);
        licenseStatusRequest.setName(null);

        LicenseStatus licenseStatus = LicenseStatusRequest.to(licenseStatusRequest);

        assertEquals(true, licenseStatus.isPending());
        assertNull(licenseStatus.getName());
    }
}

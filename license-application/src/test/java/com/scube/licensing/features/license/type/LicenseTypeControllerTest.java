package com.scube.licensing.features.license.type;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.licensing.SharedTestConfig;
import com.scube.licensing.features.permission.Permissions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.server.ResponseStatusException;

import static com.scube.licensing.features.permissions.MockMvcHelper.END_JSON;
import static com.scube.licensing.features.permissions.MockMvcHelper.START_JSON;
import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


class LicenseTypeControllerTest extends SharedTestConfig {
    @Autowired
    private LicenseTypeService licenseTypeService;

    // Create License Type
    @Test
    @WithJwt(json = START_JSON + Permissions.LicenseType.CREATE_LICENSE_TYPE + END_JSON)
    void testCreateLicenseType() throws Exception {
        LicenseTypeResponse typeResponse = createLicenseTypeHelper("testingCodeCreate");

        assertTrue(typeResponse.getId() > 0);
        var getResponse = licenseTypeService.getLicenseTypeById(typeResponse.getId());
        assertEquals("testingName", getResponse.getName());
    }

    // Get All License Types
    @Test
    @WithJwt(json = START_JSON +
            Permissions.LicenseType.CREATE_LICENSE_TYPE + ", " +
            Permissions.LicenseType.GET_ALL_LICENSE_TYPES +
            END_JSON
    )
    void testGetAllLicenseTypes() throws Exception {
        createLicenseTypeHelper("testingCodeAll");
        var res = mockMvc.perform(get("/config/license-type"))
                .andExpect(status().isOk()).andReturn();

        String responseBody = res.getResponse().getContentAsString();
        GetAllLicenseTypeResponse response = objectMapper.readValue(responseBody, GetAllLicenseTypeResponse.class);
        var items = response.getItems().stream().filter(x -> x.getName().equals("testingName")).toList();
        assertEquals(1, items.size());
        var item = items.stream().findFirst().get();
        assertEquals("testingName", item.getName());
        assertEquals("testingCodeAll", item.getCode());
        assertEquals("testingGroupName", item.getGroupName());
        assertEquals("testingDescription", item.getDescription());
    }

    // Get License Type By ID
    @Test
    @WithJwt(json = START_JSON +
            Permissions.LicenseType.CREATE_LICENSE_TYPE + ", " +
            Permissions.LicenseType.GET_LICENSE_TYPE_BY_ID +
            END_JSON
    )
    void testGetLicenseTypeById() throws Exception {
        LicenseTypeResponse typeResponse = createLicenseTypeHelper("testingCodeGet");
        var res = mockMvc.perform(get("/config/license-type/" + typeResponse.getId()))
                .andExpect(status().isOk()).andReturn();

        String responseBody = res.getResponse().getContentAsString();
        LicenseTypeResponse response = objectMapper.readValue(responseBody, LicenseTypeResponse.class);
        assertEquals((typeResponse.getId()), response.getId());
        assertEquals("testingCodeGet", response.getCode());
        assertEquals("testingName", response.getName());
        assertEquals("testingGroupName", response.getGroupName());
        assertEquals("testingDescription", response.getDescription());
    }

    // Update License Type
    @Test
    @WithJwt(json = START_JSON +
            Permissions.LicenseType.CREATE_LICENSE_TYPE + ", " +
            Permissions.LicenseType.UPDATE_LICENSE_TYPE +
            END_JSON
    )
    void testUpdateLicenseType() throws Exception {
        LicenseTypeResponse createTypeResponse = createLicenseTypeHelper("testingCodeUpdate");
        LicenseTypeRequest request = new LicenseTypeRequest();
        request.setCode("updatedCode");
        request.setName("updatedName");
        request.setGroupName("updatedGroupName");
        request.setDescription("updatedDescription");

        mockMvc.perform(put("/config/license-type/" + createTypeResponse.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk());

        var getResponse = licenseTypeService.getLicenseTypeById(createTypeResponse.getId());
        assertEquals("updatedCode", getResponse.getCode());
        assertEquals("updatedName", getResponse.getName());
        assertEquals("updatedGroupName", getResponse.getGroupName());
        assertEquals("updatedDescription", getResponse.getDescription());

    }

    // Delete License Type
    @Test
    @WithJwt(json = START_JSON +
            Permissions.LicenseType.CREATE_LICENSE_TYPE + ", " +
            Permissions.LicenseType.DELETE_LICENSE_TYPE +
            END_JSON
    )
    void testDeleteLicenseType() throws Exception {
        LicenseTypeResponse createTypeResponse = createLicenseTypeHelper("testingCodeDelete");
        var id = createTypeResponse.getId();

        mockMvc.perform(delete("/config/license-type/" + id))
                .andExpect(status().isNoContent());

        assertThrows(ResponseStatusException.class, () -> licenseTypeService.getLicenseTypeById(id));
    }


    // Helper method for Create License Type
    private LicenseTypeResponse createLicenseTypeHelper(String code) throws Exception {
        LicenseTypeRequest request = new LicenseTypeRequest();
        request.setName("testingName");
        request.setCode(code);
        request.setGroupName("testingGroupName");
        request.setDescription("testingDescription");

        var res = mockMvc.perform(post("/config/license-type")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated()).andReturn();

        String responseBody = res.getResponse().getContentAsString();

        assertNotNull(responseBody);
        return objectMapper.readValue(responseBody, LicenseTypeResponse.class);

    }

}

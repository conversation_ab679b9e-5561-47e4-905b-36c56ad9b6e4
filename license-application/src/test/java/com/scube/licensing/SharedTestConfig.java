package com.scube.licensing;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.config_utils.json_storage.WithJsonStorage;
import com.scube.licensing.features.events.service.EventTypeService;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.permissions.TestUtils;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantGroupRepository;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantStatusRepository;
import com.scube.rabbit.core.AmqpGateway;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.MOCK, classes = LicenseServiceApplication.class)
@ActiveProfiles("test")
@AutoConfigureMockMvc
@WithJwt("mock-keycloak.json")
@Import(TestConfig.class)
@WithJsonStorage(keys={"config=tenant"},values={SharedTestConfig.TEST_TENANT})
public abstract class SharedTestConfig {
    @Autowired
    protected MockMvc mockMvc;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected ParticipantService participantService;

    @Autowired
    protected EventTypeService eventTypeService;

    @Autowired
    protected ParticipantStatusRepository participantStatusRepository;

    @Autowired
    protected ParticipantGroupRepository participantGroupRepository;

    @MockBean
    private AmqpGateway amqpGateway;

    private TestUtils testUtils;

    public TestUtils getTestUtils() {
        if (testUtils != null) return testUtils;
        return testUtils = new TestUtils(eventTypeService, participantStatusRepository, participantGroupRepository, mockMvc, objectMapper, amqpGateway);
    }

    public static final String TEST_TENANT = """
            {
              "adminCity": "Schenectady",
              "clerkName": "Test Clerk",
              "adminState": "NY",
              "clerkEmail": "<EMAIL>",
              "clerkTitle": "City Clerk",
              "adminOffice": "Clerkxpress City Clerk",
              "adminStreet": "100 Test Street",
              "adminZipCode": "11111",
              "clerkSignature": null,
              "clerkXpressUrl": null,
              "adminOfficeRoom": "Test Room 100",
              "clerkPhoneNumber": "(*************",
              "cityClerkOfficeName": "Office of the Test Clerk"
            }
    """;

}

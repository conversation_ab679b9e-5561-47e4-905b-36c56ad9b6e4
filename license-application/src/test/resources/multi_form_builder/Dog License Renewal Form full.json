{"name": "test renewal full", "description": "Dog License Renewal Form", "pages": [{"title": "Instructions", "optional": false, "sortOrder": 1, "conditionallyDisplay": [], "sections": [{"title": null, "sortOrder": 1, "conditionallyDisplay": [], "elements": [{"label": null, "description": null, "fieldName": "instructionHeader", "type": "html", "defaultValue": "<h3>Please complete the following steps to renew a dog license with expired documents.</h3>", "sortOrder": 1, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": null, "description": null, "fieldName": "instructionFooter", "type": "html", "defaultValue": "<p>Attention: If you are renewing a dog license for a dog that has been spayed or neutered, you must provide a copy of the spay/neuter certificate from your veterinarian. You can upload the certificate at the end of this form.</p>", "sortOrder": 2, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}]}], "onPageNext": [], "onFormSubmit": []}, {"title": "License Duration", "optional": false, "sortOrder": 2, "conditionallyDisplay": [], "sections": [{"title": "Select License Duration", "sortOrder": 1, "conditionallyDisplay": [], "elements": [{"label": null, "description": null, "fieldName": "durationHeader", "type": "html", "defaultValue": "<h3>Please select the duration of the license.</h3>", "sortOrder": 1, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": null, "description": null, "fieldName": "durationFooter", "type": "html", "defaultValue": "<p>Attention: Maximum of 3 year per dog license unless dogs are purebred.</p>", "sortOrder": 2, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "License Duration", "description": null, "fieldName": "licenseDuration", "type": "select", "defaultValue": null, "sortOrder": 3, "size": "full", "info": null, "google": false, "required": {"value": true, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [{"label": "Select", "value": "", "sortOrder": 1, "description": null, "group": null, "default": true}, {"label": "1 Year", "value": "1", "sortOrder": 2, "description": null, "group": null, "default": false}, {"label": "2 Years", "value": "2", "sortOrder": 3, "description": null, "group": null, "default": false}, {"label": "3 Years", "value": "3", "sortOrder": 4, "description": null, "group": null, "default": false}], "columnOrCustomFieldName": "licenseDuration", "tableName": "license", "argumentTemplate": null, "arguments": []}]}], "onPageNext": [{"api": "/license/license/{entityId}/renew", "method": "POST", "requestSlug": [{"key": "{entityId}", "valueLocation": "queryString", "valueName": "licenseId"}], "queryString": [{"key": "duration", "valueLocation": "form", "valueName": "licenseDuration"}], "body": null, "response": {"success": {"code": 204, "waitUntilComplete": true, "setAllToForm": false, "navigate": "cart", "fields": []}, "error": {"code": [400, 500], "message": "Fail to renew license."}}}], "onFormSubmit": []}, {"title": "Renewal Documents", "optional": false, "sortOrder": 3, "conditionallyDisplay": [], "sections": [{"title": null, "sortOrder": 1, "conditionallyDisplay": [], "elements": [{"label": null, "description": null, "fieldName": "renewalDocHeader", "type": "html", "defaultValue": "<h3>Check the documents that are expired.</h3>", "sortOrder": 1, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": null, "description": null, "fieldName": "renewalD<PERSON><PERSON><PERSON>er", "type": "html", "defaultValue": "<p>Ensure uploaded documents are complete and accurate to avoid delays. Incomplete or incorrect submissions may result in your license being put on hold.</p>", "sortOrder": 2, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Renewal Documents Upload", "description": "Copy of the updated license documents.", "fieldName": "renewalDocumentsUpload", "type": "fileOptions", "defaultValue": null, "sortOrder": 4, "size": "full", "info": null, "google": false, "required": {"value": false, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [{"label": "Select", "value": "", "sortOrder": 1, "description": "Select a file to upload.", "group": null, "default": true}, {"label": "Rabies Certificate", "value": "vaccineRecord", "sortOrder": 2, "description": "Please upload a copy of the dog's rabies certificate.", "group": null, "default": false}, {"label": "Purebred Certificate", "value": "purebredCert", "sortOrder": 3, "description": "Copy of the dog's purebred certificate.", "group": null, "default": false}, {"label": "Service Dog Certificate", "value": "serviceDogCert", "sortOrder": 4, "description": "Copy of the dog's service dog certificate.", "group": null, "default": false}, {"label": "License Exemption Document", "value": "exemptionDocuments", "sortOrder": 5, "description": "Copy of the dog's license exemption document.", "group": null, "default": false}], "columnOrCustomFieldName": "renewalDocumentsUpload", "tableName": "license", "argumentTemplate": null, "arguments": []}]}], "onPageNext": [{"api": "/coordinator/license/multiFormBuilder/newDogLicenseForm/draft/{entityId}", "method": "PATCH", "requestSlug": [{"key": "{entityId}", "valueLocation": "queryString", "valueName": "licenseId"}], "queryString": [], "body": {"type": "form-data", "sendAllFormDataField": true, "fields": []}, "response": {"success": {"code": 204, "waitUntilComplete": false, "setAllToForm": false, "navigate": null, "fields": []}, "error": {"code": [400, 500], "message": "Some error message here for toast"}}}, {"api": "/coordinator/textextractor/bulk", "method": "POST", "requestSlug": [], "queryString": [], "body": {"type": "form-data", "sendAllFormDataField": false, "fields": [{"key": "exemptionDocuments", "valueLocation": "form", "valueName": "exemptionDocuments"}, {"key": "purebredCert", "valueLocation": "form", "valueName": "purebredCert"}, {"key": "serviceDogCert", "valueLocation": "form", "valueName": "serviceDogCert"}, {"key": "vaccineRecord", "valueLocation": "form", "valueName": "vaccineRecord"}]}, "response": {"success": {"code": 200, "waitUntilComplete": true, "setAllToForm": true, "navigate": null, "fields": []}, "error": {"code": [400, 500], "message": "Some error message here for toast"}}}], "onFormSubmit": []}, {"title": "Health Information", "optional": false, "sortOrder": 5, "conditionallyDisplay": [], "sections": [{"title": "Dog Health Information", "sortOrder": 1, "conditionallyDisplay": [], "elements": [{"label": "Vet / Hospital Name", "description": null, "fieldName": "veterinaryName", "type": "text", "defaultValue": null, "sortOrder": 1, "size": "md", "info": null, "google": false, "required": {"value": true, "message": "Please enter a valid name."}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": {"value": "^[A-Za-z\\s'-.,]*$", "message": "Please enter a valid name."}, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": "veterinaryName", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Veterinarian Name", "description": null, "fieldName": "veterinarianName", "type": "text", "defaultValue": null, "sortOrder": 2, "size": "md", "info": null, "google": false, "required": {"value": true, "message": "Please enter a valid name."}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": {"value": "^[A-Za-z\\s'-.,]*$", "message": "Please enter a valid name."}, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": "veterinarianName", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Rabies Tag Number", "description": null, "fieldName": "rabiesTagNumber", "type": "text", "defaultValue": null, "sortOrder": 3, "size": "full", "info": null, "google": false, "required": {"value": true, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": {"value": "^[a-zA-Z0-9\\s]*$", "message": "Please enter a valid rabies tag number."}, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": "rabiesTagNumber", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Vaccine Name", "description": null, "fieldName": "vaccineName", "type": "select", "defaultValue": "rabies", "sortOrder": 4, "size": "md", "info": null, "google": false, "required": {"value": true, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [{"label": "Select", "value": "", "sortOrder": 1, "description": null, "group": null, "default": false}, {"label": "Rabies", "value": "rabies", "sortOrder": 2, "description": null, "group": null, "default": false}], "columnOrCustomFieldName": "vaccineName", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Vaccine Producer", "description": null, "fieldName": "vaccineProducer", "type": "select", "defaultValue": null, "sortOrder": 5, "size": "md", "info": null, "google": false, "required": {"value": true, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [{"label": "Select", "value": "", "sortOrder": 1, "description": null, "group": null, "default": false}, {"label": "<PERSON><PERSON>", "value": "z<PERSON><PERSON>", "sortOrder": 2, "description": null, "group": null, "default": false}, {"label": "Merial, Ltd.", "value": "merial", "sortOrder": 3, "description": null, "group": null, "default": false}, {"label": "Merck Animal Health", "value": "merch animal health", "sortOrder": 4, "description": null, "group": null, "default": false}, {"label": "Boehringer Ingelheim Vetmedica", "value": "boehring ingelheim vetmedica", "sortOrder": 5, "description": null, "group": null, "default": false}], "columnOrCustomFieldName": "vaccineProducer", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Vaccine Brand", "description": null, "fieldName": "vaccineBrand", "type": "select", "defaultValue": null, "sortOrder": 6, "size": "md", "info": null, "google": false, "required": {"value": true, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [{"label": "Select", "value": "", "sortOrder": 1, "description": null, "group": null, "default": false}, {"label": "IMRAB 1", "value": "imrab 1", "sortOrder": 2, "description": null, "group": null, "default": false}, {"label": "IMRAB 1 (TF)", "value": "imrab 1 tf", "sortOrder": 3, "description": null, "group": null, "default": false}, {"label": "IMRAB 3", "value": "imrab 3", "sortOrder": 4, "description": null, "group": null, "default": false}, {"label": "IMRAB 3 (TF)", "value": "imrab 3 tf", "sortOrder": 5, "description": null, "group": null, "default": false}, {"label": "PUREVAX 1", "value": "purevax 1", "sortOrder": 6, "description": null, "group": null, "default": false}, {"label": "PUREVAX 3", "value": "purevax 3", "sortOrder": 7, "description": null, "group": null, "default": false}, {"label": "NOBIVAC 1", "value": "nobivac 1", "sortOrder": 8, "description": null, "group": null, "default": false}, {"label": "NOBIVAC 3 CA", "value": "nobivac 3 ca", "sortOrder": 9, "description": null, "group": null, "default": false}, {"label": "NOBIVAC 3", "value": "nobivac 3", "sortOrder": 10, "description": null, "group": null, "default": false}, {"label": "RABVAC 1", "value": "rabvac 1", "sortOrder": 11, "description": null, "group": null, "default": false}, {"label": "RABVAC 3", "value": "rabvac 3", "sortOrder": 12, "description": null, "group": null, "default": false}, {"label": "DEFENSOR 1", "value": "defensor 1", "sortOrder": 13, "description": null, "group": null, "default": false}, {"label": "DEFENSOR 3", "value": "defensor 3", "sortOrder": 14, "description": null, "group": null, "default": false}], "columnOrCustomFieldName": "vaccineBrand", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Administered Date", "description": null, "fieldName": "vaccineAdministeredDate", "type": "date", "defaultValue": null, "sortOrder": 7, "size": "md", "info": null, "google": false, "required": {"value": true, "message": "Please enter a valid date."}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": "vaccineAdministeredDate", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Vaccine Due Date", "description": null, "fieldName": "vaccineDueDate", "type": "date", "defaultValue": null, "sortOrder": 8, "size": "md", "info": null, "google": false, "required": {"value": true, "message": "Please enter a valid date."}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": "vaccineDueDate", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Lot/Serial Number", "description": null, "fieldName": "vaccineLotNumber", "type": "text", "defaultValue": "", "sortOrder": 9, "size": "md", "info": null, "google": false, "required": {"value": false, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": "vaccineLotNumber", "tableName": "participant", "argumentTemplate": null, "arguments": []}, {"label": "Lot/Serial Expiration", "description": null, "fieldName": "vaccineLotExpirationDate", "type": "date", "defaultValue": null, "sortOrder": 10, "size": "md", "info": null, "google": false, "required": {"value": false, "message": null}, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": "vaccineLotExpirationDate", "tableName": "participant", "argumentTemplate": null, "arguments": []}]}, {"title": null, "sortOrder": 5, "conditionallyDisplay": [], "elements": [{"label": null, "description": null, "fieldName": "serviceDogCert", "type": "fileDisplay", "defaultValue": null, "sortOrder": 1, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}]}], "onPageNext": [{"api": "/coordinator/license/multiFormBuilder/newDogLicenseForm/draft/{entityId}", "method": "PATCH", "requestSlug": [{"key": "{entityId}", "valueLocation": "queryString", "valueName": "licenseId"}], "queryString": [], "body": {"type": "form-data", "sendAllFormDataField": true, "fields": []}, "response": {"success": {"code": 204, "waitUntilComplete": false, "setAllToForm": false, "navigate": null, "fields": []}, "error": {"code": [400, 500], "message": "Some error message here for toast"}}}], "onFormSubmit": []}, {"title": "Confirm Information", "optional": false, "sortOrder": 6, "conditionallyDisplay": [], "sections": [{"title": "License Duration", "sortOrder": 1, "conditionallyDisplay": [], "elements": [{"label": "License Duration", "description": null, "fieldName": "licenseDuration", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 1, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}]}, {"title": "Health Information", "sortOrder": 2, "conditionallyDisplay": [], "elements": [{"label": "Vet / Hospital Name", "description": null, "fieldName": "veterinaryName", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 1, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Veterinarian Name", "description": null, "fieldName": "veterinarianName", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 2, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Rabies Tag Number", "description": null, "fieldName": "rabiesTagNumber", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 3, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Vaccine Name", "description": null, "fieldName": "vaccineName", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 4, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Vaccine Producer", "description": null, "fieldName": "vacineProducer", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 5, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Administered Date", "description": null, "fieldName": "vaccineAdministeredDate", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 6, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Vaccine Due Date", "description": null, "fieldName": "vaccineDueDate", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 7, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Lot/Serial Number", "description": null, "fieldName": "vaccineLotNumber", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 8, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}, {"label": "Lot/Serial Expiration", "description": null, "fieldName": "vaccineLotExpirationDate", "type": "fieldDisplay", "defaultValue": null, "sortOrder": 9, "size": "full", "info": null, "google": false, "required": null, "minLength": null, "minValue": null, "maxLength": null, "maxValue": null, "pattern": null, "conditionallyDisplay": [], "triggers": [], "options": [], "columnOrCustomFieldName": null, "tableName": null, "argumentTemplate": null, "arguments": []}]}], "onPageNext": [], "onFormSubmit": []}], "requiredFields": [{"key": "{entityId}", "valueLocation": "queryString", "valueName": "licenseId"}]}
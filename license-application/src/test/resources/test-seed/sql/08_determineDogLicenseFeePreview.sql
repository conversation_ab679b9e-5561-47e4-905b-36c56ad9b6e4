drop FUNCTION if exists license.determineDogLicenseFeePreview;
CREATE OR REPLACE FUNCTION license.determineDogLicenseFeePreview(
    in i_license_entity_id uuid
)
RETURNS TABLE (
    fee_type text,
    fee_code text,
    fee_amount NUMERIC(20, 10),
    label text,
    duration int
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    select
        'MANUAL',
        'DOG_LICENSE_FEE',
        10.00,
        'Dog License Fee',
        1;
END $$;

update license_type lt
set fee_preview_function = 'determineDogLicenseFeePreview'
where lt.code = 'dogLicense';
DROP VIEW IF EXISTS view_pivoted_license;
CREATE OR REPLACE VIEW view_pivoted_license AS
WITH PivotCTE AS (
    SELECT
        l.license_id,
        l.license_uuid as entity_id,
        l.license_number,
        l.properties,
        l.modifier,
        l.approved,
        TO_CHAR(l.approved_date AT TIME ZONE 'America/New_York', 'MM/DD/YYYY') as approved_date,
        l.approved_by,
        l.denied_comment,
        TO_CHAR(l.valid_from_date, 'MM/DD/YYYY') as valid_from_date,
        TO_CHAR(l.valid_to_date, 'MM/DD/YYYY') as valid_to_date,
		TO_CHAR(l.issued_date AT TIME ZONE 'America/New_York', 'MM/DD/YYYY') as licenseIssuedDate,
        ls.name AS license_status,
        ls.code AS license_status_code,
        lt.name AS license_type_name,
        lt.code AS license_type_code,
        lt.group_name AS license_type_group,
		dog.*,
		individual.*,
		fee.*,
		tenant.*,
		activity.*,
		spayNeuterCount.*

    FROM license l
    JOIN license_status ls
		ON ls.license_status_id = l.license_status_id
    JOIN license_type lt
		ON lt.license_type_id = l.license_type_id

	LEFT JOIN LATERAL (
		select
			upper(la.activity_type) as activity_type,
			TO_CHAR(la.created_date, 'MM/DD/YYYY') as activity_date
		from license_activity la

		where la.license_id = l.license_id
		order by la.valid_from_date desc
		limit 1
	) AS activity ON true

	LEFT JOIN LATERAL (
		select
			round(sum(case when lf.fee_code like '%-S-%' then lf.amount end)::numeric, 2)::TEXT as state_fee,
			round(sum(case when lf.fee_code like '%-M-%' then lf.amount end)::numeric, 2)::TEXT as city_fee,
			round(sum(case when lf.fee_code like '%PEN-%' or lf.fee_code like '%LATE-%' then lf.amount end)::numeric, 2)::TEXT as penalty_fee,
			round(sum(case when lf.fee_code like '%PR-%' then lf.amount end)::numeric, 2)::TEXT as lic_pro_fee,
			round(sum(case when lf.fee_code like '%PAY-%' then lf.amount end)::numeric, 2)::TEXT as pmt_pro_fee,
			round(sum(case when lf.amount < 0 then lf.amount end)::numeric, 2)::TEXT as discount_amount,
			round(sum(lf.amount)::numeric, 2)::TEXT as total_fees,
			max(case when lf.fee_code like '%SENIOR%' then '*Senior Discount' end) as discount
		from license_activity la

		left join license_activity_fee lf
			on lf.license_activity_id = la.license_activity_id

		left join lateral(
			Select
				ila.license_activity_id,
				DENSE_RANK() OVER (ORDER BY ila.last_modified_date::date desc) AS row_number
			from license_activity ila
			where ila.license_id = l.license_id
		) as act on true
		and act.license_activity_id = la.license_activity_id

		where la.license_id = l.license_id
		and act.row_number = 1
	) AS fee ON true

	--participant
	LEFT JOIN LATERAL(
		SELECT
			pIndividual.entity_id as ownerEntityId,
			pIndividual.properties as ownerProperties,
			p.opt_ins as optins,
			p.registered as registered,
			case when mailAddr.address_id is not null then mailAddr.entity_id else homeAddr.entity_id end as ownerAddressEntityId,
			case when mailAddr.address_id is not null then mailAddr.city else homeAddr.city end as ownerCity,
			case when mailAddr.address_id is not null then mailAddr.latitude else homeAddr.latitude end as ownerLatitude,
			case when mailAddr.address_id is not null then mailAddr.longitude else homeAddr.longitude end as ownerLongitude,
			case when mailAddr.address_id is not null then mailAddr.state else homeAddr.state end as ownerState,
			case when mailAddr.address_id is not null then mailAddr.street_address else homeAddr.street_address end as ownerAddress,
			case when mailAddr.address_id is not null then mailAddr.street_address_2 else homeAddr.street_address_2 end as ownerAddress2,
			case when mailAddr.address_id is not null then mailAddr.zip else homeAddr.zip end as ownerZip,
			case when mailAddr.address_id is not null then mailAddr.full_address else homeAddr.full_address end as ownerFullAddress,
			case when mailAddr.address_id is not null then mailAddr.house_number else homeAddr.house_number end as ownerHouseNo,
			case when mailAddr.address_id is not null then mailAddr.road else homeAddr.road end as ownerRoad,
			case when mailAddr.address_id is not null then mailAddr.town else homeAddr.town end as ownerTown,
			case when mailAddr.address_id is not null then mailAddr.address_type else homeAddr.address_type end as addressType,
			cEmail.contact_value as ownerPrimaryEmail,
			cPhone.contact_value as ownerPrimaryPhone
		FROM association aIndividual

		LEFT JOIN view_participant pIndividual
			on pIndividual.participant_id = aIndividual.child_id

		left join participant p
            on p.participant_id = pIndividual.participant_id

        left join view_participant_address homeAddr
            on homeAddr.participant_id = pIndividual.participant_id
            and homeAddr.address_type ILIKE 'Home'

        left join view_participant_address mailAddr
            on mailAddr.participant_id = pIndividual.participant_id
            and mailAddr.address_type ILIKE 'Mailing'

		left join view_contact cEmail
			on cEmail.participant_id = pIndividual.participant_id
			and cEmail.group_name = 'Email'
			and cEmail.type_name = 'Primary'
		left join view_contact cPhone
			on cPhone.participant_id = pIndividual.participant_id
			and cPhone.group_name = 'Phone'
			and cPhone.type_name = 'Home'

        WHERE aIndividual.parent_association_type = 'LICENSE'
        AND aIndividual.child_association_type = 'PARTICIPANT'
        AND aIndividual.parent_id = l.license_id
		and pIndividual.group_name = 'Individual'
	) AS individual ON true


	--dogs
	LEFT JOIN LATERAL(
		SELECT
			pdog.entity_id as dogEntityId,
			pdog.properties as dogProperties
		FROM association aDog

		LEFT JOIN view_participant pdog
			on pdog.participant_id = aDog.child_id

        WHERE aDog.parent_association_type = 'LICENSE'
        AND aDog.child_association_type = 'PARTICIPANT'
        AND aDog.parent_id = l.license_id
		and pdog.group_name = 'Dog'
	) AS dog ON true

    --spay neuter text
    LEFT JOIN LATERAL(
        SELECT
            sum(1) as spayNeuterCount
        FROM association aDog

        LEFT JOIN view_participant pdog
            on pdog.participant_id = aDog.child_id

        WHERE aDog.parent_association_type = 'LICENSE'
        AND aDog.child_association_type = 'PARTICIPANT'
        AND aDog.parent_id = l.license_id
        and pdog.group_name = 'Dog'
        and pdog.properties->>'dogSpayedOrNeutered' = 'yes'
    ) AS spayNeuterCount ON true

	--clerk-admin info
	LEFT JOIN LATERAL(
		select
			admin_city,
			admin_office,
			admin_office_room,
			admin_state,
			admin_street,
			admin_zip_code,
			city_clerk_office_name,
			clerk_email,
			clerk_name,
			clerk_phone_number,
			clerk_title,
			clerk_xpress_url
		from tenant t
		limit 1
	) AS tenant ON true

)
select
	*,
    CASE
        WHEN license_status = 'Canceled' THEN 'Canceled'
        WHEN activity_type = 'ADD_PUREBRED_DOG' THEN 'Additional Dog'
        ELSE activity_type
    END || CASE
        WHEN modifier IS NOT NULL AND TRIM(modifier) <> '' THEN ' - ' || modifier
        ELSE ''
    END AS form_status_display,
    CASE
        WHEN "dog.isDangerous" = 'true' THEN 'Yes'
        ELSE 'No'
    END as is_dangerous_text,
	case
		when coalesce(nullif(trim("dog.dogSecondaryColor"),''),'') <> '' then "dog.dogPrimaryColor" || '/' || "dog.dogSecondaryColor"
		else "dog.dogPrimaryColor"
	end as "dog.dogFormattedColor",
	remove_any_extra_spaces_and_trim(
	    left("owner.firstName" || ' ' || "owner.lastName", 30)
	) as "owner.fullName",
	remove_any_extra_spaces_and_trim(
	    "ownerAddress.address"
	) as "ownerAddress.fullAddressPrimary",
	remove_any_extra_spaces_and_trim(
	    "ownerAddress.city" || ', ' || "ownerAddress.state" || ' ' || "ownerAddress.zip"
	) as "ownerAddress.fullAddressSecondary",
	remove_any_extra_spaces_and_trim(
        "municipality.city" || ', ' || "municipality.state" || ' ' || "municipality.zipCode"
    ) as "municipality.fullAddressSecondary",
    coalesce("approved_date", "licenseIssuedDate") as "signatureDate"
from (
	SELECT
    p.license_id,
    p.entity_id,
    p.license_number,
    p.valid_from_date,
    p.valid_to_date,
    p.licenseIssuedDate as "licenseIssuedDate",
    p.license_status,
    p.license_status_code,
    p.modifier,
    p.approved,
    p.approved_date,
    p.denied_comment,
    p.license_type_name,
    p.license_type_code,
    p.license_type_group,
    MAX(p.properties->>'licenseDuration') AS "licenseDuration",

    -- vaccine
    MAX(TO_CHAR(CAST(p.dogProperties->>'vaccineDueDate' AS DATE), 'MM/DD/YYYY') ) AS "vaccineDueDate",
    MAX(p.dogProperties->>'rabiesTagNumber') AS "rabiesTagNumber",
    MAX(p.dogProperties->>'veterinarianName') AS "veterinarianName",
    MAX(p.dogProperties->>'vaccineProducer') AS "vaccineProducer",
    MAX(TO_CHAR(CAST(p.dogProperties->>'vaccineAdministeredDate' AS DATE), 'MM/DD/YYYY') ) AS "vaccineAdministeredDate",
    MAX(p.dogProperties->>'vaccineLotNumber') AS "vaccineLotNumber",
    MAX(p.dogProperties->>'veterinaryName') AS "veterinaryName",
    MAX(p.dogProperties->>'vaccineName') AS "vaccineName",
    MAX(p.dogProperties->>'vaccineDatesExempt') AS "vaccineDatesExempt",
    MAX(p.dogProperties->>'licenseExempt') AS "dog.licenseExempt",

    MAX(TO_CHAR(CAST(p.dogProperties->>'vaccineDueDate' AS DATE), 'MM/DD/YYYY') ) AS "dog.vaccineDueDate",
    MAX(p.dogProperties->>'rabiesTagNumber') AS "dog.rabiesTagNumber",
    MAX(p.dogProperties->>'veterinarianName') AS "dog.veterinarianName",
    MAX(p.dogProperties->>'vaccineProducer') AS "dog.vaccineProducer",
    MAX(TO_CHAR(CAST(p.dogProperties->>'vaccineAdministeredDate' AS DATE), 'MM/DD/YYYY') ) AS "dog.vaccineAdministeredDate",
    MAX(p.dogProperties->>'vaccineLotNumber') AS "dog.vaccineLotNumber",
    MAX(p.dogProperties->>'veterinaryName') AS "dog.veterinaryName",
    MAX(p.dogProperties->>'vaccineName') AS "dog.vaccineName",
    MAX(p.dogProperties->>'vaccineDatesExempt') AS "dog.vaccineDatesExempt",

    CASE
        WHEN EXTRACT(MONTH FROM AGE(
                                    CAST(MAX(p.dogProperties->>'vaccineDueDate') AS DATE)  + interval '1 day',
                                    CAST(MAX(p.dogProperties->>'vaccineAdministeredDate') AS DATE)
                )) >= 6
                THEN EXTRACT(YEAR FROM AGE(
                            CAST(MAX(p.dogProperties->>'vaccineDueDate') AS DATE)  + interval '1 day',
                            CAST(MAX(p.dogProperties->>'vaccineAdministeredDate') AS DATE)
                      )) + 1
        ELSE EXTRACT(YEAR FROM AGE(
                               		CAST(MAX(p.dogProperties->>'vaccineDueDate') AS DATE)  + interval '1 day',
                               		CAST(MAX(p.dogProperties->>'vaccineAdministeredDate') AS DATE)
                               	))
    END AS "dog.vaccinePeriod",

	--dogs
	MAX(p.dogProperties->>'dogName') AS "dog.dogName",
    MAX(p.dogProperties->>'microchipNumber') AS "dog.microchipNumber",
    MAX(p.dogProperties->>'tagNumber') AS "dog.tagNumber",
    MAX(p.dogProperties->>'dogSpayedOrNeutered') AS "dog.dogSpayedOrNeutered",
    MAX(p.dogProperties->>'dogSex') AS "dog.dogSex",
    MAX(p.dogProperties->>'dogPrimaryColor') AS "dog.dogPrimaryColor",
    MAX(p.dogProperties->>'dogSecondaryColor') AS "dog.dogSecondaryColor",
    MAX(p.dogProperties->>'dogBreed') AS "dog.dogBreed",
    MAX(TO_CHAR(CAST(p.dogProperties->>'dogBirthDate' AS DATE), 'MM/DD/YYYY') ) AS "dog.dogBirthDate",
    MAX(CAST(DATE_PART('year', CAST(p.dogProperties->>'dogBirthDate' AS DATE)) AS INTEGER) ) AS "dog.dogBirthYear",
    MAX(p.dogProperties->>'dogMarkings') AS "dog.dogMarkings",
    MAX(p.dogProperties->>'dogBio') AS "dog.dogBio",
    MAX(p.dogProperties->>'dogFriendly') AS "dog.dogFriendly",
    MAX(p.dogProperties->>'catFriendly') AS "dog.catFriendly",
    MAX(p.dogProperties->>'childFriendly') AS "dog.childFriendly",
    MAX(p.dogProperties->>'isDangerous') AS "dog.isDangerous",

	--owners
	MAX(p.ownerEntityId::text) AS "owner.entityId",
	CASE
      WHEN EXISTS (
        SELECT 1
        FROM jsonb_array_elements(p.optins) AS elem
        WHERE elem->>'name' = 'optInPaperless'
        AND elem->>'active' = 'true'
      ) THEN true
      ELSE false
    END AS "owner.paperless",
	MAX(p.ownerProperties->>'firstName') AS "owner.firstName",
    MAX(p.ownerProperties->>'lastName') AS "owner.lastName",
    MAX(TO_CHAR(CAST(p.ownerProperties->>'dateOfBirth' AS DATE), 'MM/DD/YYYY')) AS "owner.dateOfBirth",
    MAX(p.ownerProperties->>'registrationCode') as "owner.registrationCode",
    p.registered as "owner.registered",
    MAX(left(case when p.registered then (p.ownerProperties->>'firstName') || ' ' || (p.ownerProperties->>'lastName') else '' end, 30)) as "owner.signature",
    MAX(case when p.registered then ' - processed online' else '' end) as "owner.signature2",
	--owner address
	MAX(p.ownerCity) AS "ownerAddress.city",
	MAX(p.ownerLatitude) AS "ownerAddress.latitude",
	MAX(p.ownerLongitude) AS "ownerAddress.longitude",
	MAX(p.ownerState) AS "ownerAddress.state",
	MAX(p.ownerAddress) AS "ownerAddress.address",
	MAX(p.ownerAddress2) AS "ownerAddress.address2",
	MAX(p.ownerZip) AS "ownerAddress.zip",
	MAX(p.ownerFullAddress) AS "ownerAddress.fullAddress",
	MAX(p.ownerHouseNo) AS "ownerAddress.houseNo",
	MAX(p.ownerRoad) AS "ownerAddress.road",
	MAX(p.ownerTown) AS "ownerAddress.town",
	MAX(p.addressType) AS "ownerAddress.type",
	--owner phone
	MAX(
        '(' || SUBSTRING(p.ownerPrimaryPhone, 1, 3) || ') ' ||
          SUBSTRING(p.ownerPrimaryPhone, 4, 3) || '-' ||
          SUBSTRING(p.ownerPrimaryPhone, 7, 4)
	) as "ownerPhone.phone",
	--owner email
	MAX(p.ownerPrimaryEmail) as "ownerEmail.email",

	--clerks
	MAX(coalesce(nullif(p.approved_by,''), clerk_name)) as "clerk.clerkName",
	MAX(clerk_email) as "clerk.clerkEmail",
	MAX(clerk_title) as "clerk.clerkTitle",
	MAX(clerk_phone_number) as "clerk.clerkPhoneNumber",
	MAX(clerk_name) as "clerk.clerkSignature",
	MAX(city_clerk_office_name) as "clerk.clerkOfficeName",
	MAX(clerk_xpress_url) as "clerk.clerkXpressUrl",

	--city info
	MAX(admin_office) as "municipality.building",
	MAX(admin_street) as "municipality.address",
	MAX(admin_office_room) as "municipality.roomNumber",
	MAX(admin_city) as "municipality.city",
	MAX(admin_state) as "municipality.state",
	MAX(admin_zip_code) as "municipality.zipCode",
	max(clerk_phone_number) as "municipality.cityPhoneNumber",

	--fees
	coalesce(p.city_fee, '0.00') as city_fee,
	coalesce(p.state_fee, '0.00') as state_fee,
	coalesce(p.total_fees, '0.00') as total_fees,
	coalesce(p.penalty_fee, '0.00') as penalty_fee,
	coalesce(p.lic_pro_fee, '0.00') as lic_pro_fee,
	coalesce(p.pmt_pro_fee, '0.00') as pmt_pro_fee,
	coalesce(p.discount_amount, '0.00') as discount_amount,
	p.discount as "seniorDiscountStatusText",
	coalesce(p.activity_type, 'NEW') as activity_type,
	p.activity_date,
	MAX(
		CASE
			WHEN coalesce(p.spayNeuterCount, 0) > 0 THEN '*Spayed or Neuter'
		END
	) AS "spayNeuterStatusText"

FROM PivotCTE p
GROUP BY
    p.license_id,
    p.entity_id,
    p.license_number,
    p.optins,
	p.registered,
    p.valid_from_date,
    p.valid_to_date,
	p.licenseIssuedDate,
    p.license_status,
    p.modifier,
    p.approved,
    p.approved_date,
    p.denied_comment,
    p.license_status_code,
    p.license_type_name,
    p.license_type_code,
    p.license_type_group,
	p.ownerEntityId,
	p.dogEntityid,
	p.ownerAddressEntityId,
	p.city_fee,
	p.state_fee,
	p.total_fees,
	p.penalty_fee,
	p.lic_pro_fee,
	p.pmt_pro_fee,
	p.discount,
	p.discount_amount,
	p.activity_type,
	p.activity_date
) as a;

--drop view view_pivoted_license

--SELECT * from view_pivoted_license where entity_id = '6eb25d5a-e715-4e3f-b3a8-88fbbee343c0'
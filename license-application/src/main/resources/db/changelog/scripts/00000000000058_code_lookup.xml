<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="David" id="createTable_codeLookup">
        <createTable tableName="code_lookup">
            <column autoIncrement="true" name="code_lookup_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="code_lookup_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="code_lookup_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>

            <column name="code" type="VARCHAR(30)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="entity_type" type="VARCHAR(255)"/>
            <column name="entity_id" type="VARCHAR(255)"/>
            <column name="realm" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="David" id="createTable_auditLogCodeLookup2">
        <createTable tableName="audit_log_code_lookup">
            <column name="code_lookup_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_code_lookup_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_code_lookup_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="code_lookup_uuid" type="UUID"/>

            <column name="code" type="VARCHAR(30)"/>
            <column name="entity_type" type="VARCHAR(255)"/>
            <column name="entity_id" type="VARCHAR(255)"/>
            <column name="realm" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="David" id="createIndex_code_lookup_code">
        <createIndex indexName="idx_code_lookup_code" tableName="code_lookup">
            <column name="code"/>
        </createIndex>
    </changeSet>
    <changeSet author="David" id="createIndex_code_lookup_entity_type_and_entity_id">
        <createIndex indexName="idx_code_lookup_entity_type_and_entity_id" tableName="code_lookup">
            <column name="entity_type"/>
            <column name="entity_id"/>
        </createIndex>
    </changeSet>
    <!--    add action column-->
    <changeSet author="David" id="addColumn_action_code_lookup">
        <addColumn tableName="code_lookup">
            <column name="action" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="addColumn_action_audit_log_code_lookup">
        <addColumn tableName="audit_log_code_lookup">
            <column name="action" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet author="David" id="idx_code_lookup_entity_type_and_entity_id_and_action">
        <createIndex indexName="idx_code_lookup_entity_type_and_entity_id_and_action" tableName="code_lookup">
            <column name="entity_type"/>
            <column name="entity_id"/>
            <column name="action"/>
        </createIndex>
    </changeSet>
    <changeSet id="update_registrationCodeProperty_participant" author="David">
        <sql>
            UPDATE participant
            set properties = properties || jsonb_build_object('registrationCode', registration_code)
            where COALESCE(registration_code, '') != '';
        </sql>
    </changeSet>
    <changeSet id="insert_participantRegistrationCode_code_lookup" author="David">
        <sql>
            INSERT INTO code_lookup(created_by, created_date, last_modified_by, last_modified_date, code, entity_type,
            entity_id, realm, "action")
            select
            'seed',
            now(),
            'seed',
            now(),
            registration_code,
            'individual',
            participant_uuid,
            'schenectady',
            'REGISTER'
            from participant p
            where COALESCE(p.registration_code, '') != ''
            and not exists(
            select null from code_lookup cl
            where cl.code = p.registration_code
            and cl.entity_id = p.participant_uuid::text
            and cl.entity_type = 'individual'
            );
        </sql>
    </changeSet>
    <changeSet id="dropView_view_license_license2" author="David">
        <sql>
            DROP VIEW IF EXISTS view_pivoted_license;
        </sql>
    </changeSet>
    <changeSet id="dropTrigger_update_registration_code_trigger" author="David">
        <sql>
            DROP TRIGGER IF EXISTS update_registration_code_trigger ON participant;
        </sql>
    </changeSet>
    <changeSet id="dropFunction_updateRegistrationCode" author="David">
        <sql>
            DROP FUNCTION IF EXISTS update_registration_code();
        </sql>
    </changeSet>
    <changeSet id="dropFunction_generate_unique_code" author="David">
        <sql>
            DROP FUNCTION IF EXISTS generate_unique_code();
        </sql>
    </changeSet>
    <changeSet id="dropFunction_randomize" author="David">
        <sql>
            DROP FUNCTION IF EXISTS randomize();
        </sql>
    </changeSet>
    <changeSet id="drop_registrationCode_participant" author="David">
        <dropColumn tableName="participant" columnName="registration_code"/>
    </changeSet>
    <changeSet id="drop_registrationCode_audit_log_participant" author="David">
        <dropColumn tableName="audit_log_participant" columnName="registration_code"/>
    </changeSet>
    <changeSet id="createTable_code_lookup_bk" author="David">
        <preConditions onFail="MARK_RAN">
            <!-- Ensure code_lookup exists -->
            <tableExists schemaName="license" tableName="code_lookup"/>

            <!-- Ensure code_lookup_bk does NOT exist -->
            <not>
                <tableExists schemaName="license" tableName="code_lookup_bk"/>
            </not>
        </preConditions>

        <sql>
            SELECT *
            INTO license.code_lookup_bk
            FROM license.code_lookup;
        </sql>
    </changeSet>
    <changeSet id="dropTable_code_lookup_onAllDb_except_postgres" author="David">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT CASE WHEN current_database() = 'postgres' THEN 1 ELSE 0 END
            </sqlCheck>
        </preConditions>
        <dropTable tableName="code_lookup"/>
    </changeSet>
    <changeSet id="dropTable_audit_log_code_lookup_onAllDb_except_postgres" author="David">
        <preConditions onFail="MARK_RAN">
            <sqlCheck expectedResult="0">
                SELECT CASE WHEN current_database() = 'postgres' THEN 1 ELSE 0 END
            </sqlCheck>
        </preConditions>
        <dropTable tableName="audit_log_code_lookup"/>
    </changeSet>
</databaseChangeLog>
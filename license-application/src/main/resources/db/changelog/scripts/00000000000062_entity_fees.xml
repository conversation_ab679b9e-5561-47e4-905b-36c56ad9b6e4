<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="dropConstraints" author="David">
        <dropUniqueConstraint tableName="participant_fee" constraintName="uk_tlb7tnmam8gq0hu1crk9btuiu"/>
        <dropAllForeignKeyConstraints baseTableName="participant_fee"/>
        <dropAllForeignKeyConstraints baseTableName="audit_log_entity_fee"/>
    </changeSet>
    <changeSet id="dropTable_audit_log_participant_fee" author="David">
        <dropTable tableName="audit_log_participant_fee"/>
    </changeSet>
    <changeSet id="dropTable_participant_fee" author="David">
        <dropTable tableName="participant_fee"/>
    </changeSet>
    <changeSet id="createTable_entity_fee" author="David">
        <createTable tableName="entity_fee">
            <column autoIncrement="true" name="entity_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="entity_fee_pkey"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="entity_fee_uuid" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="amount" type="numeric(38, 2)"/>
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="order_id" type="UUID"/>
            <column name="paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="payment_status" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="events" type="jsonb"/>
            <column name="comment" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet id="createTable_audit_log_entity_fee" author="">
        <createTable tableName="audit_log_entity_fee">
            <column name="entity_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_fee_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_entity_fee_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="properties" type="JSONB"/>
            <column defaultValueComputed="gen_random_uuid()" name="entity_fee_uuid" type="UUID"/>
            <column name="amount" type="numeric(38, 2)"/>
            <column name="fee_code" type="VARCHAR(255)"/>
            <column name="order_id" type="UUID"/>
            <column name="paid_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="payment_status" type="VARCHAR(255)"/>
            <column name="events" type="jsonb"/>
            <column name="comment" type="TEXT"/>
        </createTable>
    </changeSet>

    <changeSet id="alterEventType_removeNotNullConstraintFrom_profile_type_id" author="David">
        <sql>
            ALTER TABLE event_type ALTER COLUMN profile_type_id DROP NOT NULL;
        </sql>
    </changeSet>

    <!--  @formatter:off   -->
    <changeSet id="addEventType_entity_fee" author="David">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'entityFeeAdded', 'Entity Fee Added', 'Entity Fee Added')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'entityFeeRemoved', 'Entity Fee Removed', 'Entity Fee Removed')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'entityFeeAmountChanged', 'Entity Fee Amount Changed', 'Entity Fee Amount Changed')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'entityFeeCommentChanged', 'Entity Fee Comment Changed', 'Entity Fee Comment Changed')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'entityFeeFeeCodeChanged', 'Entity Fee Type Changed', 'Entity Fee Type Changed')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description, name)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'entityFeeMarkedAsPaid', 'Entity Fee Paid', 'Entity Fee Paid')
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
</databaseChangeLog>
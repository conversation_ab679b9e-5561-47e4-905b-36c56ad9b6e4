<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="additionalStatuses" author="David">
        <sql>
            INSERT INTO participant_status (created_by, created_date, last_modified_by, last_modified_date, name, code,
            participant_group_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'Found', 'ACTIVE',
            (select participant_group_id from participant_group where name = 'Dog'));
        </sql>
    </changeSet>
</databaseChangeLog>
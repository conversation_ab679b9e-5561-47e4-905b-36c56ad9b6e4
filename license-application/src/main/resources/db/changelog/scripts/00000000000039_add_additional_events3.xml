<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="insertParticipantAndLicenseChangedEventTypes" author="David">
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'nameChanged', 'Name Changed', 'Name Changed',
            (select profile_type_id from profile_type where name = 'individual'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dateOfBirthChanged', 'DOB Changed', 'DOB Changed',
            (select profile_type_id from profile_type where name = 'individual'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'contactChanged', 'Contact Changed', 'Contact Changed',
            (select profile_type_id from profile_type where name = 'individual'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'individualChanged', 'Individual Changed', 'Individual Changed',
            (select profile_type_id from profile_type where name = 'individual'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'dogChanged', 'Dog Changed', 'Dog Changed',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'vaccineChanged', 'Vaccine Changed', 'Vaccine Changed',
            (select profile_type_id from profile_type where name = 'dog'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
        <sql>
            INSERT INTO event_type (created_by, created_date, last_modified_by, last_modified_date, code, description,
            name, profile_type_id)
            values ( 'SYSTEM', current_timestamp, 'SYSTEM', current_timestamp, 'licenseChanged', 'License Changed', 'License Changed',
            (select profile_type_id from profile_type where name = 'license'))
            ON CONFLICT ON CONSTRAINT uk_event_type_code DO NOTHING;
        </sql>
    </changeSet>
</databaseChangeLog>

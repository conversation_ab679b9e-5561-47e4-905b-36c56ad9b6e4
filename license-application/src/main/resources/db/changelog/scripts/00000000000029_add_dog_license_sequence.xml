<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="createLicenseNumberSequence" author="Ben">
        <createSequence sequenceName="license_number_seq"/>
    </changeSet>
    <changeSet id="createPurebredLicenseNumberSequence" author="David">
        <createSequence sequenceName="purebred_license_number_seq"/>
    </changeSet>
    <changeSet id="createPendingLicenseNumberSequence" author="Ben">
        <createSequence sequenceName="pending_license_number_seq"/>
    </changeSet>
    <changeSet id="createPendingPurebredLicenseNumberSequence" author="David">
        <createSequence sequenceName="pending_purebred_license_number_seq"/>
    </changeSet>
    <changeSet id="license_number_correction" author="David">
        <sql>
            update license
            set license_number = date_part('year', created_date)||license_number
            where license_number ILIKE 'PENDDL%';

            update license
            set license_number = date_part('year', created_date)||license_number
            where license_number ILIKE 'DL%';
        </sql>
    </changeSet>
</databaseChangeLog>
<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addRegisteredColumnToParticipant" author="Ben">
        <addColumn tableName="participant">
            <column name="registered" type="boolean" defaultValue="false"/>
        </addColumn>
        <sql>
            UPDATE participant
            SET registered = false;
        </sql>
        <addNotNullConstraint tableName="participant"
                              columnName="registered"
                              columnDataType="boolean"/>
    </changeSet>
    <changeSet id="addRegisteredColumntoAuditLogParticipant" author="Ben">
        <addColumn tableName="audit_log_participant">
            <column name="registered" type="boolean" defaultValue="false"/>
        </addColumn>
    </changeSet>
</databaseChangeLog>

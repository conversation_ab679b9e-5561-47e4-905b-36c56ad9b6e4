<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="jira" id="cts-01">
        <sql splitStatements="false">
            insert into contact(created_by, created_date, last_modified_by, last_modified_date,
            contact_type_group_id, contact_value, participant_id, conversion_reference)
            select
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            1,
            '',
            p.participant_id,
            p.conversion_reference
            from participant p
            where coalesce(conversion_reference,'') != ''
            and participant_type_group_id = 1
            and not exists(
            select * from contact c
            where c.participant_id = p.participant_id
            and contact_type_group_id = 1
            )
            union all
            select
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            2,
            '',
            p.participant_id,
            p.conversion_reference
            from participant p
            where coalesce(conversion_reference,'') != ''
            and participant_type_group_id = 1
            and not exists(
            select * from contact c
            where c.participant_id = p.participant_id
            and contact_type_group_id = 2
            )
            union all
            select
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            4,
            '',
            p.participant_id,
            p.conversion_reference
            from participant p
            where coalesce(conversion_reference,'') != ''
            and participant_type_group_id = 1
            and not exists(
            select * from contact c
            where c.participant_id = p.participant_id
            and contact_type_group_id = 4
            )
            union all
            select
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            5,
            '',
            p.participant_id,
            p.conversion_reference
            from participant p
            where coalesce(conversion_reference,'') != ''
            and participant_type_group_id = 1
            and not exists(
            select * from contact c
            where c.participant_id = p.participant_id
            and contact_type_group_id = 5
            )
            union all
            select
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            6,
            '',
            p.participant_id,
            p.conversion_reference
            from participant p
            where coalesce(conversion_reference,'') != ''
            and participant_type_group_id = 1
            and not exists(
            select * from contact c
            where c.participant_id = p.participant_id
            and contact_type_group_id = 6
            )
            union all
            select
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            7,
            '',
            p.participant_id,
            p.conversion_reference
            from participant p
            where coalesce(conversion_reference,'') != ''
            and participant_type_group_id = 1
            and not exists(
            select * from contact c
            where c.participant_id = p.participant_id
            and contact_type_group_id = 7
            )
            union all
            select
            created_by,
            created_date,
            last_modified_by,
            last_modified_date,
            8,
            '',
            p.participant_id,
            p.conversion_reference
            from participant p
            where coalesce(conversion_reference,'') != ''
            and participant_type_group_id = 1
            and not exists(
            select * from contact c
            where c.participant_id = p.participant_id
            and contact_type_group_id = 8
            );
        </sql>
    </changeSet>
</databaseChangeLog>
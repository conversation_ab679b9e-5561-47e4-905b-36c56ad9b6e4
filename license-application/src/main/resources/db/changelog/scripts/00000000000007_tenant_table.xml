<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">
    <changeSet author="Ben (generated)" id="1694096241884-4">
        <createTable tableName="tenant">
            <column autoIncrement="true" name="tenant_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="tenant_pkey"/>
            </column>
            <column name="created_by" type="VARCHAR(250)"/>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="entity_id" type="VARCHAR(36)">
                <constraints nullable="false"/>
            </column>
            <column name="admin_city" type="VARCHAR(255)"/>
            <column name="admin_office" type="VARCHAR(255)"/>
            <column name="admin_office_room" type="VARCHAR(255)"/>
            <column name="admin_state" type="VARCHAR(255)"/>
            <column name="admin_street" type="VARCHAR(500)"/>
            <column name="admin_zip_code" type="VARCHAR(255)"/>
            <column name="city_clerk_office_name" type="VARCHAR(255)"/>
            <column name="clerk_email" type="VARCHAR(255)"/>
            <column name="clerk_name" type="VARCHAR(255)"/>
            <column name="clerk_phone_number" type="VARCHAR(255)"/>
            <column name="clerk_signature" type="VARCHAR(255)"/>
            <column name="clerk_title" type="VARCHAR(255)"/>
            <column name="template_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Ben (generated)" id="1694096241884-5">
        <addUniqueConstraint columnNames="template_key" constraintName="uk_5cfh31wow2n2filsivf9txk7y"
                             tableName="tenant"/>
    </changeSet>
    <changeSet author="Ben (generated)" id="1694096241884-6">
        <addUniqueConstraint columnNames="entity_id" constraintName="uk_fjsjdxn80vcxic3wyq5cwpfy6" tableName="tenant"/>
    </changeSet>
</databaseChangeLog>

<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="participant_addColumn_registrationCode2" author="David">
        <addColumn tableName="participant">
            <column name="registration_code" type="VARCHAR(20)">
                <constraints nullable="true" unique="true"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet id="audit_log_participant_addColumn_registrationCode3" author="David">
        <addColumn tableName="audit_log_participant">
            <column name="registration_code" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>
    <!--    create index idx_participant_registration_code-->
    <changeSet id="createIndex_participant_registration_code" author="David">
        <createIndex indexName="idx_participant_registration_code" tableName="participant">
            <column name="registration_code"/>
        </createIndex>
    </changeSet>
    <changeSet id="createFunction_randomize" author="David">
        <sql splitStatements="false">
            CREATE OR REPLACE FUNCTION randomize(str TEXT)
            RETURNS TEXT LANGUAGE SQL IMMUTABLE AS '
            SELECT STRING_AGG(item, '''' ORDER BY random())
            FROM regexp_split_to_table(str, '''') WITH ORDINALITY AS a(item, ord)
            ';
        </sql>
    </changeSet>
    <changeSet id="createFunction_generate_unique_code" author="David">
        <sql splitStatements="false">
            CREATE OR REPLACE FUNCTION generate_unique_code(code_length INT) RETURNS TEXT AS '
            DECLARE
            code TEXT;
            alphabet TEXT := randomize(''ABCDEFGHJKMNPQRSUVWXYZ'');
            numbers TEXT := randomize(''23456789'');
            allowed_chars TEXT;
            begin
            allowed_chars := numbers || upper(alphabet) || numbers || lower(alphabet) || numbers;

            LOOP
            code := '''';
            FOR i IN 1..code_length loop
            allowed_chars := randomize(allowed_chars);
            code := code || substr(allowed_chars, floor(random() * length(allowed_chars) + 1)::int, 1);
            END LOOP;

            -- Check if the code is unique
            EXIT WHEN NOT EXISTS (SELECT 1 FROM participant WHERE registration_code = code);

            END LOOP;

            RETURN code;
            END;
            ' LANGUAGE plpgsql;
        </sql>
    </changeSet>
    <changeSet id="createView_view_participant" author="David">
        <sql splitStatements="false">
            CREATE OR REPLACE VIEW view_participant AS
            SELECT
            p.participant_id,
            p.participant_uuid as entity_id,
            p.name as participant_name,
            pg.name as group_name,
            pt.name as type_name,
            ps.name as status_name,
            p.properties
            from participant p

            left join participant_type_group ptg
            on ptg.participant_type_group_id = p.participant_type_group_id

            left join participant_group pg
            on pg.participant_group_id = ptg.participant_group_id

            left join participant_type pt
            on pt.participant_type_id = ptg.participant_type_id

            left join participant_status ps
            on ps.participant_status_id = p.participant_status_id;
        </sql>
    </changeSet>
    <changeSet id="participant_seedColumn_registrationCode" author="David">
        <sql splitStatements="false">
            UPDATE participant
            SET registration_code = generate_unique_code(12)
            WHERE participant_id IN (
            select distinct
            p.participant_id
            from participant p
            inner join view_participant vp
            on vp.participant_id = p.participant_id
            and vp.group_name ILIKE 'Individual'
            where p.registered = false
            );
        </sql>
    </changeSet>
    <changeSet id="participant_createFunction_updateRegistrationCode6" author="David">
        <sql splitStatements="false">
            CREATE OR REPLACE FUNCTION update_registration_code()
            RETURNS TRIGGER AS '
            BEGIN
            -- If the participant is registered or has a registration code, then do not update the registration code
            IF coalesce(NEW.registered, false) = false AND coalesce(NEW.registration_code,'''') = '''' AND EXISTS (
            select 1
            from participant_type_group ptg
            inner join participant_group pg
            on pg.participant_group_id = ptg.participant_group_id
            and pg.name ILIKE ''Individual''
            where ptg.participant_type_group_id = NEW.participant_type_group_id
            ) THEN
            NEW.registration_code := generate_unique_code(12);
            END IF;

            RETURN NEW;
            END;
            ' LANGUAGE plpgsql;
        </sql>
    </changeSet>
    <changeSet id="participant_createTrigger_updateRegistrationCode7" author="David">
        <sql splitStatements="false">
            CREATE or REPLACE TRIGGER update_registration_code_trigger
            before INSERT or update ON participant
            FOR EACH ROW
            EXECUTE PROCEDURE update_registration_code();
        </sql>
    </changeSet>
</databaseChangeLog>
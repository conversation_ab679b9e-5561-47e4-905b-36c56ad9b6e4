CREATE OR REPLACE FUNCTION get_individuals(
    IN searchFirstName varchar,
    IN searchLastName varchar,
    IN searchEmail varchar,
    IN searchPhone varchar,
    IN searchAddress varchar,
    IN searchAddress2 varchar,
    IN searchCity varchar,
    IN searchState varchar,
    IN searchZip varchar
)
RETURNS TABLE (
    entity_id uuid
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT distinct
        p.entity_id as entity_id
    FROM view_participant p

	--contact email
	left join view_contact cEmail
		on cEmail.group_name = 'Email'
		and cEmail.participant_id = p.participant_id

	--contact phone
	left join view_contact cPhone
		on cPhone.group_name = 'Phone'
		and cPhone.participant_id = p.participant_id

	--associations join address
    LEFT JOIN association a
        ON a.parent_association_type = 'PARTICIPANT'
        AND a.child_association_type = 'ADDRESS'
        AND a.parent_id = p.participant_id
    LEFT JOIN address addr
        ON a.child_id = addr.address_id

    WHERE
	--individual only
	p.group_name = 'Individual'
	AND (
			--custom fields
			(COALESCE(searchFirstName, '') = '' OR p.properties->>'firstName' ILIKE '%'|| searchFirstName || '%')
			AND (COALESCE(searchLastName, '') = '' OR p.properties->>'lastName' ILIKE '%'|| searchLastName || '%')

			--contacts
			AND (COALESCE(searchEmail, '') = '' OR cEmail.contact_value ILIKE '%'|| searchEmail || '%')
			AND (COALESCE(searchPhone, '') = '' OR cPhone.contact_value ILIKE '%'|| searchPhone || '%')

			--address
			AND (COALESCE(searchAddress, '') = '' OR addr.street_address ILIKE '%'|| searchAddress || '%')
			AND (COALESCE(searchAddress2, '')  = '' OR addr.street_address_2 ILIKE '%'|| searchAddress2 || '%')
			AND (COALESCE(searchCity, '') = '' OR addr.city ILIKE  '%'|| searchCity || '%')
			AND (COALESCE(searchState, '') = '' OR addr.state ILIKE  '%'|| searchState || '%')
			AND (COALESCE(searchZip, '') = '' OR addr.zip ILIKE  '%'|| searchZip || '%')
		  ) and
		  (
			  --atleast one must be not null or empty
			  COALESCE(searchFirstName, '') != ''
				OR COALESCE(searchLastName, '') != ''
				OR COALESCE(searchEmail, '') != ''
				OR COALESCE(searchPhone, '') != ''
				OR COALESCE(searchAddress, '') != ''
				OR COALESCE(searchAddress2, '') !=''
				OR COALESCE(searchCity, '') !=''
				OR COALESCE(searchState, '') !=''
				OR COALESCE(searchZip, '') !=''
		  )
	limit 10;
END;
$$;

--drop function get_individuals
--SELECT * FROM get_individuals('T', NULL, NULL, NULL, '', NULL, NULL, NULL, NULL);
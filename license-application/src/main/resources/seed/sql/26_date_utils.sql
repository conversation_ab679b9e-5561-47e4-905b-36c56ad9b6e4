-- start of day
CREATE OR REPLACE FUNCTION license.start_of_day(input_timestamp TIMESTAMPTZ)
RETURNS TIMESTAMPTZ AS $$
BEGIN
    RETURN date_trunc('day', input_timestamp);
END;
$$ LANGUAGE plpgsql;

-- end of day
CREATE OR REPLACE FUNCTION license.end_of_day(input_date TIMESTAMPTZ)
RETURNS TIMESTAMPTZ AS $$
BEGIN
    -- Set the time to 23:59:59 of the given date
    RETURN date_trunc('day', input_date) + INTERVAL '23 hours 59 minutes 59 seconds';
END;
$$ LANGUAGE plpgsql;

-- start of month
CREATE OR REPLACE FUNCTION license.start_of_month(input_date TIMESTAMPTZ)
RETURNS TIMESTAMPTZ AS $$
BEGIN
    -- Calculate the start of the month
    RETURN date_trunc('month', input_date)::TIMESTAMPTZ;
END;
$$ LANGUAGE plpgsql;

-- end of month
CREATE OR REPLACE FUNCTION license.end_of_month(input_date TIMESTAMPTZ)
R<PERSON><PERSON>NS TIMESTAMPTZ AS $$
BEGIN
    -- Calculate the end of the month
    RETURN (date_trunc('month', input_date) + INTERVAL '1 month - 1 second')::TIMESTAMPTZ;
END;
$$ LANGUAGE plpgsql;

-- end of month plus one year
CREATE OR REPLACE FUNCTION license.end_of_month_plus_one_year(input_date TIMESTAMPTZ)
RETURNS TIMESTAMPTZ AS $$
BEGIN
    -- Add one year to the end of the month date
    RETURN license.end_of_month(input_date) + INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION license.addMonths(input_date TIMESTAMPTZ, months_to_add int)
RETURNS TIMESTAMPTZ AS $$
BEGIN
    RETURN (input_date + (months_to_add || ' months')::INTERVAL)::TIMESTAMPTZ;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION license.addYears(input_date TIMESTAMPTZ, years_to_add int)
RETURNS TIMESTAMPTZ AS $$
BEGIN
    RETURN (input_date + (years_to_add || ' years')::INTERVAL)::TIMESTAMPTZ;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION license.addDays(input_date TIMESTAMPTZ, days_to_add int)
RETURNS TIMESTAMPTZ AS $$
BEGIN
    RETURN (input_date + (days_to_add || ' days')::INTERVAL)::TIMESTAMPTZ;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION license.countBetweenDates(count_type text, start_date TIMESTAMPTZ, end_date TIMESTAMPTZ)
RETURNS INT AS $$
BEGIN
    IF count_type = 'days' THEN
        RETURN abs(EXTRACT(day FROM start_date - end_date));
    ELSIF count_type = 'months' THEN
        RETURN abs(EXTRACT(YEAR FROM AGE(end_date, start_date))) * 12 + abs(EXTRACT(MONTH FROM AGE(end_date, start_date)));
    ELSIF count_type = 'years' THEN
        RETURN abs(EXTRACT(YEAR FROM AGE(end_date, start_date)));
    ELSE
        RAISE EXCEPTION 'Invalid count type: %', count_type;
    END IF;
END;
$$ LANGUAGE plpgsql;
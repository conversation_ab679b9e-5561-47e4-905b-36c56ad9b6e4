package com.scube.licensing.features.code_lookup.rabbit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetGeneratedCodeQueryResponse {
    private String code;
    private String entityType;
    private String entityId;
    private String action;

    public GetGeneratedCodeQueryResponse(CodeLookup codeLookup) {
        this.code = codeLookup.getCode();
        this.entityType = codeLookup.getEntityType();
        this.entityId = codeLookup.getEntityId();
        this.action = codeLookup.getAction().name();
    }
}
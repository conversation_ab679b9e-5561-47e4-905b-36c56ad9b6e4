package com.scube.licensing.features.participant.address;

import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddressType;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantAddressTypeRepository;
import org.springframework.stereotype.Service;

@Service
public class ParticipantAddressTypeService {
    private final ParticipantAddressTypeRepository participantAddressTypeRepository;

    public ParticipantAddressTypeService(ParticipantAddressTypeRepository participantAddressTypeRepository) {
        this.participantAddressTypeRepository = participantAddressTypeRepository;
    }

    public ParticipantAddressType getParticipantAddressType(String name) {
        return participantAddressTypeRepository.findByNameIgnoreCase(name)
                .orElseThrow(() -> new ParticipantAddressTypeNotFoundException("ParticipantAddressType with name " + name + " not found"));
    }

    public ParticipantAddressType getParticipantAddressType(Long id) {
        return participantAddressTypeRepository.findById(id)
                .orElseThrow(() -> new ParticipantAddressTypeNotFoundException("ParticipantAddressType with id " + id + " not found"));
    }

    public ParticipantAddressType save(ParticipantAddressType participantAddressType) {
        return participantAddressTypeRepository.save(participantAddressType);
    }
}
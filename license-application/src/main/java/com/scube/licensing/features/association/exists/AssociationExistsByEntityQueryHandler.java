package com.scube.licensing.features.association.exists;

import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import jakarta.transaction.Transactional;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;

@Component
public class AssociationExistsByEntityQueryHandler implements IRequestHandlerAxon<AssociationExistsByEntityQuery, Boolean> {
    private final AssociationRepository associationRepository;

    public AssociationExistsByEntityQueryHandler(AssociationRepository associationRepository) {
        this.associationRepository = associationRepository;
    }

    @Override
    @Transactional
    @QueryHandler
    public Boolean handle(AssociationExistsByEntityQuery command) {
        return associationRepository.findByParentIdAndParentAssociationTypeAndChildIdAndChildAssociationType(
                command.parent().getId(),
                command.parent().getAssociationType(),
                command.child().getId(),
                command.child().getAssociationType()
        ).isPresent();
    }
}
package com.scube.licensing.features.participant.dog.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.features.entity.EntityRequest;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto.createCustomField;

@EqualsAndHashCode(callSuper = true)
public class CreateDogRequestDto extends EntityRequest<CreateDogRequestDto> {
    public CreateDogRequestDto(Map<String, String> map) {
        super(map, Participant.TABLE_NAME);
    }

    @JsonProperty("dogName")
    public String getDogName() {
        return getAndValidate("dogName");
    }

    @JsonProperty("tagNumber")
    public String getTagNumber() {
        return getAndValidate("tagNumber");
    }

    @JsonProperty("dogBreed")
    public String getDogBreed() {
        return getAndValidate("dogBreed");
    }

    @JsonProperty("dogBirthDate")
    public String getDogBirthDate() {
        return getAndValidate("dogBirthDate");
    }

    @JsonProperty("dogSex")
    public String getDogSex() {
        return getAndValidate("dogSex");
    }

    @JsonProperty("dogSpayedOrNeutered")
    public String getDogSpayedOrNeutered() {
        return getAndValidate("dogSpayedOrNeutered");
    }

    @JsonProperty("dogPrimaryColor")
    public String getDogPrimaryColor() {
        return getAndValidate("dogPrimaryColor");
    }

    @JsonProperty("dogSecondaryColor")
    public String getDogSecondaryColor() {
        return getAndValidate("dogSecondaryColor");
    }

    @JsonProperty("microchipNumber")
    public String getMicrochipNumber() {
        return getAndValidate("microchipNumber");
    }

    @JsonProperty("licenseExempt")
    public String getLicenseExempt() {
        return getAndValidate("licenseExempt");
    }

    @JsonProperty("serviceDogType")
    public String getServiceDogType() {
        return getAndValidate("serviceDogType");
    }

    @JsonProperty("dogBio")
    public String getDogBio() {
        return getAndValidate("dogBio");
    }

    @JsonProperty("dogMarkings")
    public String getDogMarkings() {
        return getAndValidate("dogMarkings");
    }

    @JsonProperty("catFriendly")
    public String getCatFriendly() {
        return getAndValidate("catFriendly");
    }

    @JsonProperty("dogFriendly")
    public String getDogFriendly() {
        return getAndValidate("dogFriendly");
    }

    @JsonProperty("childFriendly")
    public String getChildFriendly() {
        return getAndValidate("childFriendly");
    }

    // health information

    @JsonProperty("veterinaryName")
    public String getVeterinaryName() {
        return getAndValidate("veterinaryName");
    }

    @JsonProperty("veterinarianName")
    public String getVeterinarianName() {
        return getAndValidate("veterinarianName");
    }

    @JsonProperty("vaccineDatesExempt")
    public String getVaccineDatesExempt() {
        return getAndValidate("vaccineDatesExempt");
    }

    @JsonProperty("rabiesTagNumber")
    public String getRabiesTagNumber() {
        return getAndValidate("rabiesTagNumber");
    }

    @JsonProperty("vaccineName")
    public String getVaccineName() {
        return getAndValidate("vaccineName");
    }

    @JsonProperty("vaccineProducer")
    public String getVaccineProducer() {
        return getAndValidate("vaccineProducer");
    }

    @JsonProperty("vaccineBrand")
    public String getVaccineBrand() {
        return getAndValidate("vaccineBrand");
    }

    @JsonProperty("vaccineAdministeredDate")
    public String getVaccineAdministeredDate() {
        return getAndValidate("vaccineAdministeredDate");
    }

    @JsonProperty("vaccineDueDate")
    public String getVaccineDueDate() {
        return getAndValidate("vaccineDueDate");
    }

    @JsonProperty("vaccineLotNumber")
    public String getVaccineLotNumber() {
        return getAndValidate("vaccineLotNumber");
    }

    @JsonProperty("vaccineLotExpirationDate")
    public String getVaccineLotExpirationDate() {
        return getAndValidate("vaccineLotExpirationDate");
    }

    public List<CreateCustomFieldsDto> getCustomFields() {
        var result = new ArrayList<>(List.of(
                createCustomField("string", Participant.TABLE_NAME, "dogName", getDogName()),
                createCustomField("string", Participant.TABLE_NAME, "tagNumber", getTagNumber()),
                createCustomField("string", Participant.TABLE_NAME, "dogBreed", getDogBreed()),
                createCustomField("date", Participant.TABLE_NAME, "dogBirthDate", getDogBirthDate()),
                createCustomField("string", Participant.TABLE_NAME, "dogSex", getDogSex()),
                createCustomField("string", Participant.TABLE_NAME, "dogSpayedOrNeutered", getDogSpayedOrNeutered()),
                createCustomField("string", Participant.TABLE_NAME, "dogPrimaryColor", getDogPrimaryColor()),
                createCustomField("string", Participant.TABLE_NAME, "dogSecondaryColor", getDogSecondaryColor()),
                createCustomField("string", Participant.TABLE_NAME, "microchipNumber", getMicrochipNumber()),
                createCustomField("boolean", Participant.TABLE_NAME, "licenseExempt", getLicenseExempt()),
                createCustomField("string", Participant.TABLE_NAME, "serviceDogType", getServiceDogType()),
                createCustomField("string", Participant.TABLE_NAME, "dogBio", getDogBio()),
                createCustomField("string", Participant.TABLE_NAME, "dogMarkings", getDogMarkings()),
                createCustomField("string", Participant.TABLE_NAME, "catFriendly", getCatFriendly()),
                createCustomField("string", Participant.TABLE_NAME, "dogFriendly", getDogFriendly()),
                createCustomField("string", Participant.TABLE_NAME, "childFriendly", getChildFriendly()),
                createCustomField("string", Participant.TABLE_NAME, "veterinaryName", getVeterinaryName()),
                createCustomField("string", Participant.TABLE_NAME, "veterinarianName", getVeterinarianName()),
                createCustomField("boolean", Participant.TABLE_NAME, "vaccineDatesExempt", getVaccineDatesExempt()),
                createCustomField("string", Participant.TABLE_NAME, "rabiesTagNumber", getRabiesTagNumber()),
                createCustomField("string", Participant.TABLE_NAME, "vaccineName", getVaccineName()),
                createCustomField("string", Participant.TABLE_NAME, "vaccineProducer", getVaccineProducer()),
                createCustomField("string", Participant.TABLE_NAME, "vaccineBrand", getVaccineBrand()),
                createCustomField("date", Participant.TABLE_NAME, "vaccineAdministeredDate", getVaccineAdministeredDate()),
                createCustomField("date", Participant.TABLE_NAME, "vaccineDueDate", getVaccineDueDate()),
                createCustomField("string", Participant.TABLE_NAME, "vaccineLotNumber", getVaccineLotNumber()),
                createCustomField("date", Participant.TABLE_NAME, "vaccineLotExpirationDate", getVaccineLotExpirationDate())
        ));

        getAdditionalFields().entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (map, entry) -> map.put(entry.getKey(), entry.getValue()), HashMap::putAll)
                .forEach((key, value) -> result.add(createCustomField("string", Participant.TABLE_NAME, key.toString(), value)));

        return result;
    }
}

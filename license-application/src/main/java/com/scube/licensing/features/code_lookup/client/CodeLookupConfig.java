package com.scube.licensing.features.code_lookup.client;


import com.scube.auth.library.ITokenService;
import com.scube.client.config.ProxyBeanUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import java.util.function.Supplier;

@Configuration
@RequiredArgsConstructor
public class CodeLookupConfig {
    private final ITokenService tokenService;

    @Bean
    @Profile("!test")
    public ICodeLookupExchange codeLookupControllerHttpExchange(@Value("${com.scube.client.license}") String serviceUrl) {
        Supplier<String> superAdminTokenFunction = tokenService::getNewAccessTokenFromCurrentRealm;
        return ProxyBeanUtil.createHttpProxyClient(serviceUrl, superAdminTokenFunction, ICodeLookupExchange.class);
    }

    @Bean
    @Profile("test")
    public ICodeLookupExchange codeLookupControllerHttpExchangeTest(@Value("${com.scube.client.license}") String serviceUrl) {
        return ProxyBeanUtil.createHttpProxyClient(serviceUrl, ICodeLookupExchange.class);
    }
}

package com.scube.licensing.features.license.type;

import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class LicenseTypeRequest {

    private String name;
    private String code;
    private String description;
    private String groupName;

    public static LicenseType to(LicenseTypeRequest request) {
        return new LicenseType()
                .setCode(request.getCode())
                .setName(request.getName())
                .setDescription(request.getDescription())
                .setGroupName(request.getGroupName());
    }
}

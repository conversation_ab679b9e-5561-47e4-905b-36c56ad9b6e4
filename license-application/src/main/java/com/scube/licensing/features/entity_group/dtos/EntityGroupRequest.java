package com.scube.licensing.features.entity_group.dtos;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.features.entity.IEntityRequest;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

import java.util.*;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EntityGroupRequest implements IEntityRequest {
    @NotBlank
    private String name;

    @JsonAnySetter
    private Map<String, Object> properties = new HashMap<>();

    @Getter(AccessLevel.NONE)
    @JsonProperty("associations")
    private Set<Map<String, Object>> associations = new HashSet<>();

    @JsonIgnore
    public Set<EntityAssociation> getAssociations() {
        return associations.stream()
                .map(EntityAssociation::new)
                .collect(Collectors.toSet());
    }

    @JsonIgnore
    public List<CreateCustomFieldsDto> getCustomFields() {
        var result = new ArrayList<CreateCustomFieldsDto>();

        getProperties()
                .forEach((key, value) -> result.add(CreateCustomFieldsDto.createCustomField("object", EntityFee.TABLE_NAME, key, value)));

        return result;
    }
}
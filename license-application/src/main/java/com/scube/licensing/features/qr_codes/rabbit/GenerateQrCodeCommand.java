package com.scube.licensing.features.qr_codes.rabbit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.licensing.features.code_lookup.dto.CodeLookupRequest;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GenerateQrCodeCommand extends CodeLookupRequest implements IRabbitFanoutSubscriberRpc<GenerateQrCodeCommandResponse> {
    private String qrCodeText;
    private String code;

    @JsonIgnore
    public boolean byCode() {
        return !ObjectUtils.isEmpty(code);
    }

    @JsonIgnore
    public boolean byEntity() {
        return !ObjectUtils.isEmpty(getEntityType()) && !ObjectUtils.isEmpty(getEntityId());
    }

    @JsonIgnore
    public boolean byQrCodeText() {
        return !ObjectUtils.isEmpty(qrCodeText);
    }
}

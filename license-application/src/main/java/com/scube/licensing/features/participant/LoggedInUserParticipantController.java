package com.scube.licensing.features.participant;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.licensing.features.participant.dto.CreateParticipantResponseDTO;
import com.scube.licensing.features.participant.mapper.ParticipantDtoMapper;
import com.scube.licensing.features.participant.swagger.Swagger_LoggedInUserParticipantController_CreateOnlineResident;
import com.scube.licensing.features.participant.swagger.Swagger_LoggedInUserParticipantController_UpdateDog;
import com.scube.licensing.features.participant.swagger.Swagger_LoggedInUserParticipantController_UpdateIndividual;
import com.scube.licensing.features.participant.swagger.Swagger_LoggedInUserParticipantController_UpdateOnlineParticipantContact;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import com.scube.licensing.infrastructure.validation.NullOrUndefinedToNull;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("me/participant")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
public class LoggedInUserParticipantController {
    private final ParticipantService participantService;
    private final ParticipantDtoMapper participantDtoMapper;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @Swagger_LoggedInUserParticipantController_CreateOnlineResident
    @RolesAllowed(Permissions.LoggedInUserParticipant.CREATE_ONLINE_RESIDENT)
    public CreateParticipantResponseDTO createOnlineResident(@AuthenticationPrincipal OpenidClaimSet jwt,
                                                             @RequestParam(required = false) @NullOrUndefinedToNull Map<String, String> fields,
                                                             @RequestParam(required = false) MultiValueMap<String, MultipartFile> files) {
        return participantService.createOnlineResident(UUID.fromString(jwt.getSubject()), fields, files.toSingleValueMap());
    }

    @PostMapping("/mark-online")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Mark the logged in user as registered online", description = "This is used to mark the logged in user as registered online in the resident portal.")
    @RolesAllowed(Permissions.LoggedInUserParticipant.MARK_ONLINE_RESIDENT)
    public void markOnlineResident(@AuthenticationPrincipal OpenidClaimSet jwt) {
        participantService.martResidentAsOnline(UUID.fromString(jwt.getSubject()));
    }

    @PostMapping("/mark-offline")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Mark the logged in user as offline", description = "This is used to mark the logged in user as offline in the resident portal.")
    @RolesAllowed(Permissions.LoggedInUserParticipant.MARK_OFFLINE_RESIDENT)
    public void markOfflineResident(@AuthenticationPrincipal OpenidClaimSet jwt) {
        participantService.martResidentAsOffline(UUID.fromString(jwt.getSubject()));
    }

    @PatchMapping("/contact")
    @ResponseStatus(HttpStatus.OK)
    @Swagger_LoggedInUserParticipantController_UpdateOnlineParticipantContact
    @RolesAllowed(Permissions.LoggedInUserParticipant.UPDATE_ONLINE_PARTICIPANT_CONTACT)
    public void updateOnlineParticipantContact(@AuthenticationPrincipal OpenidClaimSet jwt,
                                               @RequestBody @NullOrUndefinedToNull Map<String, String> fields) {
        participantService.patchParticipantContact(UUID.fromString(jwt.getSubject()), fields);
    }

    @PatchMapping(value = "individual", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @Swagger_LoggedInUserParticipantController_UpdateIndividual
    @RolesAllowed(Permissions.LoggedInUserParticipant.UPDATE_INDIVIDUAL)
    public void updateIndividual(@AuthenticationPrincipal OpenidClaimSet jwt,
                                 @RequestParam(required = false) @NullOrUndefinedToNull Map<String, String> fields,
                                 @RequestParam(required = false) Map<String, MultipartFile> files) {
        participantService.patchOnlineParticipant(UUID.fromString(jwt.getSubject()), fields, files);
    }

    @PatchMapping(value = "dog/{dogEntityId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @Swagger_LoggedInUserParticipantController_UpdateDog
    @RolesAllowed(Permissions.LoggedInUserParticipant.UPDATE_DOG)
    @PreAuthorize("isOwnerOfDog(#dogEntityId)")
    public void updateDog(@PathVariable UUID dogEntityId,
                          @RequestParam(required = false) @NullOrUndefinedToNull Map<String, String> fields,
                          @RequestParam(required = false) Map<String, MultipartFile> files) {
        participantService.patchOnlineParticipant(dogEntityId, fields, files);
    }

    @GetMapping
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get the logged in participant", description = "This is used to get the logged in participant.")
    @RolesAllowed(Permissions.LoggedInUserParticipant.GET_PARTICIPANT)
    public ParticipantDto getParticipant(@AuthenticationPrincipal OpenidClaimSet jwt) {
        return participantDtoMapper.toDto(participantService.getParticipantOrElseThrowReadOnly(UUID.fromString(jwt.getSubject())));
    }
}
package com.scube.licensing.features.entity_fee;

import com.scube.scheduling.lib.IScheduledTask;
import com.scube.scheduling.lib.ScheduledTaskConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Slf4j
@AllArgsConstructor
@Component
@ScheduledTaskConfig(cronExpression = "0 */1 * * * *", schedulerName = "recurringEntityFeeScheduler")
@Profile("!test")
public class RecurringEntityFeeScheduler implements IScheduledTask {
    private final EntityFeeService entityFeeService;

    @Override
    public void execute() {
        log.info("Executing RecurringEntityFeeScheduler");
        entityFeeService.processRecurringFees();
    }
}
package com.scube.licensing.features.profile.mapper;

import com.scube.licensing.features.address.mapper.AddressDtoMapper;
import com.scube.licensing.features.business.mapper.BusinessDtoMapper;
import com.scube.licensing.features.document.mapper.DocumentDtoMapper;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.entity_fee.mapper.EntityFeeDtoMapper;
import com.scube.licensing.features.entity_group.mapper.EntityGroupMapper;
import com.scube.licensing.features.entity_note.mapper.EntityNoteDtoMapper;
import com.scube.licensing.features.license.mapper.LicenseDtoMapper;
import com.scube.licensing.features.participant.mapper.ParticipantDtoMapper;
import com.scube.licensing.features.profile.dto.ProfileAndAssociationsDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.features.profile.model.ProfileAndAssociations;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.business.Business;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.licensing.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.licensing.infrastructure.db.entity.entity_note.EntityNote;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.http.HttpStatus.BAD_REQUEST;

@Service
@RequiredArgsConstructor
public class ProfileMapperService {
    private final AddressDtoMapper addressDtoMapper;
    private final LicenseDtoMapper licenseDtoMapper;
    private final ParticipantDtoMapper participantDtoMapper;
    private final DocumentDtoMapper documentDtoMapper;
    private final EntityFeeDtoMapper entityFeeDtoMapper;
    private final BusinessDtoMapper businessDtoMapper;
    private final EntityNoteDtoMapper entityNoteDtoMapper;
    private final EntityGroupMapper entityGroupMapper;

    public List<IAssociableDto> toDto(List<Associable> profiles) {
        return profiles.stream()
                .map(this::toDto)
                .toList();
    }

    public IEntityDto toQueryDto(Associable profile) {
        return switch (profile.getAssociationType()) {
            case CUSTOM_ENTITY -> throw new ResponseStatusException(BAD_REQUEST, "Invalid profile type");
            case LICENSE -> licenseDtoMapper.toQueryDto((License) profile);
            case PARTICIPANT -> participantDtoMapper.toQueryDto((Participant) profile);
            case DOCUMENT -> documentDtoMapper.toDto((Document) profile);
            case ADDRESS -> addressDtoMapper.toDto((Address) profile);
            case ENTITY_FEE -> entityFeeDtoMapper.toDto((EntityFee) profile);
            case BUSINESS -> businessDtoMapper.toDto((Business) profile);
            case ENTITY_NOTE -> entityNoteDtoMapper.toDto((EntityNote) profile);
            case ENTITY_GROUP -> entityGroupMapper.toDto((EntityGroup) profile);
        };
    }

    public IAssociableDto toDto(Associable profile) {
        return switch (profile.getAssociationType()) {
            case CUSTOM_ENTITY -> throw new ResponseStatusException(BAD_REQUEST, "Invalid profile type");
            case LICENSE -> licenseDtoMapper.toDto((License) profile);
            case PARTICIPANT -> participantDtoMapper.toDto((Participant) profile);
            case DOCUMENT -> documentDtoMapper.toDto((Document) profile);
            case ADDRESS -> addressDtoMapper.toDto((Address) profile);
            case ENTITY_FEE -> entityFeeDtoMapper.toDto((EntityFee) profile);
            case BUSINESS -> businessDtoMapper.toDto((Business) profile);
            case ENTITY_NOTE -> entityNoteDtoMapper.toDto((EntityNote) profile);
            case ENTITY_GROUP -> entityGroupMapper.toDto((EntityGroup) profile);
        };
    }

    public ProfileAndAssociationsDto toDto(ProfileAndAssociations profileAndAssociations) {
        var profile = profileAndAssociations.getProfile();
        var associables = new ArrayList<>(profileAndAssociations.getAssociables());
        var associations = new ArrayList<>(profileAndAssociations.getAssociations());

        //Need to get license holder address for license, since licenses are not associated with addresses
        if (profile instanceof License) {
            var addressResult = new ArrayList<Associable>();
            for (Associable assoc : associables) {
                if (assoc instanceof Participant participant && participant.isIndividual()) {
                    addressResult.addAll(participant.getAddresses());
                }
            }

            associables.addAll(addressResult);
        }

        IAssociableDto profileDto = toDto(profile);
        List<IAssociableDto> associationDtos = associations.stream()
                .map(x -> {
                    IAssociableDto dto = this.toDto(x.getChildAssociable());
                    dto.putProperties(x.getProperties());
                    return dto;
                })
                .toList();

        return new ProfileAndAssociationsDto(profileDto, associationDtos);
    }
}

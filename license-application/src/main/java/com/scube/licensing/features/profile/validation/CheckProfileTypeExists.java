package com.scube.licensing.features.profile.validation;

import com.scube.licensing.features.profile.ProfileService;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CheckProfileTypeExists.CheckProfileTypeExistValidator.class)
public @interface CheckProfileTypeExists {
    String message() default "Profile type does not exist";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    class CheckProfileTypeExistValidator implements ConstraintValidator<CheckProfileTypeExists, String> {
        private final ProfileService profileService;

        @Autowired
        public CheckProfileTypeExistValidator(ProfileService profileService) {
            this.profileService = profileService;
        }

        @Override
        public void initialize(CheckProfileTypeExists constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(String name, ConstraintValidatorContext constraintValidatorContext) {
            return profileService.checkProfileTypeExistsByName(name);
        }
    }
}
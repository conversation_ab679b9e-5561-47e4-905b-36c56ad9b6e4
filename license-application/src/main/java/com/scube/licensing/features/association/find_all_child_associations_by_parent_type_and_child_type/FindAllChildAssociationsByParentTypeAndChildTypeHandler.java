package com.scube.licensing.features.association.find_all_child_associations_by_parent_type_and_child_type;

import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class FindAllChildAssociationsByParentTypeAndChildTypeHandler implements IRequestHandlerAxon<FindAllChildAssociationsByParentTypeAndChildTypeQuery, List<Association>> {
    private final AssociationRepository associationRepository;

    public FindAllChildAssociationsByParentTypeAndChildTypeHandler(AssociationRepository associationRepository) {
        this.associationRepository = associationRepository;
    }

    @Override
    @QueryHandler
    public List<Association> handle(FindAllChildAssociationsByParentTypeAndChildTypeQuery query) {
        return associationRepository.findAllByParentIdAndParentAssociationTypeAndChildAssociationType(
                query.parent().getId(),
                query.parent().getAssociationType(),
                query.childAssociationType()
        );
    }
}
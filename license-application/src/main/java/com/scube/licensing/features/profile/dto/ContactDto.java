package com.scube.licensing.features.profile.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContactDto {
    private Long id;
    private String type;
    private String group;
    private String value;

    @JsonProperty("isPrimary")
    private boolean isPrimary;

    @JsonProperty("isEmail")
    private boolean isEmail;

    @JsonProperty("isPhone")
    private boolean isPhone;
}
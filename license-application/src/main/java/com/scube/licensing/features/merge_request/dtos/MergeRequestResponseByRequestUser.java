package com.scube.licensing.features.merge_request.dtos;

import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@NoArgsConstructor
public final class MergeRequestResponseByRequestUser {
    private IAssociableDto requestedResident;
    private List<MergeRequestResponseLineItem> requests;

    public MergeRequestResponseByRequestUser(UUID requestedIndividualId, List<MergeRequestResponseLineItem> items, Map<UUID, IAssociableDto> residents) {
        this.requests = items;
        requestedResident = residents.get(requestedIndividualId);
    }
}
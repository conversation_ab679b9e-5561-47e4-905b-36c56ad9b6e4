package com.scube.licensing.features.profile.get_profile_header;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.features.profile.validation.CheckProfileTypeExists;

import jakarta.validation.constraints.NotBlank;

import java.util.UUID;


public record GetProfileHeaderQuery(
        @CheckProfileTypeExists
        @NotBlank(message = "Profile type is required")
        String profileType,

        @NotBlank(message = "EntityId is required")
        UUID entityId
) implements IRequestAxon<GetProfileHeaderResponse> {

}
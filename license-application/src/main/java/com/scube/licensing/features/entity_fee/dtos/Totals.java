package com.scube.licensing.features.entity_fee.dtos;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
public class Totals {
    private BigDecimal totalAmount = BigDecimal.ZERO;
    private BigDecimal totalPaidAmount = BigDecimal.ZERO;
    private BigDecimal totalOutstandingAmount = BigDecimal.ZERO;
    private BigDecimal totalDiscountAmount = BigDecimal.ZERO;
    private BigDecimal totalSubtotal = BigDecimal.ZERO;

    public void add(Totals other) {
        this.totalAmount = this.totalAmount.add(other.totalAmount);
        this.totalPaidAmount = this.totalPaidAmount.add(other.totalPaidAmount);
        this.totalOutstandingAmount = this.totalOutstandingAmount.add(other.totalOutstandingAmount);
        this.totalDiscountAmount = this.totalDiscountAmount.add(other.totalDiscountAmount);
        this.totalSubtotal = this.totalSubtotal.add(other.totalSubtotal);
    }
}

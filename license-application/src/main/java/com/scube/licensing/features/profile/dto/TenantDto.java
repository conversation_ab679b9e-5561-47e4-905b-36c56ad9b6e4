package com.scube.licensing.features.profile.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.UUID;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TenantDto {
    private UUID entityId;
    private String templateKey;
    private String clerkSignature;
    private String clerkName;
    private String clerkTitle;
    private String cityClerkOfficeName;
    private String clerkEmail;
    private String clerkPhoneNumber;
    private String adminOffice;
    private String adminOfficeRoom;
    private String adminStreet;
    private String adminCity;
    private String adminState;
    private String adminZipCode;
    private String clerkXpressUrl;
}

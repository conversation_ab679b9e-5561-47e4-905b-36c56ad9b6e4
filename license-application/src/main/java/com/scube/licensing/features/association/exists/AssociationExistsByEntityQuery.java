package com.scube.licensing.features.association.exists;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;

public record AssociationExistsByEntityQuery(Associable parent, Associable child) implements IRequestAxon<Boolean> {
    public AssociationExistsByEntityQuery {
        if (parent == null || parent.getId() == null)
            throw new IllegalArgumentException("parent cannot be null");
        if (child == null || child.getId() == null)
            throw new IllegalArgumentException("child cannot be null");
    }
}
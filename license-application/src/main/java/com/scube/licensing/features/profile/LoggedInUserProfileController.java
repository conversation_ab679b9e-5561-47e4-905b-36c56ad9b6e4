package com.scube.licensing.features.profile;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.client.config.generated.ConfigServiceConnection;
import com.scube.lib.misc.annotations.validation.NoValidation;
import com.scube.licensing.features.participant.validation.CreateParticipantIfNotExists;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.db.entity.entity_note.EntityNote;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;
import java.util.UUID;

@RestController
@RequestMapping("me/profile")
@Slf4j
@Validated
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
public class LoggedInUserProfileController {
    private final ProfileService profileService;
    private final RejectedFieldsService rejectedFieldsService;
    private final ConfigServiceConnection configServiceConnection;

    @GetMapping("/individual")
    @ResponseStatus(HttpStatus.OK)
    public JsonNode getIndividualEntityAndAllAssociations(@AuthenticationPrincipal @CreateParticipantIfNotExists OpenidClaimSet jwt) {
        var individual = profileService.getProfileAndAssociationsAsJsonReadOnly(UUID.fromString(jwt.getSubject()), "individual");
        var permissions = configServiceConnection.loggedInUserTenant().getRoles();
        ((ObjectNode) individual).putPOJO("permissions", permissions);
        ((ObjectNode) individual).remove(EntityNote.ENTITY_TYPE);
        return individual;
    }

    @GetMapping("/individual/{ignore}")
    @ResponseStatus(HttpStatus.OK)
    public JsonNode getIndividualEntityAndAllAssociations(@AuthenticationPrincipal @CreateParticipantIfNotExists OpenidClaimSet jwt, @PathVariable @NoValidation String ignore) {
        var result = getIndividualEntityAndAllAssociations(jwt);
        ((ObjectNode) result).remove("permissions");
        return result;
    }

    @GetMapping("/license/{licenseEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_LICENSE_ENTITY_AND_ALL_ASSOCIATIONS)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public JsonNode getLicenseEntityAndAllAssociations(@PathVariable UUID licenseEntityId) {
        var result = profileService.getProfileAndAssociationsAsJsonReadOnly(licenseEntityId, "license");
        ((ObjectNode) result).remove(EntityNote.ENTITY_TYPE);
        return result;
    }

    @GetMapping("/dog/{dogEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_DOG_ENTITY_AND_ALL_ASSOCIATIONS)
    @PreAuthorize("isOwnerOfDog(#dogEntityId)")
    public JsonNode getDogEntityAndAllAssociations(@PathVariable UUID dogEntityId) {
        var result = profileService.getProfileAndAssociationsAsJsonReadOnly(dogEntityId, "dog");
        ((ObjectNode) result).remove(EntityNote.ENTITY_TYPE);
        return result;
    }

    @GetMapping("/address/{addressEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_ADDRESS_ENTITY_AND_ALL_ASSOCIATIONS)
    @PreAuthorize("isOwnerOfAddress(#addressEntityId)")
    public JsonNode getAddressEntityAndAllAssociations(@PathVariable UUID addressEntityId) {
        var result = profileService.getProfileAndAssociationsAsJsonReadOnly(addressEntityId, "address");
        ((ObjectNode) result).remove(EntityNote.ENTITY_TYPE);
        return result;
    }

    @GetMapping("/document/{documentEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_DOCUMENT_ENTITY_AND_ALL_ASSOCIATIONS)
    @PreAuthorize("isOwnerOfDocument(#documentEntityId)")
    public JsonNode getDocumentEntityAndAllAssociations(@PathVariable UUID documentEntityId) {
        var result = profileService.getProfileAndAssociationsAsJsonReadOnly(documentEntityId, "document");
        ((ObjectNode) result).remove(EntityNote.ENTITY_TYPE);
        return result;
    }

    @GetMapping("/{entityType}/{entityId}/reject-fields")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_REJECTED_FIELD_LIST)
    @PreAuthorize("isOwnerOfEntity(#entityType, #entityId)")
    public RejectFieldResponse getRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        return new RejectFieldResponse(rejectedFieldsService.getRejectedFieldList(entityType, entityId));
    }

    @PatchMapping("/{entityType}/{entityId}/reject-fields")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserProfile.ADD_TO_REJECTED_FIELD_LIST)
    public void addToRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType,
                                       @PathVariable UUID entityId,
                                       @RequestBody com.scube.licensing.features.profile.ProfileController.RejectFieldRequest request) {
        rejectedFieldsService.addToRejectedFieldList(entityType, entityId, request.fields(), true);
    }

    @DeleteMapping("/{entityType}/{entityId}/reject-fields")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserProfile.REMOVE_FROM_REJECTED_FIELD_LIST)
    public void removeFromRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId,
                                            @RequestParam("field") Set<String> fields) {
        rejectedFieldsService.removeFromRejectedFieldList(entityType, entityId, fields, true);
    }

    @DeleteMapping("/{entityType}/{entityId}/reject-fields/clear")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserProfile.CLEAR_REJECTED_FIELD_LIST)
    public void clearRejectedFieldList(@Valid @PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        rejectedFieldsService.clearRejectedFieldList(entityType, entityId);
    }

    // @formatter:off
    public record RejectFieldResponse(Set<String> fields){}
}
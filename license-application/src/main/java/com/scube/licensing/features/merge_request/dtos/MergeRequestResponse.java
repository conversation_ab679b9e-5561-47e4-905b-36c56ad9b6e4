package com.scube.licensing.features.merge_request.dtos;

import com.scube.licensing.features.merge_request.MergeRequestUtil;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class MergeRequestResponse {
    private List<MergeRequestResponseByRequestUser> content;

    public MergeRequestResponse(List<MergeRequest> requests, ProfileService profileService) {
        Map<UUID, IAssociableDto> residents = MergeRequestUtil.buildResidentDtos(requests, profileService);

        var responseItems = requests.stream()
                .map(x -> new MergeRequestResponseLineItem(x, residents))
                .toList();

        //group the requests by the requestedIndividualId
        this.content = responseItems.stream().collect(
                        Collectors.groupingBy(
                                MergeRequestResponseLineItem::getRequestedIndividualId
                        )
                ).entrySet().stream()
                .map(entry -> new MergeRequestResponseByRequestUser(entry.getKey(), entry.getValue(), residents))
                .toList();
    }
}
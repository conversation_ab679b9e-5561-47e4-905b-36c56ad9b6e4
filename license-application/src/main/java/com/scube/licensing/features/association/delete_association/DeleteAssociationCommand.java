package com.scube.licensing.features.association.delete_association;

import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import com.scube.licensing.infrastructure.db.entity.association.Association;

public record DeleteAssociationCommand(Association association) implements IRequestVoidAxon {
    public DeleteAssociationCommand {
        if (association == null || association.getId() == null)
            throw new IllegalArgumentException("association cannot be null");
    }
}
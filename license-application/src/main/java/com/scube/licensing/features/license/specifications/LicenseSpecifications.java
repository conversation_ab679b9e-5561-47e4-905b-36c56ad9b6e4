package com.scube.licensing.features.license.specifications;

import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivity;
import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class LicenseSpecifications {
    private static final DateTimeFormatter formatter = new DateTimeFormatterBuilder()
            .appendPattern("yyyy-MM-dd")
            .toFormatter();
    public static final String STATUS = "status";
    public static final String TYPE = "type";
    public static final String ACTIVITY = "activity";
    public static final String VALID_TO_DATE = "validToDate";
    public static final String VALID_FROM_DATE = "validFromDate";
    public static final String LICENSE_STATUS = "licenseStatus";
    public static final String LICENSE_TYPE = "licenseType";
    public static final String NAME = "name";
    public static final String ACTIVITY_TYPE = "activityType";
    public static final String LICENSE = "license";
    public static final String CODE = "code";
    public static final String LICENSE_ACTIVITIES = "licenseActivities";
    public static final String ENTITY_ID = "entityId";
    public static final String ISSUED_DATE = "issuedDate";
    public static final String CREATED_BY = "createdBy";
    public static final String CREATED_DATE = "createdDate";

    public static final String APPROVED = "approved";

    public static Specification<License> createSpecification(Map<String, Object> searchParams) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (searchParams.containsKey(ENTITY_ID)) {
                String entityId = ((String) searchParams.get(ENTITY_ID)).toLowerCase();
                predicates.add(cb.equal(cb.lower(root.get(ENTITY_ID)), entityId));
            }

            if (searchParams.containsKey(CREATED_BY)) {
                String createdBy = ((String) searchParams.get(CREATED_BY)).toLowerCase();
                predicates.add(cb.equal(
                        cb.lower(root.get(CREATED_BY)),
                        createdBy
                ));
            }

            if (searchParams.containsKey(CREATED_DATE)) {
                String[] dates = ((String) searchParams.get(CREATED_DATE)).split(",");

                predicates.add(cb.between(root.get(CREATED_DATE), parseInstant(dates[0]), parseInstant(dates[1])));
            }

            if (searchParams.containsKey(STATUS)) {
                String[] statuses = ((String) searchParams.get(STATUS)).toLowerCase().split(",");
                predicates.add(cb.lower(root.get(LICENSE_STATUS).get(NAME)).in(statuses));
            }

            if (searchParams.containsKey(CODE)) {
                String[] codes = ((String) searchParams.get(CODE)).toLowerCase().split(",");
                predicates.add(cb.lower(root.get(LICENSE_STATUS).get(CODE)).in(codes));
            }

            if (searchParams.containsKey(TYPE)) {
                String[] types = ((String) searchParams.get(TYPE)).toLowerCase().split(",");
                predicates.add(cb.lower(root.get(LICENSE_TYPE).get(CODE)).in(types));
            }

            if (searchParams.containsKey(VALID_FROM_DATE)) {
                String[] dates = ((String) searchParams.get(VALID_FROM_DATE)).split(",");

                predicates.add(cb.between(root.get(VALID_FROM_DATE), parseInstant(dates[0]), parseInstant(dates[1])));
            }

            if (searchParams.containsKey(VALID_TO_DATE)) {
                String[] dates = ((String) searchParams.get(VALID_TO_DATE)).split(",");

                predicates.add(cb.between(root.get(VALID_TO_DATE), parseInstant(dates[0]), parseInstant(dates[1])));
            }

            if (searchParams.containsKey(ISSUED_DATE)) {
                String[] dates = ((String) searchParams.get(ISSUED_DATE)).split(",");

                predicates.add(cb.between(root.get(ISSUED_DATE), parseInstant(dates[0]), parseInstant(dates[1])));
            }

            if (searchParams.containsKey(ACTIVITY)) {
                LicenseActivity.ActivityType activity = LicenseActivity.ActivityType.valueOf(
                        ((String) searchParams.get(ACTIVITY)).toUpperCase());

                // Step 1: Subquery to find the max ID of LicenseActivity for each License
                Subquery<Long> maxActivityIdSubquery = query.subquery(Long.class);
                Root<LicenseActivity> activityRoot = maxActivityIdSubquery.from(LicenseActivity.class);
                maxActivityIdSubquery.select(cb.max(activityRoot.get("id")))
                        .where(cb.equal(activityRoot.get("license"), root));

                // Step 2: Use the subquery in the main query to match the most recent activity's type
                Join<License, LicenseActivity> activitiesJoin = root.join(LICENSE_ACTIVITIES, JoinType.LEFT);
                Predicate mostRecentActivityMatch = cb.equal(activitiesJoin.get("id"), maxActivityIdSubquery);
                Predicate activityTypeMatch = cb.equal(activitiesJoin.get(ACTIVITY_TYPE), activity);

                // Combine predicates
                predicates.add(cb.and(mostRecentActivityMatch, activityTypeMatch));
            }

            if (searchParams.containsKey(APPROVED)) {
                boolean approved = Boolean.parseBoolean((String) searchParams.get(APPROVED));
                predicates.add(cb.equal(root.get(APPROVED), approved));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }

    public static Instant parseInstant(String date) {
        return LocalDate.parse(date, formatter).atStartOfDay(ZoneId.of("UTC")).toInstant();
    }
}


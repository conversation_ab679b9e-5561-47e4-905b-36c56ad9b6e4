package com.scube.licensing.features.merge_request.dtos;

import com.scube.licensing.features.merge_request.MergeRequestUtil;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestExistingUser;
import lombok.Data;
import org.springframework.http.HttpStatus;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Data
public class GetByRequestedUserMergeRequestResponse {
    private IAssociableDto requestedResident;
    private List<GetByRequestedUserFoundResident> foundResidents = new ArrayList<>();

    public GetByRequestedUserMergeRequestResponse(UUID requestedUserId, List<MergeRequest> requests, ProfileService profileService) {
        Map<UUID, IAssociableDto> residents = MergeRequestUtil.buildResidentDtos(requests, profileService);
        // clear events since we don't need them in this response
        residents.forEach((key, value) -> {
            if (value.getEvents() != null) value.getEvents().clear();
        });
        this.requestedResident = residents.getOrDefault(requestedUserId, null);
        if (ObjectUtils.isEmpty(requestedResident))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Requested user with id " + requestedUserId + " not found");

        Map<UUID, List<MergeRequestExistingUser>> groupedFoundedResidents = requests.stream()
                .flatMap(x -> x.getExistingUsers().stream())
                .distinct()
                .collect(Collectors.groupingBy(MergeRequestExistingUser::getExistingUserId));

        for (var entry : groupedFoundedResidents.entrySet()) {
            foundResidents.add(new GetByRequestedUserFoundResident(entry.getKey(), requests, entry.getValue(), residents));
        }

        // filter out any foundResidents that has a resident that is null
        foundResidents.removeIf(x -> ObjectUtils.isEmpty(x) || ObjectUtils.isEmpty(x.getResident()));
    }

    @Data
    public static final class GetByRequestedUserFoundResident {
        private IAssociableDto resident;
        private List<GetByRequestedUserFoundResidentRequest> requests = new ArrayList<>();

        public GetByRequestedUserFoundResident(UUID foundedUserId, List<MergeRequest> mergeRequests,
                                               List<MergeRequestExistingUser> existingUsers, Map<UUID, IAssociableDto> residents) {
            this.resident = residents.getOrDefault(foundedUserId, null);
            for (MergeRequestExistingUser existingUser : existingUsers) {
                MergeRequest mergeRequest = mergeRequests.stream()
                        .filter(x -> x.getExistingUsers().contains(existingUser))
                        .findFirst()
                        .orElseThrow(() -> new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Something went wrong finding merge request"));
                this.requests.add(new GetByRequestedUserFoundResidentRequest(existingUser, mergeRequest));
            }

            // filter out any requests that are suggested
            requests.removeIf(x -> x.getMatchType().equalsIgnoreCase("suggested"));

            // make resident null if there are no requests
            if (ObjectUtils.isEmpty(requests)) this.resident = null;
        }
    }

    @Data
    public static class GetByRequestedUserFoundResidentRequest {
        private UUID mergeRequestEntityId;
        private String searchBy;
        private String searchValue;
        private String status;
        private Instant createdDate;
        private String matchType;
        private Integer probability;

        public GetByRequestedUserFoundResidentRequest(MergeRequestExistingUser existingUser, MergeRequest mergeRequest) {
            this.mergeRequestEntityId = mergeRequest.getUuid();
            this.searchBy = existingUser.getSearchBy();
            this.searchValue = mergeRequest.getSearchValue();
            this.status = mergeRequest.getStatus().toString();
            this.createdDate = mergeRequest.getCreatedDate();
            this.probability = existingUser.getProbability();
            this.matchType = existingUser.getMatchType();
        }
    }
}
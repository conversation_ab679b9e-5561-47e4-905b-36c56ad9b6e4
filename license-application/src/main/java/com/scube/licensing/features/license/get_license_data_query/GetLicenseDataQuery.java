package com.scube.licensing.features.license.get_license_data_query;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.UUID;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetLicenseDataQuery implements IRabbitFanoutSubscriberRpc<Map<String, Object>> {
    private UUID licenseEntityId;

    @JsonProperty("formatData")
    private boolean formatData = true;
}
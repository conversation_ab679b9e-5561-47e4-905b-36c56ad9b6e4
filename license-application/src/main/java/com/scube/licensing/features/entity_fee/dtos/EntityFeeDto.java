package com.scube.licensing.features.entity_fee.dtos;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.profile.dto.EventDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EntityFeeDto implements IEntityDto, IAssociableDto {
    private UUID entityId;
    private String entityType;
    @JsonIgnore
    private List<EventDto> events;

    @JsonIgnore
    private Map<String, Object> customFields;

    @JsonIgnore
    private String tableName;

    @JsonIgnore
    private Instant feePaidDate;
    @JsonIgnore
    private UUID orderId;
    @JsonIgnore
    private Instant createdDateTime;
    @JsonIgnore
    private Instant updatedDateTime;
    @JsonIgnore
    private String createdBy;
    @JsonIgnore
    private String updatedBy;

    private String feeCode;
    private String feeName;
    private String feeStatus;
    private BigDecimal feeAmount;
    private String comment;

    @JsonIgnore
    private List<EntityAssociation> associations;

    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId") && entry.getValue() != null)
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}
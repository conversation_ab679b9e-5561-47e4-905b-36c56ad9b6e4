package com.scube.licensing.features.association.delete_association;

import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerVoidAxon;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import jakarta.transaction.Transactional;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;

@Component
public class DeleteAssociationByEntityHandler implements IRequestHandlerVoidAxon<DeleteAssociationByEntityCommand> {
    private final AssociationRepository associationRepository;

    public DeleteAssociationByEntityHandler(AssociationRepository associationRepository) {
        this.associationRepository = associationRepository;
    }

    @Override
    @Transactional
    @CommandHandler
    public void handle(DeleteAssociationByEntityCommand command) {
        associationRepository.findByParentIdAndParentAssociationTypeAndChildIdAndChildAssociationType(
                command.parent().getId(),
                command.parent().getAssociationType(),
                command.child().getId(),
                command.child().getAssociationType()
        ).ifPresent(associationRepository::delete);
    }
}
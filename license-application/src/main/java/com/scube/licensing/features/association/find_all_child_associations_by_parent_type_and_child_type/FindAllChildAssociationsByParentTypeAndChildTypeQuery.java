package com.scube.licensing.features.association.find_all_child_associations_by_parent_type_and_child_type;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import org.springframework.util.ObjectUtils;

import java.util.List;


public record FindAllChildAssociationsByParentTypeAndChildTypeQuery(Associable parent,
                                                                    AssociationType childAssociationType) implements IRequestAxon<List<Association>> {
    public FindAllChildAssociationsByParentTypeAndChildTypeQuery {
        if (ObjectUtils.isEmpty(parent) || ObjectUtils.isEmpty(childAssociationType))
            throw new IllegalArgumentException("Parent and child association type are required");
    }
}
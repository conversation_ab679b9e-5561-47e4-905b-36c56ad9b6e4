package com.scube.licensing.features.entity_fee.rabbit;

import com.scube.licensing.features.entity_fee.EntityFeeService;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
@Slf4j
public class UpdateEntityFeesWithOrderCommandHandler extends FanoutListenerRpc<UpdateEntityFeesWithOrderCommandHandler.UpdateEntityFeesWithOrderCommand, Boolean> {
    private final EntityFeeService entityFeeService;

    public RabbitResult<Boolean> consume(UpdateEntityFeesWithOrderCommand event) {
        return RabbitResult.of(() -> {
                    event.entityFeeDtos()
                            .forEach(entityFeeDto -> entityFeeService.updateEntityFeeWithOrderId(entityFeeDto, event.orderId()));
                    return true;
                }
        );
    }

    // @formatter:off
    public record UpdateEntityFeesWithOrderCommand(List<EntityFeeDto> entityFeeDtos, UUID orderId) implements IRabbitFanoutSubscriberRpc<Boolean> { }
}
package com.scube.licensing.features.qr_codes.rabbit;

import com.scube.licensing.features.qr_codes.QrCodeService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class GenerateQrCodeCommandHandler extends FanoutListenerRpc<GenerateQrCodeCommand, GenerateQrCodeCommandResponse> {
    private final QrCodeService qrCodeService;

    public RabbitResult<GenerateQrCodeCommandResponse> consume(GenerateQrCodeCommand event) {
        return RabbitResult.of(() -> qrCodeService.getGenerateQrCodeCommandResponse(event));
    }
}
package com.scube.licensing.features.profile.get_profile_and_associations;

import com.scube.licensing.features.profile.dto.ProfileAndAssociationsDto;
import com.scube.licensing.features.profile.validation.CheckProfileTypeExists;
import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import jakarta.validation.constraints.NotBlank;

import java.util.UUID;


public record GetProfileAndAssociationsDtoQuery(
        @CheckProfileTypeExists
        @NotBlank(message = "Profile type is required")
        String profileType,

        @NotBlank(message = "EntityId is required")
        UUID entityId
) implements IRequestAxon<ProfileAndAssociationsDto> {}
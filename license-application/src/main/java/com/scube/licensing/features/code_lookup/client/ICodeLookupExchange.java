package com.scube.licensing.features.code_lookup.client;

import com.scube.licensing.features.code_lookup.CodeLookupController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PatchExchange;

public interface ICodeLookupExchange {
    @PatchExchange(
            value = "/code-lookup/tag-update"
    )
    void updateTag(@RequestBody CodeLookupController.TagUpdateRequest request);
}

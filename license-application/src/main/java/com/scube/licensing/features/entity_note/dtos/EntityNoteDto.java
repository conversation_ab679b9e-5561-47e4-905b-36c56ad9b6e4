package com.scube.licensing.features.entity_note.dtos;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.profile.dto.EventDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EntityNoteDto implements IEntityDto, IAssociableDto {
    private UUID entityId;
    private String entityType;
    private List<EventDto> events;

    private String note;
    private String createdBy;
    private Instant createdDate;
    private String lastModifiedBy;
    private Instant lastModifiedDate;

    @JsonIgnore
    private Map<String, Object> customFields;

    @JsonIgnore
    private String tableName;

    @JsonAnyGetter
    public Map<String, Object> getCustomFieldsMap() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}

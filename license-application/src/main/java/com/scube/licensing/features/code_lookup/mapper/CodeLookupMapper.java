package com.scube.licensing.features.code_lookup.mapper;

import com.scube.licensing.features.code_lookup.dto.CodeLookupResponseLineItem;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookup;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class CodeLookupMapper {
    @Mapping(target = "action", source = "action.value")
    public abstract CodeLookupResponseLineItem toDto(CodeLookup codeLookup);

    public abstract List<CodeLookupResponseLineItem> toDto(List<CodeLookup> codeLookups);

    @Mapping(target = "action", expression = "java(mapAction(codeLookupResponse))")
    public abstract CodeLookup toEntity(CodeLookupResponseLineItem codeLookupResponse);

    public abstract List<CodeLookup> toEntity(List<CodeLookupResponseLineItem> codeLookupResponses);

    protected CodeLookupActionEnum mapAction(CodeLookupResponseLineItem codeLookupResponse) {
        return CodeLookupActionEnum.fromValue(codeLookupResponse.getAction());
    }
}
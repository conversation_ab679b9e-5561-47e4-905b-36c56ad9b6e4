package com.scube.licensing.features.entity_fee.rabbit;

import com.scube.licensing.features.entity_fee.EntityFeeService;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@AllArgsConstructor
@Slf4j
public class UpdateEntityFeeWithOrderCommandHandler extends FanoutListenerRpc<UpdateEntityFeeWithOrderCommandHandler.UpdateEntityFeeWithOrderCommand, Boolean> {
    private final EntityFeeService entityFeeService;

    public RabbitResult<Boolean> consume(UpdateEntityFeeWithOrderCommand event) {
        return RabbitResult.of(() ->
                entityFeeService.updateEntityFeeWithOrderId(event.entityFeeDto(), event.orderId())
        );
    }

    // @formatter:off
    public record UpdateEntityFeeWithOrderCommand(EntityFeeDto entityFeeDto, UUID orderId) implements IRabbitFanoutSubscriberRpc<Boolean> { }
}
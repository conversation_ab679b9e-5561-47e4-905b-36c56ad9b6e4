package com.scube.licensing.features.entity_fee;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeRequest;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/entity-fee")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class EntityFeeController {
    private final EntityFeeService entityFeeService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @SwaggerInfo(summary = "Add or update a fee", description = "This is used to add a fee to an entity.")
    @RolesAllowed(Permissions.EntityFee.ADD_FEE)
    public EntityFeeDto addFee(@RequestBody EntityFeeRequest request) {
        return entityFeeService.createFee(request);
    }

    @PostMapping(value = "{feeEntityId}/add-file", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Add a file to a fee", description = "This is used to add a file to a fee of the participant.")
    @RolesAllowed(Permissions.EntityFee.ADD_FILE)
    public List<DocumentDto> addFile(@PathVariable UUID feeEntityId, @RequestParam Map<String, MultipartFile> files) {
        return entityFeeService.addFiles(feeEntityId, files);
    }

    @PostMapping("{feeEntityId}/add-association")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Add an association to a fee", description = "This is used to add an association to a fee.")
    @RolesAllowed(Permissions.EntityFee.ADD_ASSOCIATION)
    public EntityFeeDto addAssociation(@PathVariable UUID feeEntityId, @RequestBody EntityFeeRequest request) {
        return entityFeeService.addAssociation(feeEntityId, request);
    }

    @PutMapping("{feeEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Update a fee", description = "This is used to update a fee of the participant.")
    @RolesAllowed(Permissions.EntityFee.UPDATE_FEE)
    public EntityFeeDto updateFee(@PathVariable UUID feeEntityId, @RequestBody EntityFeeRequest request) {
        return entityFeeService.updateFee(feeEntityId, request);
    }

    @GetMapping("{entityType}/{entityId}/all")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get all fees of an entity", description = "This is used to get all fees of an entity.")
    @RolesAllowed(Permissions.EntityFee.GET_FEES)
    public GetAllEntityFeeResponse getFees(@PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        return entityFeeService.getAllFees(entityType, entityId);
    }

    @GetMapping("{feeEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get a fee of the participant", description = "This is used to get a fee of the participant.")
    @RolesAllowed(Permissions.EntityFee.GET_FEE)
    public EntityFeeDto getFee(@PathVariable UUID feeEntityId) {
        return entityFeeService.getFee(feeEntityId);
    }

    @DeleteMapping("{feeEntityId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Delete a fee of the participant", description = "This is used to delete a fee of the participant.")
    @RolesAllowed(Permissions.EntityFee.DELETE_FEE)
    public void deleteFee(@PathVariable UUID feeEntityId) {
        entityFeeService.deleteFee(feeEntityId);
    }

    @PostMapping("{feeEntityId}/resume-recurring")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Resume a recurring fee", description = "This is used to resume a recurring fee.")
    @RolesAllowed(Permissions.EntityFee.RESUME_RECURRING_FEE)
    public void resumeRecurringFee(@PathVariable UUID feeEntityId) {
        entityFeeService.resumeRecurringFee(feeEntityId);
    }

    @PostMapping("{feeEntityId}/pause-recurring")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @SwaggerInfo(summary = "Pause a recurring fee", description = "This is used to pause a recurring fee.")
    @RolesAllowed(Permissions.EntityFee.PAUSE_RECURRING_FEE)
    public void pauseRecurringFee(@PathVariable UUID feeEntityId) {
        entityFeeService.pauseRecurringFee(feeEntityId);
    }
}

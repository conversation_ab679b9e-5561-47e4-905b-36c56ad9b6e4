package com.scube.licensing.features.profile.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.infrastructure.db.entity.custom_entity.CustomEntityInstance;
import lombok.Data;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
public class CustomEntityInstanceDto implements IAssociableDto {
    private UUID entityId;
    private List<EventDto> events;
    private String entityNumber;
    private String type;
    private String subtype;
    @JsonIgnore
    private String tableName;

    private static EventMapper mapper = Mappers.getMapper(EventMapper.class);

    public static CustomEntityInstanceDto of(CustomEntityInstance customEntityInstance) {
        CustomEntityInstanceDto customEntityInstanceDto = new CustomEntityInstanceDto();
        customEntityInstanceDto.setEntityId(customEntityInstance.getUuid());
        customEntityInstanceDto.setEntityNumber(customEntityInstance.getEntityNumber());
        customEntityInstanceDto.setType(customEntityInstance.getCustomEntitySubType().getCustomEntityType().getName());
        customEntityInstanceDto.setSubtype(customEntityInstance.getCustomEntitySubType().getName());
        customEntityInstanceDto.setEvents(mapper.toDto(customEntityInstance.getEvents()));
        return customEntityInstanceDto;
    }

    @Override
    public void putProperties(Map<String, Object> properties) {

    }
}
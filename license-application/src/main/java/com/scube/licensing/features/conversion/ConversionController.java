package com.scube.licensing.features.conversion;

import com.scube.licensing.features.conversion.services.PaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;


@Slf4j
@RequiredArgsConstructor
public class ConversionController {
    private final PaymentService paymentService;

    /**
     * DR: Keeping this here for now, encase we get another bass customer for conversion.
     */
    @PostMapping("/payments")
    //@RolesAllowed(Permissions.Conversion.PROCESS_PAYMENTS)
    public void processPayments() {
        log.info("Starting payment process");
        paymentService.processPayments();
        log.info("Finished payment process");
    }
}
package com.scube.licensing.features.settings.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotEmpty;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CreateAppPropertyRequest {
    @NotEmpty
    private String name;
    @NotEmpty
    private String value;
    private String description;
}

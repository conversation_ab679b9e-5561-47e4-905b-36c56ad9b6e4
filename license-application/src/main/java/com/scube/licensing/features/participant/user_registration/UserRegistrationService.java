package com.scube.licensing.features.participant.user_registration;

import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.participant.dto.CreateParticipantRequestDto;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserRegistrationService {
    private final ParticipantService participantService;
    private final AxonGateway axonGateway;

    public void registerUser(UserRegistrationEvent message) {
        log.info("New User Registration: {}", message);

        //check if user exists as a resident
        var firstName = message.getFirstName();
        var lastName = message.getLastName();
        var email = message.getEmail();
        var userId = message.getUserId();

        var uuids = axonGateway.query(new ParticipantService.GetSearchIndividualQuery(email));

        log.info("Found participants {} with email: {}", uuids, email);

        //if not, create a new resident
        if (ObjectUtils.isEmpty(uuids)) {
            log.info("Creating new participant with info: {} {} {}", firstName, lastName, email);
            HashedMap<String, String> requestMap = new HashedMap<>();
            requestMap.put("firstName", Optional.ofNullable(firstName).orElse("Unknown"));
            requestMap.put("lastName", Optional.ofNullable(lastName).orElse("Unknown"));
            requestMap.put("email", email);

            var createResult = participantService.createResident(
                    new CreateParticipantRequestDto(requestMap).toStringMap(),
                    Map.of()
            );
            uuids = Collections.singletonList(createResult.getEntityId());
            log.info("Created participant with id: {}", uuids.getFirst());
        }

        log.info("Updating participant with entityId: {} and keycloak userId: {}", uuids.getFirst(), userId);
        Participant participant = participantService.getParticipantOrElseThrow(uuids.getFirst());
        participant.setUuid(userId);
        participantService.save(participant);
        log.info("Done updating participant with entityId: {} and keycloak userId: {}", uuids.getFirst(), userId);
    }
}

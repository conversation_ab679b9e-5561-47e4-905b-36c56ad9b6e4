package com.scube.licensing.features.participant.get_participant_group_by_name;

import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantGroup;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantGroupRepository;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;

@Component
public class GetParticipantGroupByNameHandler implements IRequestHandlerAxon<GetParticipantGroupByNameQuery, ParticipantGroup> {
    private final ParticipantGroupRepository participantGroupRepository;

    public GetParticipantGroupByNameHandler(ParticipantGroupRepository participantGroupRepository) {
        this.participantGroupRepository = participantGroupRepository;
    }

    @Override
    @QueryHandler
    public ParticipantGroup handle(GetParticipantGroupByNameQuery query) {
        return participantGroupRepository.findByNameIgnoreCase(query.name());
    }
}
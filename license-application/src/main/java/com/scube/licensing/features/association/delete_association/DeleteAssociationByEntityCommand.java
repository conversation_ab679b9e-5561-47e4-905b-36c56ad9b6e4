package com.scube.licensing.features.association.delete_association;

import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;

public record DeleteAssociationByEntityCommand(Associable parent, Associable child) implements IRequestVoidAxon {
    public DeleteAssociationByEntityCommand {
        if (parent == null || parent.getId() == null)
            throw new IllegalArgumentException("parent cannot be null");
        if (child == null || child.getId() == null)
            throw new IllegalArgumentException("child cannot be null");
    }
}
package com.scube.licensing.features.participant.events;

import com.scube.audit.auditable.events.AuditableEntityPropertySetEvent;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import static com.scube.licensing.infrastructure.db.entity.association.Associable.RejectedFieldsAssociable.REJECTED_FIELDS_PROPERTY;

@Slf4j
@Component
@RequiredArgsConstructor
public class RemoveRejectedFieldsDomainEventHandler {
    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    public void handleRejectFields(AuditableEntityPropertySetEvent event) {
        if (event.getPropertyKey().equalsIgnoreCase(REJECTED_FIELDS_PROPERTY)) return;
        if (event.getEntity() instanceof Associable associable) {
            log.info("Removing rejected field: {}", event.getPropertyKey());
            associable.rejectedFields().remove(event.getPropertyKey());
            log.info("Removed rejected field: {}", event.getPropertyKey());
        }
    }
}
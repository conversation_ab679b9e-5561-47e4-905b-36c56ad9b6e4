package com.scube.licensing.features.document.service;

import com.scube.licensing.infrastructure.db.entity.document.Document;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import org.hibernate.envers.AuditReader;
import org.hibernate.envers.AuditReaderFactory;
import org.hibernate.envers.query.AuditEntity;
import org.hibernate.envers.query.AuditQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class DocumentHistoryService {
    private final EntityManager entityManager;

    /**
     * Get the history of a document by its entity ID.
     * @param entityId
     * @param pageable
     * @return
     */
    public Page<Document> getDocumentHistoryByEntityId(UUID entityId, Pageable pageable) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(Document.class, false, true)
                .add(AuditEntity.property("entityId").eq(entityId))
                .setFirstResult((int) pageable.getOffset())
                .setMaxResults(pageable.getPageSize());

        List<Object[]> results = query.getResultList();

        List<Document> transformedResults = results.stream()
                .map(result -> (Document) result[0])
                .collect(Collectors.toList());


        long totalRevisions = countRevisions("entityId", entityId);

        return new PageImpl<>(transformedResults, pageable, totalRevisions);
    }

    /**
     * Get the history of a document by its ID.
     * @param documentId
     * @param pageable
     * @return
     */
    public Page<Document> getDocumentHistoryById(Long documentId, Pageable pageable) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);

        AuditQuery query = auditReader.createQuery()
                .forRevisionsOfEntity(Document.class, false, true)
                .add(AuditEntity.id().eq(documentId))
                .setFirstResult((int) pageable.getOffset())
                .setMaxResults(pageable.getPageSize());

        List<Object[]> results = query.getResultList();

        List<Document> transformedResults = results.stream()
                .map(result -> (Document) result[0])
                .collect(Collectors.toList());

        long totalRevisions = countRevisions("id", documentId);

        return new PageImpl<>(transformedResults, pageable, totalRevisions);
    }

    private long countRevisions(String propertyName, Object property) {
        AuditReader auditReader = AuditReaderFactory.get(entityManager);

        Number count = (Number) auditReader.createQuery()
                .forRevisionsOfEntity(Document.class, false, true)
                .addProjection(AuditEntity.id().count())
                .add(AuditEntity.property(propertyName).eq(property))
                .getSingleResult();

        return count.longValue();
    }
}


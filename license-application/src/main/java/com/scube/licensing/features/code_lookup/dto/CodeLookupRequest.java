package com.scube.licensing.features.code_lookup.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.util.ObjectUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CodeLookupRequest {
    @Size(max = 255)
    private String entityType;

    @Size(max = 255)
    private String entityId;

    @Nullable
    @Getter(AccessLevel.NONE)
    private String action;

    public CodeLookupActionEnum getAction() {
        if (ObjectUtils.isEmpty(action)) return CodeLookupActionEnum.LOOKUP;
        return CodeLookupActionEnum.fromValue(action);
    }
}
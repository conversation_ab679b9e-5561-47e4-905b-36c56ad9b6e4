package com.scube.licensing.features.association.delete_association;

import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

@Slf4j
@Component
@RequiredArgsConstructor
public class DeleteAssociationDomainEventHandler {
    private final AxonGateway axonGateway;
    private final ProfileService profileService;

    @Async // DR: Won't work without this
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleDeleteAssociation(DeleteAssociationDomainEvent event) {
        log.info("Deleting association between parent: {} and child: {}", event.getParent(), event.getChild());
        if (event.getParent() == null) {
            event.setParent(profileService.getProfileOrElseThrow(event.getParentEntityType(), event.getParentEntityId()));
        }

        if (event.getChild() == null) {
            event.setChild(profileService.getProfileOrElseThrow(event.getChildEntityType(), event.getChildEntityId()));
        }

        axonGateway.sendAndWait(new DeleteAssociationByEntityCommand(event.getParent(), event.getChild()));
        log.info("Done deleting association between parent: {} and child: {}", event.getParent(), event.getChild());
    }
}
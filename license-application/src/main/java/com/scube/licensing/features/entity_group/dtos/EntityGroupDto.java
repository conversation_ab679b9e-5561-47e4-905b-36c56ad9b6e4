package com.scube.licensing.features.entity_group.dtos;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.profile.dto.EventDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EntityGroupDto implements IEntityDto, IAssociableDto {
    private String groupName;
    private UUID entityId;
    private String entityType;
    private List<EventDto> events;

    @JsonIgnore
    private Map<String, Object> customFields;

    @JsonIgnore
    private String tableName;

    private Instant createdDateTime;
    private Instant updatedDateTime;
    private String createdBy;
    private String updatedBy;

    private List<EntityAssociation> associations;

    private GetAllEntityFeeResponse fees;

    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}
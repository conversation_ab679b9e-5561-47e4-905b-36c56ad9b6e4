package com.scube.licensing.features.participant.swagger;

import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.lib.misc.annotations.swagger.SwaggerParameter;
import com.scube.lib.misc.annotations.swagger.SwaggerRequestBody;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@SwaggerInfo(summary = "Update the phone or email of a participant", description = "This is used to update the phone or email of a participant.")
@SwaggerParameter(name = "entityId", description = "This can be the resident or the dog entity id", required = true)
@SwaggerRequestBody(
        description = "The request body should be a map of key value pairs. The key should be the contactId and the value should be a phone number or email.",
        content = @Content(examples = {
                @ExampleObject(value = """
                        {
                            "21": "(*************",
                            "23": "<EMAIL>"
                        }
                        """
                )
        })
)
public @interface Swagger_ParticipantController_UpdateParticipantContact {
}

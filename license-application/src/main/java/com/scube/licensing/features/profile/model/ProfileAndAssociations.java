package com.scube.licensing.features.profile.model;


import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.Deletable;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public final class ProfileAndAssociations {
    private final Associable profile;
    private final List<Associable> associables;

    public ProfileAndAssociations(Associable profile, List<Associable> associations) {
        this.profile = profile;
        this.associables = associations;
    }

    public ProfileAndAssociations(Associable profile) {
        this.profile = profile;
        this.associables = profile.getChildAssociables().stream()
                .filter(childEntity -> !(childEntity instanceof Deletable deletable) || !deletable.isDeleted())
                .toList();
    }

    public Set<Association> getAssociations() {
        return profile.getChildAssociations();
    }
}
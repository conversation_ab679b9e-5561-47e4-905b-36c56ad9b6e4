package com.scube.licensing.features.qr_codes.dto;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.UUID;

@Data
public class QrCodeResponse {
    private UUID qrCodeDocumentUUID;
    private MultipartFile file;

    public QrCodeResponse(UUID qrCodeDocumentUUID) {
        this.qrCodeDocumentUUID = qrCodeDocumentUUID;
    }

    public QrCodeResponse(UUID qrCodeDocumentUUID, MultipartFile file) {
        this.qrCodeDocumentUUID = qrCodeDocumentUUID;
        this.file = file;
    }

    public QrCodeResponse(MultipartFile file) {
        this.file = file;
    }
}
package com.scube.licensing.features.entity_group.dtos;



import com.scube.licensing.features.entity_fee.dtos.Totals;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.*;

@Data
@NoArgsConstructor
public class CustomFeeDto {
    private List<FeeSetDto> feeSets = new ArrayList<>();
    private Totals totals = new Totals();

    public void calculateTotals() {
        Totals totalSum = new Totals();
        for (FeeSetDto feeSetDto : feeSets) {
            if (feeSetDto.getTotals() != null) {
                totalSum.add(feeSetDto.getTotals());
            }
        }

        this.totals = totalSum;
    }

}

package com.scube.licensing.features.participant.dog.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DogSighting implements Serializable {
    private Location location;
    private ReportedBy reportedBy;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Location implements Serializable {
        private String name;
        private Instant datetime;
        private String location;
        private String latitude;
        private String longitude;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReportedBy implements Serializable {
        private String name;
        private String email;
        private String phone;
    }
}


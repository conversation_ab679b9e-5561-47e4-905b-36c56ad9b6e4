package com.scube.licensing.features.code_lookup.rabbit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.licensing.features.code_lookup.dto.CodeLookupRequest;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;

@JsonIgnoreProperties(ignoreUnknown = true)
public class GetGeneratedCodeQuery extends CodeLookupRequest implements IRabbitFanoutSubscriberRpc<GetGeneratedCodeQueryResponse> {
}
package com.scube.licensing.features.qr_codes;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.features.qr_codes.dto.GenerateMultipleQrCodeCommand;
import com.scube.licensing.features.qr_codes.dto.QrCodeRequest;
import com.scube.licensing.features.qr_codes.dto.QrCodeResponse;
import com.scube.licensing.features.qr_codes.rabbit.GenerateQrCodeCommand;
import com.scube.licensing.features.qr_codes.rabbit.GenerateQrCodeCommandResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequestMapping("/qr-codes")
@RequiredArgsConstructor
@Validated
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
public class QrCodeController {
    private final QrCodeService qrCodeService;

    @PostMapping("/generate-and-upload")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.QrCode.GENERATE_AND_UPLOAD)
    public QrCodeResponse generateAndUpload(@RequestBody @Valid QrCodeRequest qrCodeRequest) {
        return qrCodeService.generateByEntityAndUpload(qrCodeRequest.getEntityType(), qrCodeRequest.getEntityId(), qrCodeRequest.getAction());
    }

    @PostMapping("/generate-no-upload")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.QrCode.GENERATE_QR_CODE_NO_UPLOAD)
    public ResponseEntity<Resource> generateQrCodeNoUpload(@RequestBody @Valid QrCodeRequest qrCodeRequest) {
        var image = qrCodeService.generateByEntityNoUpload(qrCodeRequest.getEntityType(), qrCodeRequest.getEntityId(), qrCodeRequest.getAction());
        return getResourceResponseEntity(image.getFile());
    }

    @PostMapping("/generate-by-code/{code}")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.QrCode.GENERATE_BY_CODE)
    public ResponseEntity<Resource> generateByCode(@PathVariable @Size(max = 255) @NotBlank String code) {
        var image = qrCodeService.generateByCode(code);
        return getResourceResponseEntity(image.getFile());
    }

    @PostMapping("/generate")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.QrCode.GENERATE_WITH_TEXT)
    public ResponseEntity<Resource> generateWithText(@RequestBody @Valid QrCodeTextRequest request) {
        var image = qrCodeService.generateByText(request.qrCodeText());
        return getResourceResponseEntity(image.getFile());
    }

    @PostMapping("/generate-batch")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.QrCode.GENERATE_BATCH)
    public Flux<GenerateQrCodeCommandResponse> generateBatch(@RequestBody GenerateMultipleQrCodeCommand command) {
        List<GenerateQrCodeCommand> requests = command.getRequests();
        if (ObjectUtils.isEmpty(requests)) return Flux.empty();
        return Flux.fromIterable(requests)
                .flatMap(request -> Mono.fromFuture(() -> qrCodeService.getGenerateQrCodeCommandResponseAsync(request)));
    }

    private static @NotNull ResponseEntity<Resource> getResourceResponseEntity(MultipartFile file) {
        var image = file.getResource();
        return ResponseEntity.ok()
                .contentType(MediaType.IMAGE_PNG)
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + image.getFilename() + "\"")
                .body(image);
    }

    // @formatter:off
    public record QrCodeTextRequest(@NotBlank String qrCodeText) {}
}
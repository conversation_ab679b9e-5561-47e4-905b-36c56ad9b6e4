package com.scube.licensing.features.merge_request.events;

import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class MergeRequestDomainEventHandler {
    private final AmqpGateway amqpGateway;

    @EventListener
    public void handleApprovedMergeRequest(MergeRequestApprovedEvent event) {
        amqpGateway.publish(event);
    }

    @EventListener
    public void handleRejectedMergeRequest(MergeRequestRejectedEvent event) {
        amqpGateway.publish(event);
    }
}
package com.scube.licensing.features.settings.entity;

import com.scube.audit.auditable.entity.AuditableEntityWithoutID;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.envers.Audited;

import java.util.UUID;

@Entity
@Table(name = AppProperty.TABLE_NAME)
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Audited
public class AppProperty extends AuditableEntityWithoutID<UUID> {
    public static final String TABLE_NAME = "app_properties";

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Size(max = 100)
    @Column(unique = true, updatable = false, nullable = false, length = 100)
    private String name;

    @Size(max = 250)
    @Column(nullable = false, length = 250, name = "property_value")
    private String value;

    @Size(max = 250)
    @Column(length = 250)
    private String description;
}
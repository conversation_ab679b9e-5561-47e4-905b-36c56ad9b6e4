package com.scube.licensing.features.license.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class LicenseActivityFeeUpdateRequest {
    @Min(0)
    private BigDecimal feeAmount;
    private String reason;
}
package com.scube.licensing.features.search.by_parcel;

import com.scube.licensing.features.address.service.AddressService;
import com.scube.licensing.features.participant.dto.SearchResponseDTO;
import com.scube.licensing.features.profile.get_profile_header.GetProfileHeaderQuery;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;

@Component
public class SearchByParcelHandler implements IRequestHandlerAxon<SearchByParcelQuery, SearchByParcelResponse> {
    private final AxonGateway axonGateway;

    public SearchByParcelHandler(AxonGateway axonGateway) {
        this.axonGateway = axonGateway;
    }

    @Override
    @QueryHandler
    public SearchByParcelResponse handle(SearchByParcelQuery command) {
        var map = command.jsonBodyMap();
        if (ObjectUtils.isEmpty(map) || map.isEmpty())
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Search text is required");

        var hasSearchText = map.entrySet().stream().anyMatch(entry -> !ObjectUtils.isEmpty(entry.getValue()));
        if (!hasSearchText)
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Search text is required");


        var arrayObject = new ArrayList<Map<String, Object>>();
        var result = new SearchByParcelResponse(arrayObject);

        var addressIds = axonGateway.query(
                new AddressService.QueryAddressByFieldsQuery(
                        map.getOrDefault("parcelNumber", "").toString(),
                        map.getOrDefault("lotNumber", "").toString(),
                        map.getOrDefault("blockNumber", "").toString(),
                        map.getOrDefault("subdivision", "").toString(),
                        map.getOrDefault("address", "").toString(),
                        map.getOrDefault("address2", "").toString(),
                        map.getOrDefault("city", "").toString(),
                        map.getOrDefault("state", "").toString(),
                        map.getOrDefault("zip", "").toString()
                )
        );

        for (UUID entityId : addressIds) {
            var searchResponseDTO = new SearchResponseDTO();
            searchResponseDTO.setEntityId(entityId);
            searchResponseDTO.setEntityType("parcel");

            var headerParticipantMap = axonGateway.query(new GetProfileHeaderQuery("parcel", entityId)).header();

            arrayObject.add(headerParticipantMap);
        }

        return result;
    }
}
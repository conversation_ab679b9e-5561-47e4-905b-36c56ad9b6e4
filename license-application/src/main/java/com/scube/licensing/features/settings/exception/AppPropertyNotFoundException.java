package com.scube.licensing.features.settings.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class AppPropertyNotFoundException extends RuntimeException {
    public AppPropertyNotFoundException() {
    }

    public AppPropertyNotFoundException(String message) {
        super(message);
    }

    public AppPropertyNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    public AppPropertyNotFoundException(Throwable cause) {
        super(cause);
    }
}

package com.scube.licensing.features.code_lookup.rabbit;

import com.scube.licensing.features.code_lookup.CodeLookupService;
import com.scube.multi.tenant.TenantContext;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class GetGeneratedCodeQueryHandler extends FanoutListenerRpc<GetGeneratedCodeQuery, GetGeneratedCodeQueryResponse> {
    private final CodeLookupService codeLookupService;

    public RabbitResult<GetGeneratedCodeQueryResponse> consume(GetGeneratedCodeQuery event) {
        return RabbitResult.of(() -> getGeneratedCode(event));
    }

    private GetGeneratedCodeQueryResponse getGeneratedCode(GetGeneratedCodeQuery event) {
        var code = codeLookupService.createOrLookup(event.getEntityType(), event.getEntityId(), event.getAction(), TenantContext.getTenantId());
        return new GetGeneratedCodeQueryResponse(code);
    }
}
package com.scube.licensing.features.entity_note.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.features.entity.EntityRequest;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.infrastructure.db.entity.entity_note.EntityNote;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto.createCustomField;

public class EntityNoteRequest extends EntityRequest<EntityNoteRequest> {
    public EntityNoteRequest(Map<String, String> map) {
        super(map, EntityNote.TABLE_NAME);
    }

    @JsonProperty("note")
    public String getNote() {
        return getAndValidate("note");
    }

    @Override
    public List<CreateCustomFieldsDto> getCustomFields() {
        var result = new ArrayList<CreateCustomFieldsDto>();

        getAdditionalFields()
                .forEach((key, value) -> result.add(createCustomField("object", EntityNote.TABLE_NAME, key, value)));

        return result;
    }

}

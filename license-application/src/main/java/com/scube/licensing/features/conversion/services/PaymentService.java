package com.scube.licensing.features.conversion.services;

import com.scube.lib.misc.dates.DateUtils;
import com.scube.licensing.features.license.LicenseActivityService;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivity;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivityFee;
import com.scube.licensing.infrastructure.db.repository.license.LicenseRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class PaymentService {
    private final LicenseRepository licenseRepository;
    private final JdbcTemplate jdbcTemplate;
    private final LicenseActivityService licenseActivityService;

    /**
     * DR: Keeping this here for now, encase we get another bass customer for conversion.
     */
    public void processPayments() {
        List<License> licenses = licenseRepository.findAll();

        var licActivityTransactionQuery = """
                select la.license_activity_id, lt.recid from license_activity la
                inner join cx_dogs_license_history lh
                on lh.unique_id = la.conversion_reference::uuid
                inner join cx_license_transaction lt
                on lt.recid::int = lh.transaction_rec_id
                """;
        var licActivityTransactionList = jdbcTemplate.queryForList(licActivityTransactionQuery);
        var licActivityTransactionMap = new HashMap<Long, Set<Long>>();
        for (var item : licActivityTransactionList) {
            var licActivityId = convertToString(item.getOrDefault("license_activity_id", ""));
            var trxRecId = convertToString(item.getOrDefault("recid", ""));
            licActivityTransactionMap.computeIfAbsent(Long.valueOf(trxRecId), k -> new HashSet<>()).add(Long.valueOf(licActivityId));
        }

        int i = 0;
        for (var license : licenses) {
            if (i++ % 10 == 0) {
                log.info("Processing license " + i + " of " + licenses.size());
            }

            var recId = license.getConversionReference();

            var query = "SELECT recid, statutoryfee, localfee, seniordiscount, unspayedneuteredfee, spayedneutered, transactiondate, exempt::boolean, licenseperiod " +
                        "FROM public.cx_license_transaction " +
                        "where dogs_recid = ? " +
                        "and transactiontype not in ('G') " +
                        "and IsVoided::boolean = false " +
                        "order by transactiondate::timestamp asc";
            var trxList = jdbcTemplate.queryForList(query, recId);

            for (var item : trxList) {
                var trxRecId = convertToString(item.getOrDefault("recid", ""));
                var origStatutoryFee = new BigDecimal(convertToString(item.getOrDefault("statutoryfee", "0"), "0"));
                var origLocalFee = new BigDecimal(convertToString(item.getOrDefault("localfee", "0"), "0"));
                var origSeniorDiscount = new BigDecimal(convertToString(item.getOrDefault("seniordiscount", "0"), "0"));
                var origUnSpayedNeuteredFee = new BigDecimal(convertToString(item.getOrDefault("unspayedneuteredfee", "0"), "0"));
                var isSpayedNeutered = convertToString(item.getOrDefault("spayedneutered", "0")).equals("1");
                var transactionDate = DateUtils.toInstant(convertToString(item.getOrDefault("transactiondate", "")));
                var exempt = Boolean.valueOf(convertToString(item.getOrDefault("exempt", "false")));
                var licensePeriod = new BigDecimal((convertToString(item.getOrDefault("licenseperiod", "0"))));
                if (licensePeriod.compareTo(BigDecimal.ZERO) == 0) licensePeriod = BigDecimal.ONE;

                var activities = licActivityTransactionMap.get(Long.valueOf(trxRecId));
                if (ObjectUtils.isEmpty(activities)) continue;
                var seniorDiscount = origSeniorDiscount.divide(licensePeriod, 2, BigDecimal.ROUND_HALF_UP);
                var unSpayedNeuteredFee = origUnSpayedNeuteredFee.divide(licensePeriod, 2, BigDecimal.ROUND_HALF_UP);
                var statutoryFee = origStatutoryFee.divide(licensePeriod, 2, BigDecimal.ROUND_HALF_UP);
                var localFee = origLocalFee.divide(licensePeriod, 2, BigDecimal.ROUND_HALF_UP);
                var total = statutoryFee.add(localFee);

                for (var activityId : activities) {
                    var activity = findActivity(license, activityId);
                    if (activity == null) continue;

                    activity.addLicenseFee(
                            Boolean.FALSE.equals(isSpayedNeutered) ? "DL-S-UNALT" : "DL-S-ALT",
                            unSpayedNeuteredFee,
                            LicenseActivityFee.PaymentStatus.PAID,
                            transactionDate
                    );

                    if (Boolean.FALSE.equals(isSpayedNeutered)) {
                        //dog is unspayed
                        //if total is greater than 5.00
                        if (total.compareTo(new BigDecimal("5.00")) >= 0) {
                            activity.addLicenseFee("DL-M-UNALT", BigDecimal.valueOf(5), LicenseActivityFee.PaymentStatus.PAID, transactionDate);
                            total = total.subtract(BigDecimal.valueOf(5));
                        }
                    }

                    //if seniordiscount is greater than 0
                    if (seniorDiscount.compareTo(BigDecimal.ZERO) < 0) {
                        activity.addLicenseFee("DL-M-SENIOR", seniorDiscount, LicenseActivityFee.PaymentStatus.PAID, transactionDate);
                    }

                    //if total >= 2.50
                    if (total.compareTo(new BigDecimal("2.50")) >= 0) {
                        activity.addLicenseFee("DL-M-DATA", BigDecimal.valueOf(2.50), LicenseActivityFee.PaymentStatus.PAID, transactionDate);
                        total = total.subtract(BigDecimal.valueOf(2.50));
                    }
                    activity.addLicenseFee("DL-M-LICENSE", total, LicenseActivityFee.PaymentStatus.PAID, transactionDate);

                    if (Boolean.TRUE.equals(exempt)) {
                        licenseActivityService.removeAndReIssueFees(license, activity, false);
                    }
                }
            }

            licenseRepository.save(license);
        }
    }

    private LicenseActivity findActivity(License license, Long activityId) {
        if (ObjectUtils.isEmpty(license.getLicenseActivities()))
            return null;

        return license.getLicenseActivities().stream()
                .filter(x -> x.getId().equals(activityId))
                .findFirst()
                .orElse(null);
    }


    public String convertToString(Object value) {
        return convertToString(value, "");
    }

    public String convertToString(Object value, String nullString) {
        if (ObjectUtils.isEmpty(value)) {
            return nullString;
        }
        return value.toString();
    }
}

package com.scube.licensing.features.participant.validation;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.participant.user_registration.UserRegistrationEvent;
import com.scube.licensing.features.participant.user_registration.UserRegistrationService;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.UUID;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CreateParticipantIfNotExists.CheckParticipantExistValidator.class)
public @interface CreateParticipantIfNotExists {
    String message() default "owner does not exist. Consider creating one first.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Slf4j
    @RequiredArgsConstructor
    class CheckParticipantExistValidator implements ConstraintValidator<CreateParticipantIfNotExists, OpenidClaimSet> {
        private final AxonGateway axonGateway;
        private final UserRegistrationService userRegistrationService;

        @Override
        public void initialize(CreateParticipantIfNotExists constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(OpenidClaimSet claimSet, ConstraintValidatorContext constraintValidatorContext) {
            var entityId = UUID.fromString(claimSet.getSubject());
            var exists = axonGateway.query(new ParticipantService.CheckParticipantExistsByEntityIdQuery(entityId));
            if (Boolean.FALSE.equals(exists)) {
                var event = new UserRegistrationEvent(claimSet.getGivenName(), claimSet.getFamilyName(), claimSet.getEmail(), entityId);
                try {
                    userRegistrationService.registerUser(event);
                } catch (Exception e) {
                    log.error("Failed to create participant for user registration event: {}", event, e);
                    throw e;
                }
            }
            return true;
        }
    }
}
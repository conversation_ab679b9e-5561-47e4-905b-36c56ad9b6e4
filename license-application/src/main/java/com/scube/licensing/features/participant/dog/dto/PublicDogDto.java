package com.scube.licensing.features.participant.dog.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
public class PublicDogDto {
      private Dog dog;
      private License license;
      private Object contacts;

      @JsonProperty("sightings")
      public Object getSightings() {
            var sightings = getDog().getCustomFields().get("sightings");
            return sightings == null ? List.of() : sightings;
      };

      @Data
      @AllArgsConstructor
      public static class Dog {
            private static List<String> view =
                    List.of(
                            "dogSex",
                            "dogName",
                            "dogBreed",
                            "tagNumber",
                            "dogBio",
                            "dogBirthDate",
                            "dogPrimaryColor",
                            "dogSecondaryColor"
                    );

            @JsonIgnore
            private Map<String, Object> customFields;
            private UUID uuid;
            private Boolean lost;
            private byte[] avatar;

            @JsonAnyGetter
            public Map<String, Object> getFlattenedFields() {
                  Map<String, Object> flattenedFields = new HashMap<>();
                  // Ensure all fields in the view are added
                  view.forEach(field -> {
                        flattenedFields.put(field, getCustomFieldsView().getOrDefault(field, null));
                  });
                  return flattenedFields;
            }

            @JsonIgnore
            public Map<String, Object> getCustomFieldsView() {
                  if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
                  return customFields.entrySet().stream()
                          .filter(entry -> view.contains(entry.getKey()))
                          .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
            }
      }

      @Data
      @AllArgsConstructor
      public static class License {
            private String licenseStatus;
      }
}


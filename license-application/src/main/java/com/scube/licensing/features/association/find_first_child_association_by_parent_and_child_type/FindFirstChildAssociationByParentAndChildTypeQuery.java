package com.scube.licensing.features.association.find_first_child_association_by_parent_and_child_type;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import org.springframework.util.ObjectUtils;

import java.util.Optional;

public record FindFirstChildAssociationByParentAndChildTypeQuery(Associable parent,
                                                                 AssociationType childAssociationType) implements IRequestAxon<Optional<Association>> {
    public FindFirstChildAssociationByParentAndChildTypeQuery {
        if (ObjectUtils.isEmpty(parent) || ObjectUtils.isEmpty(childAssociationType))
            throw new IllegalArgumentException("Parent is required");
    }
}
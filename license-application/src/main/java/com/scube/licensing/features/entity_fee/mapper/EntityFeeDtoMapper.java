package com.scube.licensing.features.entity_fee.mapper;

import com.scube.calculation.model.gen_dto.Fee;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.features.user.UserService;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import java.util.List;

@Slf4j
@Mapper(componentModel = "spring", uses = {EventMapper.class})
public abstract class EntityFeeDtoMapper {

    @Autowired
    private Environment env;

    @Autowired
    private AmqpGateway amqpGateway;

    @Autowired
    private UserService userService;

    @Mapping(target = "entityId", source = "uuid")
    @Mapping(target = "entityType", constant = EntityFee.ENTITY_TYPE)
    @Mapping(target = "tableName", constant = "fees")
    @Mapping(target = "feeStatus", source = "paymentStatus.key")
    @Mapping(target = "feeAmount", source = "amount")
    @Mapping(target = "feePaidDate", source = "paidDate")
    @Mapping(target = "feeName", expression = "java(getFeeName(entityFee))")
    @Mapping(target = "feeCode", source = "feeCode")
    @Mapping(target = "createdDateTime", source = "createdDate")
    @Mapping(target = "updatedDateTime", source = "lastModifiedDate")
    @Mapping(target = "createdBy", expression = "java(getCreatedByName(entityFee))")
    @Mapping(target = "updatedBy", expression = "java(getUpdatedByName(entityFee))")
    @Mapping(target = "associations", expression = "java(getAssociations(entityFee))")
    @Mapping(target = "customFields", source = "properties")
    public abstract EntityFeeDto toDto(EntityFee entityFee);

    public abstract List<EntityFeeDto> toDto(List<EntityFee> entityFees);

    public String getFeeName(EntityFee entityFee) {
        log.info("Getting fee name for entity fee: {}", entityFee);
        var isTestProfile = env.getActiveProfiles().length > 0 && env.getActiveProfiles()[0].equals("test");
        if (isTestProfile) return "Unknown Fee";
        var fees = amqpGateway.queryResult(new GetFeesQuery(List.of(entityFee.getFeeCode()))).orElseThrow();
        if (fees.fees().isEmpty()) return "Unknown Fee";
        return fees.fees().getFirst().getFeeName();
    }

    public List<EntityAssociation> getAssociations(EntityFee entityFee) {
        return entityFee.getParentAssociables().stream()
                .map(EntityAssociation::new)
                .toList();
    }

    public String getCreatedByName(EntityFee entityFee) {
        var username = entityFee.getCreatedBy();
        var isTestProfile = env.getActiveProfiles().length > 0 && env.getActiveProfiles()[0].equals("test");
        if (isTestProfile) return username;
        return userService.getUserNameByUsername(username);
    }

    public String getUpdatedByName(EntityFee entityFee) {
        var username = entityFee.getLastModifiedBy();
        var isTestProfile = env.getActiveProfiles().length > 0 && env.getActiveProfiles()[0].equals("test");
        if (isTestProfile) return username;
        return userService.getUserNameByUsername(username);
    }

    // @formatter:off
    public record GetFeesQuery(List<String> feeKeys) implements IRabbitFanoutPublisherRpc<GetFeesQueryResponse> { }
    public record GetFeesQueryResponse(List<Fee> fees) { }
}
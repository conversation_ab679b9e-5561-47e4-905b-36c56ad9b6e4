package com.scube.licensing.features.participant.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
public class LicenseResponseDto {
    private UUID entityId;
    private String entityType;

    private String dogName;
    private String licenseNumber;
    private String licenseStatus;
    private String licenseType;
    private String licenseExpiredDate;
    private String licenseFor;
}
package com.scube.licensing.features.profile.get_profile;

import com.scube.licensing.features.profile.validation.CheckProfileTypeExists;
import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import jakarta.validation.constraints.NotBlank;

import java.util.UUID;


public record GetProfileQuery(
        @CheckProfileTypeExists
        @NotBlank(message = "Profile type is required")
        String profileType,

        @NotBlank(message = "EntityId is required")
        UUID entityId
) implements IRequestAxon<GetProfileResponse> {

}
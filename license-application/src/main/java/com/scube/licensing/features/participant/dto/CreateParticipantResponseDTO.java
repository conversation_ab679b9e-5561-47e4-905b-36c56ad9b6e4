package com.scube.licensing.features.participant.dto;

import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.multi.tenant.TenantContext;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@NoArgsConstructor
public class CreateParticipantResponseDTO implements IEntityDto {
    private UUID entityId;
    private String entityType;
    private String tenant;

    @Setter
    private List<DocumentDto> documents = new ArrayList<>();

    public CreateParticipantResponseDTO(UUID entityId, String entityType) {
        this.entityId = entityId;
        if (!ObjectUtils.isEmpty(entityType)) {
            this.entityType = entityType.toLowerCase();
        }
        this.tenant = TenantContext.getTenantId();
    }
}
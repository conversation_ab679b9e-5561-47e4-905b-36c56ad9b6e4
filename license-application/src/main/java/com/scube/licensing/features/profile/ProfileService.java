package com.scube.licensing.features.profile;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.licensing.features.association.AssociationService;
import com.scube.licensing.features.entity.dtos.EntityTypeEnum;
import com.scube.licensing.features.license.license_actions_query.GetLicenseActionsQueryHandler;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.profile.dto.AddressDto;
import com.scube.licensing.features.profile.dto.ParticipantAddressDto;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import com.scube.licensing.features.profile.dto.ProfileAndAssociationsDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.features.profile.exception.EntityNotFoundException;
import com.scube.licensing.features.profile.exception.ProfileTypeNotFoundException;
import com.scube.licensing.features.profile.mapper.ProfileMapperService;
import com.scube.licensing.features.profile.model.ProfileAndAssociations;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.licensing.infrastructure.db.entity.profile_builder.ProfileType;
import com.scube.licensing.infrastructure.db.repository.profile.ProfileTypeRepository;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;

@Service
@Slf4j
@AllArgsConstructor
@Transactional
public class ProfileService {
    private final AxonGateway axonGateway;
    private final ProfileTypeRepository profileTypeRepository;
    private final ObjectMapper objectMapper;
    private final ProfileMapperService profileMapperService;
    private final AssociationService associationService;

    @NonNull
    public Associable getAssociableOrThrow(Long id, AssociationType associationType) {
        return getAssociable(id, associationType)
                .orElseThrow(() -> new EntityNotFoundException("Entity not found with id: " + id + " and type: " + associationType));
    }

    @Nullable
    public Associable getAssociableOrElseNull(Long id, AssociationType associationType) {
        return getAssociable(id, associationType).orElse(null);
    }

    public Optional<? extends Associable> getAssociable(Long id, AssociationType associationType) {
        var repo = associationService.getRepository(associationType);
        return repo.findById(id);
    }

    public <T extends Associable> T getProfileOrElseThrow(UUID entityId, Class<T> clazz) {
        if (!Associable.class.isAssignableFrom(clazz)) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Class must implement Associable");
        }
        return (T) getProfileOrElseThrow(clazz.getSimpleName().toLowerCase(), entityId);
    }

    public <T extends Associable> T getProfileOrElseNull(UUID entityId, Class<T> clazz) {
        var associable = getProfileOrElseNull(clazz.getSimpleName().toLowerCase(), entityId);
        if (associable == null) return null;
        return (T) associable;
    }

    @Nullable
    public Associable getProfileOrElseNull(@NonNull String entityType, UUID entityId) {
        return getProfile(entityType, entityId).orElse(null);
    }

    public Associable getProfileOrElseThrow(@NonNull String entityType, UUID entityId) {
        return getProfile(entityType, entityId)
                .orElseThrow(() -> new EntityNotFoundException("Entity not found with id: " + entityId + " and type: " + entityType));
    }

    public Optional<? extends Associable> getProfile(String entityType, UUID entityId) {
        var entityTypeEnum = EntityTypeEnum.fromValue(entityType);
        var repo = associationService.getRepository(entityTypeEnum);
        return repo.findByUuid(entityId);
    }


    public List<IAssociableDto> getResidentDtos(Iterable<UUID> entityIds) {
        var participants = getResidents(entityIds);
        return participants.stream().map(profileMapperService::toDto).toList();
    }

    public List<Participant> getResidents(Iterable<UUID> entityIds) {
        var participants = axonGateway.query(new ParticipantService.FindParticipantByEntityIdsQuery(entityIds));
        participants.removeIf(x -> !x.isIndividual());
        return participants;
    }

    public IAssociableDto getProfileDto(UUID entityId, String entityType) {
        return profileMapperService.toDto(getProfileOrElseThrow(entityType, entityId));
    }

    public ProfileAndAssociationsDto getProfileAndAssociationsDto(UUID entityId, String entityType) {
        var profilesAndAssociations = getProfileAndAssociations(entityId, entityType);
        var result = profileMapperService.toDto(profilesAndAssociations);
        log.debug("returning profile and associations dto");
        return result;
    }

    public ProfileAndAssociations getProfileAndAssociations(UUID entityId, String entityType) {
        var profile = getProfileOrElseThrow(entityType, entityId);
        return new ProfileAndAssociations(profile);
    }

    @Transactional(readOnly = true)
    public JsonNode getProfileAndAssociationsAsJsonReadOnly(UUID entityId, String entityType) {
        ProfileAndAssociations profileAndAssociations = getProfileAndAssociations(entityId, entityType);
        profileAndAssociations = filterDrafts(profileAndAssociations);
        ProfileAndAssociationsDto profileAndAssociationsDto = profileMapperService.toDto(profileAndAssociations);

        IAssociableDto profile = profileAndAssociationsDto.getProfile();
        List<IAssociableDto> associations = profileAndAssociationsDto.getAssociations();

        //Create parent node in the json from the profile
        ObjectNode parentNode = objectMapper.createObjectNode();

        parentNode.set(entityType, objectMapper.valueToTree(profile));

        // Create a map to store associations by tableName
        Map<String, ArrayNode> associationsByTable = new HashMap<>();

        // Individuals need to have participantAddressId and participantAddressTypeId added to the address json
        // So, remove the addresses from the associations list and then add the participantAddresses from the participant entity
        if ("individual".equalsIgnoreCase(entityType)) {
            associations = associations.stream()
                    .filter(assoc -> !(assoc instanceof AddressDto))
                    .toList();

            addParticipantAddressesToArrayNodeMap((ParticipantDto) profile, associationsByTable);
        }

        // Add the associations to the map
        addAssociationsToArrayNodeMap(associations, associationsByTable);

        // Add the ArrayNode for each tableName to the parent node
        for (Map.Entry<String, ArrayNode> entry : associationsByTable.entrySet()) {
            parentNode.set(entry.getKey(), entry.getValue());
        }

        return parentNode;
    }

    private static ProfileAndAssociations filterDrafts(ProfileAndAssociations profileAndAssociations) {
        var associations = profileAndAssociations.getAssociables().stream()
                .filter(ProfileService::isDraft)
                .toList();
        return new ProfileAndAssociations(profileAndAssociations.getProfile(), associations);
    }

    private static boolean isDraft(Associable associable) {
        if (associable instanceof License licenseDto) {
            // filter out any draft licenses
            return !licenseDto.isDraft();
        } else if (associable instanceof Participant participant && participant.isDog()) {
            // if the dog is part of a draft license, filter it out
            return participant.getLicenses().stream().noneMatch(License::isDraft);
        }
        return true;
    }

    private void addParticipantAddressesToArrayNodeMap(ParticipantDto participant, Map<String, ArrayNode> associationsByTable) {
        for (ParticipantAddressDto participantAddress : participant.getAddresses()) {
            JsonNode jsonNode = objectMapper.valueToTree(participantAddress);

            // Check if an ArrayNode already exists for the tableName
            ArrayNode arrayNode = associationsByTable.get("address");

            if (arrayNode == null) {
                // If it doesn't exist, create a new ArrayNode and put it in the map
                arrayNode = objectMapper.createArrayNode();
                associationsByTable.put("address", arrayNode);
            }

            // Add the jsonNode to the ArrayNode
            arrayNode.add(jsonNode);
        }

    }

    private void addAssociationsToArrayNodeMap(List<IAssociableDto> associations, Map<String, ArrayNode> associationsByTable) {
        // Loop through the associations and add them to the json
        for (IAssociableDto association : associations) {
            JsonNode node = objectMapper.valueToTree(association);

            String tableName = association.getTableName();

            //If its a participant change the node name to the participant group name
            if (tableName.equals("participant")) {
                tableName = ((ParticipantDto) association).getName().toLowerCase();
            }

            // Check if an ArrayNode already exists for the tableName
            ArrayNode arrayNode = associationsByTable.get(tableName);
            if (arrayNode == null) {
                // If it doesn't exist, create a new ArrayNode and put it in the map
                arrayNode = objectMapper.createArrayNode();
                associationsByTable.put(tableName, arrayNode);
            }

            // Add the jsonNode to the ArrayNode
            arrayNode.add(node);
        }
    }

    //profile types
    public ProfileType getProfileTypeByNameOrThrow(String name) {
        return profileTypeRepository.findByNameIgnoreCase(name)
                .orElseThrow(() -> new ProfileTypeNotFoundException(name));
    }

    public boolean checkProfileTypeExistsByName(String name) {
        return profileTypeRepository.existsByNameIgnoreCase(name);
    }

    public ProfileType saveType(ProfileType profileType) {
        return profileTypeRepository.save(profileType);
    }

    @Transactional
    public void save(Associable entity) {
        associationService.saveAssociable(entity);
    }

    // @formatter:off
    public record GetLicenseActionsQuery(UUID entityId) implements IRabbitFanoutPublisherRpc<GetLicenseActionsQueryHandler.GetLicenseActionsQueryResponse> { }
}
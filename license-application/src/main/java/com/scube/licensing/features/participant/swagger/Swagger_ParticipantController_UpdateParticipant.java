package com.scube.licensing.features.participant.swagger;

import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@SwaggerInfo(summary = "Update a participant", description = "This is used to update a participant.")
@Parameter(name = "participantEntityId", description = "This can be the resident or the dog entity id", required = true)
@Parameter(
        name = "fields",
        description = "The request body should be a map of key value pairs. The key should be the field name and the value should be the new value.",
        example = """
                {
                    "firstName": "John",
                    "lastName": "Doe",
                    "dateOfBirth": "1990-01-01",
                    "dogName": "Fido"
                }
                """,
        schema = @Schema(implementation = Object.class)
)
@Parameter(
        name = "files",
        description = "The request body should be a map of key value pairs. The key should be the file name and the value should be the file.",
        example = """
                {
                    "idCardFront": "(binary)",
                    "idCardBack": "(binary)"
                }
                """,
        schema = @Schema(implementation = Object.class)
)
public @interface Swagger_ParticipantController_UpdateParticipant {
}

package com.scube.licensing.features.participant.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
public class SearchResponseDTO {
    private UUID entityId;
    private String entityType;
    private String primaryDisplay;
    private String secondaryDisplay;
    private String email;
    private String phone;
    private String avatarUrl;

    private List<LicenseResponseDto> licenses = new ArrayList<>();
}

package com.scube.licensing.features.code_lookup;

import com.scube.audit.auditable.services.AuditableEntityService;
import com.scube.licensing.features.code_lookup.dto.CodeLookupResponse;
import com.scube.licensing.features.code_lookup.dto.CodeLookupResponseLineItem;
import com.scube.licensing.features.code_lookup.mapper.CodeLookupMapper;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookup;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.licensing.infrastructure.db.repository.code_lookup.CodeLookupRepository;
import com.scube.multi.tenant.tenancy.switch_tenant.SwitchTenant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

import static org.springframework.http.HttpStatus.NOT_FOUND;

@Slf4j
@Service
@RequiredArgsConstructor
public class CodeLookupService extends AuditableEntityService<Long, CodeLookup, CodeLookupRepository> {
    private final CodeLookupMapper codeLookupMapper;

    @SwitchTenant
    public Optional<CodeLookup> findByCode(@NonNull String code) {
        return repository.findByCode(code);
    }

    @SwitchTenant
    public Optional<CodeLookup> findByCodeAndRealm(@NonNull String code, @NonNull String tenant) {
        return repository.findByCodeAndRealm(code, tenant);
    }

    @SwitchTenant
    public CodeLookup findByCodeOrElseThrow(@NonNull String code) {
        return findByCode(code)
                .orElseThrow(() -> new ResponseStatusException(NOT_FOUND, "Code with value " + code + " not found"));
    }

    @SwitchTenant
    public Optional<CodeLookup> findFirstByEntityTypeAndEntityIdAndAction(@NonNull String entityType, @NonNull String entityId, CodeLookupActionEnum action, String tenant) {
        return repository.findFirstByEntityTypeAndEntityIdAndActionAndRealm(entityType, entityId, action, tenant);
    }

    @SwitchTenant
    public Optional<CodeLookup> findFirstByActionAndTenant(CodeLookupActionEnum action, String tenant) {
        return repository.findFirstByActionAndRealm(action, tenant);
    }

    @SwitchTenant
    public Optional<CodeLookup> findFirstByEntityTypeAndEntityId(@NonNull String entityType, @NonNull String entityId) {
        return repository.findFirstByEntityTypeAndEntityId(entityType, entityId);
    }

    @SwitchTenant
    public CodeLookup findFirstByEntityTypeAndEntityIdOrElseThrow(String entityType, String entityId) {
        return findFirstByEntityTypeAndEntityId(entityType, entityId)
                .orElseThrow(() -> new ResponseStatusException(NOT_FOUND, "Cannot find code for entity type " + entityType + " and entity id " + entityId));
    }

    @SwitchTenant
    public CodeLookup findFirstByEntityTypeAndEntityIdAndActionOrElseThrow(@NonNull String entityType, @NonNull String entityId, CodeLookupActionEnum action, String tenant) {
        return findFirstByEntityTypeAndEntityIdAndAction(entityType, entityId, action, tenant)
                .orElseThrow(() -> new ResponseStatusException(NOT_FOUND, "Cannot find code for entityType: " + entityType + ", entityId: " + entityId + ", tenant: " + tenant));
    }

    @SwitchTenant
    public CodeLookup createOrLookup(@NonNull String entityType, @NonNull String entityId, String action, String tenant) {
        return createOrLookup(entityType, entityId, CodeLookupActionEnum.fromValue(action), tenant);
    }

    @SwitchTenant
    public CodeLookup createOrLookup(String action, String tenant) {
        return createOrLookup(CodeLookupActionEnum.fromValue(action), tenant);
    }

    @SwitchTenant
    public CodeLookup createOrLookup(CodeLookupActionEnum action, String tenant) {
        return findFirstByActionAndTenant(action, tenant)
                .orElseGet(() -> createByPublic(action, tenant));
    }

    @SwitchTenant
    private CodeLookup createByPublic(CodeLookupActionEnum action, String tenant) {
        var codeLookup = CodeLookup.builderFor(repository, tenant).createByPublic(action);
        return repository.save(codeLookup);
    }

    @SwitchTenant
    public CodeLookup createOrLookup(@NonNull String entityType, @NonNull String entityId, CodeLookupActionEnum action, String tenant) {
        return findFirstByEntityTypeAndEntityIdAndAction(entityType, entityId, action, tenant)
                .orElseGet(() -> createByEntity(entityType, entityId, action, tenant));
    }

    @SwitchTenant
    public CodeLookup createByEntity(@NonNull String entityType, @Nullable String entityId, String action, @NonNull String tenant) {
        return createByEntity(entityType, entityId, CodeLookupActionEnum.fromValue(action), tenant);
    }

    @SwitchTenant
    public CodeLookup createByEntity(@NonNull String entityType, @Nullable String entityId, CodeLookupActionEnum action, @NonNull String tenant) {
        var codeLookup = CodeLookup.builderFor(repository, tenant).createByEntity(entityType, entityId, action);
        return repository.save(codeLookup);
    }

    @SwitchTenant
    public void update(@NonNull String code, @NonNull String entityType, @Nullable String entityId, CodeLookupActionEnum action, @NonNull String tenant) {
        findByCodeAndRealm(code, tenant).ifPresentOrElse(
                codeLookup -> {
                    codeLookup.setEntityType(entityType);
                    codeLookup.setEntityId(entityId);
                    codeLookup.setAction(action);
                    repository.save(codeLookup);
                },
                () -> {
                    log.warn("Code with value {} not found", code);
                }
        );

    }

    @SwitchTenant
    public CodeLookupResponse getByEntityTypeAndEntityId(String entityType, String entityId, CodeLookupActionEnum action) {
        List<CodeLookupResponseLineItem> items = (action == null) ?
                codeLookupMapper.toDto(repository.findAllByEntityTypeAndEntityIdAndAction(entityType, entityId, null)) :
                codeLookupMapper.toDto(repository.findAllByEntityTypeAndEntityIdAndAction(entityType, entityId, action));
        return new CodeLookupResponse(items);
    }

    @SwitchTenant
    public Resource batchTagCreate(int count, String tenant) {
        var result = new StringBuilder();
        for (int i = 0; i < count; i++) {
            var codeLookup = createByEntity("tag", null, CodeLookupActionEnum.CREATE, tenant);
            result.append(codeLookup.getCode()).append("\n");
        }
        return new ByteArrayResource(result.toString().getBytes(StandardCharsets.UTF_8));
    }
}
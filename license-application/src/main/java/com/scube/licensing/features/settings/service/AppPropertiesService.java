package com.scube.licensing.features.settings.service;

import com.scube.licensing.features.settings.dto.CreateAppPropertyRequest;
import com.scube.licensing.features.settings.dto.UpdateAppPropertyRequest;
import com.scube.licensing.features.settings.entity.AppProperty;
import com.scube.licensing.features.settings.exception.AppPropertyBadRequestException;
import com.scube.licensing.features.settings.exception.AppPropertyNotFoundException;
import com.scube.licensing.features.settings.exception.AppPropertyUniqueNameException;
import com.scube.licensing.features.settings.repository.AppPropertyRepository;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@AllArgsConstructor
@Service
@Slf4j
@Transactional
public class AppPropertiesService {
    private final AppPropertyRepository appPropertyRepository;
    private static final String APP_PROPERTIES_CACHE = "appPropertiesCache";

    /**
     * Retrieves the value of the specified application property.
     *
     * @param appPropertyName The name of the application property.
     * @return The value of the application property.
     */
    public String getProperty(String appPropertyName) {
        log.debug("AppPropertiesService.getAppProperty()");
        return getAppPropsMap().get(appPropertyName);
    }

    /**
     * Retrieves the current app property cache
     *
     * @return The app property cache
     */
    public Map<String, String> getCache() {
        log.debug("AppPropertiesService.getAppProperty()");
        return getAppPropsMap();
    }

    /**
     * Retrieves a map of all application properties.
     *
     * @return A map of application properties where the keys are property names and the values are property values.
     */
    @Cacheable(APP_PROPERTIES_CACHE)
    public Map<String, String> getAppPropsMap() {
        log.debug("AppPropertiesService.getAppPropsMap()");
        HashMap<String, String> appPropsMap = new HashMap<>();
        List<AppProperty> appProperties = appPropertyRepository.findAll();
        for (AppProperty appProperty : appProperties) {
            appPropsMap.put(appProperty.getName(), appProperty.getValue());
        }

        return appPropsMap;
    }

    /**
     * Retrieves a list of all application properties.
     *
     * @return A list of application properties.
     */
    public List<AppProperty> findAll() {
        log.debug("AppPropertiesService.findAll()");
        return appPropertyRepository.findAll();
    }

    /**
     * Retrieves an application property by its name.
     *
     * @param appPropertyName The name of the application property.
     * @return The application property with the specified name.
     * @throws AppPropertyNotFoundException if the application property is not found.
     */
    public AppProperty findByName(String appPropertyName) {
        log.debug("AppPropertiesService.findByName()");
        return appPropertyRepository.findByName(appPropertyName).orElseThrow(() -> new AppPropertyNotFoundException("AppProperty not found."));
    }

    /**
     * Retrieves an application property by its UUID.
     *
     * @param appPropertyUuid The UUID of the application property.
     * @return The application property with the specified UUID.
     * @throws AppPropertyNotFoundException if the application property is not found.
     */
    public AppProperty findByUuid(UUID appPropertyUuid) {
        log.debug("AppPropertiesService.findByUuid()");
        return appPropertyRepository.findById(appPropertyUuid).orElseThrow(() -> new AppPropertyNotFoundException("AppProperty not found."));
    }

    /**
     * Creates a new application property.
     *
     * @param createAppPropertyRequest The request object containing the details of the application property to be created.
     * @return The created application property.
     */
    @Transactional
    public AppProperty create(CreateAppPropertyRequest createAppPropertyRequest) {
        log.debug("AppPropertiesService.save()");

        String name = createAppPropertyRequest.getName();
        if (appPropertyRepository.existsByName(name)) {
            throw new AppPropertyUniqueNameException("App Property with name " + name + " already exists");
        }

        AppProperty appProperty = AppProperty.builder()
                .name(createAppPropertyRequest.getName())
                .value(createAppPropertyRequest.getValue())
                .description(createAppPropertyRequest.getDescription())
                .build();

        return appPropertyRepository.save(appProperty);
    }

    /**
     * Updates an existing application property.
     *
     * @param updateAppPropertyRequest The request object containing the details of the application property to be updated.
     * @return The updated application property.
     */
    @Transactional
    @CacheEvict(cacheNames = APP_PROPERTIES_CACHE, allEntries = true)
    public AppProperty update(UpdateAppPropertyRequest updateAppPropertyRequest) {
        log.debug("AppPropertiesService.update()");

        AppProperty appProperty = null;

        if (ObjectUtils.isNotEmpty(updateAppPropertyRequest.getUuid())) {
            appProperty = findByUuid(updateAppPropertyRequest.getUuid());
        } else if (StringUtils.isNotBlank(updateAppPropertyRequest.getName())) {
            appProperty = findByName(updateAppPropertyRequest.getName());
        } else {
            throw new AppPropertyBadRequestException("Either uuid or name must be provided.");
        }

        if (StringUtils.isNotBlank(updateAppPropertyRequest.getValue())) {
            appProperty.setValue(updateAppPropertyRequest.getValue());
        }
        if (StringUtils.isNotBlank(updateAppPropertyRequest.getDescription())) {
            appProperty.setDescription(updateAppPropertyRequest.getDescription());
        }

        return appPropertyRepository.save(appProperty);
    }

    /**
     * Deletes an application property by its UUID.
     *
     * @param appPropertyUuid The UUID of the application property to be deleted.
     * @throws AppPropertyNotFoundException if the application property is not found.
     */
    @Transactional
    @CacheEvict(cacheNames = APP_PROPERTIES_CACHE, allEntries = true)
    public void deleteByUuid(UUID appPropertyUuid) {
        log.debug("AppPropertiesService.deleteByUuid()");
        AppProperty appProperty = findByUuid(appPropertyUuid);
        appPropertyRepository.delete(appProperty);
    }
}

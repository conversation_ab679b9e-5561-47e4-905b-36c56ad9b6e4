package com.scube.licensing.features.participant.handlers.save;

import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerVoidAxon;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;


@Component
@AllArgsConstructor
public class SaveParticipantHandler implements IRequestHandlerVoidAxon<SaveParticipantCommand> {
    private final ParticipantService participantService;

    @Override
    @Transactional
    @CommandHandler
    public void handle(SaveParticipantCommand command) {
        participantService.save(command.participant());
    }
}



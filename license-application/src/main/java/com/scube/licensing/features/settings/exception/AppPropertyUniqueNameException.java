package com.scube.licensing.features.settings.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class AppPropertyUniqueNameException extends RuntimeException {
    public AppPropertyUniqueNameException() {
    }

    public AppPropertyUniqueNameException(String message) {
        super(message);
    }

    public AppPropertyUniqueNameException(String message, Throwable cause) {
        super(message, cause);
    }

    public AppPropertyUniqueNameException(Throwable cause) {
        super(cause);
    }
}

package com.scube.licensing.features.entity_fee;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.lib.misc.annotations.swagger.SwaggerInfo;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("me/entity-fees")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class LoggedInUserEntityFeeController {
    private final EntityFeeService entityFeeService;

    @GetMapping("individual")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get all fees of the participant", description = "This is used to get all fees of the participant.")
    @RolesAllowed(Permissions.LoggedInUserEntityFee.GET_FEES)
    public GetAllEntityFeeResponse getFees(@AuthenticationPrincipal OpenidClaimSet jwt) {
        return entityFeeService.getAllFees("individual", UUID.fromString(jwt.getSubject()));
    }

    @GetMapping("{entityType}/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get all fees of the participant", description = "This is used to get all fees of the participant.")
    @RolesAllowed(Permissions.LoggedInUserEntityFee.GET_FEES)
    @PreAuthorize("isOwnerOfEntity(#entityType, #entityId)")
    public GetAllEntityFeeResponse getFees(@PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        return entityFeeService.getAllFees(entityType, entityId);
    }

    @GetMapping("{feeEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @SwaggerInfo(summary = "Get a fee of the participant", description = "This is used to get a fee of the participant.")
    @RolesAllowed(Permissions.LoggedInUserEntityFee.GET_FEE)
    @PreAuthorize("isOwnerOfEntityFee(#feeEntityId)")
    public EntityFeeDto getFee(@PathVariable UUID feeEntityId) {
        return entityFeeService.getFee(feeEntityId);
    }
}
package com.scube.licensing.features.document.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.dto.DocumentTypeDto;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.features.document.service.DocumentTypeService;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.db.entity.document.DocumentType;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("document")
@Slf4j
@Validated
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
public class DocumentController {
    private final DocumentService documentService;
    private final DocumentTypeService documentTypeService;

    @PostMapping(path = "/{entityType}/{entityId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Document.FILE_UPLOAD)
    public List<DocumentDto> fileUpload(@PathVariable @Size(max = 255) String entityType,
                                        @PathVariable UUID entityId,
                                        @RequestParam Map<String, MultipartFile> pairs) {
        return documentService.upload(entityType, entityId, pairs);
    }

    @GetMapping("/{entityType}/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Document.GET_DOCUMENTS)
    public List<DocumentDto> getDocuments(@PathVariable @Size(max = 255) String entityType, @PathVariable UUID entityId) {
        return documentService.getDocuments(entityType, entityId);
    }

    @GetMapping("/{documentEntityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Document.GET_DOCUMENT)
    public DocumentDto getDocument(@PathVariable UUID documentEntityId) {
        return documentService.getDto(documentEntityId);
    }

    @GetMapping("/{documentEntityId}/history")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Document.GET_DOCUMENT_HISTORY)
    public Page<DocumentDto> getDocumentHistory(@PathVariable UUID documentEntityId, @PageableDefault(size = 10) Pageable pageable) {
        return documentService.getHistory(documentEntityId, pageable);
    }

    @DeleteMapping("/{documentEntityId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Document.DELETE)
    public void delete(@PathVariable UUID documentEntityId) {
        documentService.softDelete(documentEntityId);
    }

    @GetMapping("/type/{documentTypeKey}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Document.GET_DOCUMENT_TYPE)
    public DocumentType getDocumentType(@PathVariable @Size(max = 255) String documentTypeKey) {
        return documentTypeService.getDocumentType(documentTypeKey);
    }

    @GetMapping("/type")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Document.GET_DOCUMENT_TYPES)
    public List<DocumentTypeDto> getDocumentTypes() {
        return documentTypeService.getDocumentTypes();
    }

    @PostMapping("/type")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Document.SAVE_DOCUMENT_TYPE)
    public List<DocumentTypeDto> saveDocumentType(@RequestBody List<DocumentTypeDto> documentType) {
        return documentTypeService.save(documentType);
    }
}

package com.scube.licensing.features.association.find_all_parent_associations_by_child;

import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class FindAllParentAssociationsByChildHandler implements IRequestHandlerAxon<FindAllParentAssociationsByChildQuery, List<Association>> {
    private final AssociationRepository associationRepository;

    @Autowired
    public FindAllParentAssociationsByChildHandler(AssociationRepository associationRepository) {
        this.associationRepository = associationRepository;
    }

    @QueryHandler
    public List<Association> handle(FindAllParentAssociationsByChildQuery query) {
        return associationRepository.findAllByChildIdAndChildAssociationType(
                query.child().getId(),
                query.child().getAssociationType()
        ).stream().toList();
    }
}
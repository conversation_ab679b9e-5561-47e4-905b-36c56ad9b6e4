package com.scube.licensing.features.association.add_new_association;

import com.scube.licensing.infrastructure.db.entity.association.Associable;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;
import java.util.UUID;

@Data
@NoArgsConstructor
public class AddNewAssociationDomainEvent implements Serializable {
    private Associable parent;
    private Associable child;

    private String parentEntityType;
    private UUID parentEntityId;

    private String childEntityType;
    private UUID childEntityId;

    private Map<String, Object> properties;

    public AddNewAssociationDomainEvent(Associable parent, Associable child) {
        this.parent = parent;
        this.child = child;
    }

    public AddNewAssociationDomainEvent(Associable parent, Associable child, Map<String, Object> properties) {
        this.parent = parent;
        this.child = child;
    }

    public static AddNewAssociationDomainEvent child(Associable parent, String childEntityType, UUID childEntityId, Map<String, Object> properties) {
        var event = child(parent, childEntityType, childEntityId);
        event.properties = properties;
        return event;
    }

    public static AddNewAssociationDomainEvent child(Associable parent, String childEntityType, UUID childEntityId) {
        var event = new AddNewAssociationDomainEvent();
        event.parent = parent;
        event.childEntityType = childEntityType;
        event.childEntityId = childEntityId;
        return event;
    }

    public static AddNewAssociationDomainEvent parent(Associable child, String parentEntityType, UUID parentEntityId, Map<String, Object> properties) {
        var event = parent(child, parentEntityType, parentEntityId);
        event.properties = properties;
        return event;
    }

    public static AddNewAssociationDomainEvent parent(Associable child, String parentEntityType, UUID parentEntityId) {
        var event = new AddNewAssociationDomainEvent();
        event.child = child;
        event.parentEntityType = parentEntityType;
        event.parentEntityId = parentEntityId;
        return event;
    }
}

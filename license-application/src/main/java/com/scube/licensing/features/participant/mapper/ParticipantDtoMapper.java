package com.scube.licensing.features.participant.mapper;

import com.scube.lib.misc.BeanUtils;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.mapper.DocumentDtoMapper;
import com.scube.licensing.features.entity_fee.EntityFeeService;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.entity_group.EntityGroupService;
import com.scube.licensing.features.entity_group.dtos.CustomFeeDto;
import com.scube.licensing.features.entity_group.dtos.FeeSetDto;
import com.scube.licensing.features.participant.opt_in.OptInDto;
import com.scube.licensing.features.profile.dto.ContactDto;
import com.scube.licensing.features.profile.dto.ParticipantAddressDto;
import com.scube.licensing.features.profile.dto.ParticipantDto;
import com.scube.licensing.features.profile.dto.QueryParticipantDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.licensing.infrastructure.db.entity.participant.contact.Contact;
import com.scube.licensing.infrastructure.db.entity.participant.opt_in.OptIn;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Mapper(componentModel = "spring", uses = {EventMapper.class})
@Slf4j
public abstract class ParticipantDtoMapper {
    @Autowired
    protected ParticipantAddressDtoMapper participantAddressDtoMapper;

    @Autowired
    protected DocumentDtoMapper documentMapper;


    @Mapping(target = "entityId", source = "uuid")
    @Mapping(target = "entityType", expression = "java(participant.getEntityTypeToLowerCase())")
    @Mapping(target = "name", source = "participantTypeGroup.participantGroup.name")
    @Mapping(target = "participantType", source = "participantTypeGroup.participantType.name")
    @Mapping(target = "status", source = "participantStatus.name")
    @Mapping(target = "active", expression = "java(mapIsActive(participant))")
    @Mapping(target = "customFields", source = "properties")
    @Mapping(target = "addresses", expression = "java(mapParticipantAddresses(participant))")
    @Mapping(target = "documents", expression = "java(getDocuments(participant))")
    @Mapping(target = "fees", expression = "java(getGroups(participant))")
    public abstract ParticipantDto toDto(Participant participant);

    @Mapping(target = "entityId", source = "uuid")
    @Mapping(target = "entityType", expression = "java(participant.getEntityTypeToLowerCase())")
    @Mapping(target = "name", source = "participantTypeGroup.participantGroup.name")
    @Mapping(target = "participantType", source = "participantTypeGroup.participantType.name")
    @Mapping(target = "status", source = "participantStatus.name")
    @Mapping(target = "active", expression = "java(mapIsActive(participant))")
    @Mapping(target = "customFields", source = "properties")
    public abstract QueryParticipantDto toQueryDto(Participant participant);

    public abstract List<ParticipantDto> toDto(List<Participant> participants);
    public abstract List<QueryParticipantDto> toQueryDto(List<Participant> participants);

    public abstract OptInDto toOptIn(OptIn optIn);

    public abstract List<OptInDto> toOptIn(List<OptIn> optIns);

    @Mapping(target = "type", source = "contactTypeGroup.contactGroup.name")
    @Mapping(target = "group", source = "contactTypeGroup.contactType.name")
    @Mapping(target = "primary", expression = "java(contact.isPrimaryEmail() || contact.isPrimaryPhone())")
    @Mapping(target = "email", expression = "java(contact.isEmail())")
    @Mapping(target = "phone", expression = "java(contact.isPhone())")
    public abstract ContactDto toContactDto(Contact contact);

    public abstract List<ContactDto> toContactDto(List<Contact> contacts);

    protected List<ParticipantAddressDto> mapParticipantAddresses(Participant participant) {
        return participantAddressDtoMapper.toDto(participant.getParticipantAddresses());
    }

    protected boolean mapIsActive(Participant participant) {
        return participant.getParticipantStatus().isActive();
    }

    protected List<DocumentDto> getDocuments(Participant participant) {
        return documentMapper.toDto(participant.getDocuments());
    }

    public GetAllEntityFeeResponse getAllFee(Participant participant) {
        var entityFeeService = BeanUtils.getBean(EntityFeeService.class);
        return entityFeeService.getAllFees(participant.getEntityTypeToLowerCase(), participant.getUuid());
    }

    public CustomFeeDto getGroups(Participant participant) {
        var entityFeeService = BeanUtils.getBean(EntityGroupService.class);
        List<FeeSetDto> getAllFeeSetResponse = entityFeeService.getAllFeeSet(participant.getEntityTypeToLowerCase(), participant.getUuid());
        CustomFeeDto customFee = new CustomFeeDto();
        customFee.setFeeSets(getAllFeeSetResponse);
        customFee.calculateTotals();
        return customFee;
    }

    public List<ParticipantDto> getIndividuals(Participant participant) {
        if (participant.isIndividual()) return List.of();
        return participant.getIndividuals().stream()
                .map(this::toDto)
                .toList();
    }
}
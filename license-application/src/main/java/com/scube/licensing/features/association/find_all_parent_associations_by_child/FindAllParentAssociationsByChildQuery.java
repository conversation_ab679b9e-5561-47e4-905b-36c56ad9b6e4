package com.scube.licensing.features.association.find_all_parent_associations_by_child;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import org.springframework.util.ObjectUtils;

import java.util.List;

public record FindAllParentAssociationsByChildQuery(Associable child) implements IRequestAxon<List<Association>> {
    public FindAllParentAssociationsByChildQuery {
        if (ObjectUtils.isEmpty(child))
            throw new IllegalArgumentException("Child is required");

        if (ObjectUtils.isEmpty(child.getId()))
            throw new IllegalArgumentException("Child id is required");

        if (ObjectUtils.isEmpty(child.getAssociationType()))
            throw new IllegalArgumentException("Child association type is required");
    }
}
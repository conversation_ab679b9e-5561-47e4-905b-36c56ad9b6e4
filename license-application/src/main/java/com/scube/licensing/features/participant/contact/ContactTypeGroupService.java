package com.scube.licensing.features.participant.contact;

import com.scube.licensing.infrastructure.db.entity.participant.contact.ContactTypeGroup;
import com.scube.licensing.infrastructure.db.repository.participant.ContactTypeGroupRepository;
import org.springframework.stereotype.Service;

@Service
public class ContactTypeGroupService {
    private final ContactTypeGroupRepository contactTypeGroupRepository;

    public ContactTypeGroupService(ContactTypeGroupRepository contactTypeGroupRepository) {
        this.contactTypeGroupRepository = contactTypeGroupRepository;
    }

    public ContactTypeGroup getContactTypeGroup(String contactTypeName, String contactGroupName) {
        return contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(contactTypeName, contactGroupName)
                .orElseThrow(() -> new ContactTypeGroupNotFoundException("ContactTypeGroup with contactTypeName: " + contactTypeName + " and contactGroupName: " + contactGroupName + " not found"));
    }

    public ContactTypeGroup getContactTypeGroup(Long id) {
        return contactTypeGroupRepository.findById(id)
                .orElseThrow(() -> new ContactTypeGroupNotFoundException("ContactTypeGroup with id: " + id + " not found"));
    }
}
package com.scube.licensing.features.merge_request.dtos;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.merge_request.MergeRequestUtil;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestExistingUser;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
public class MergeRequestResponseLineItem {
    private UUID mergeRequestEntityId;

    @JsonIgnore
    private UUID requestedIndividualId;

    private String tagNumber;
    private String licenseNumber;
    private MergeRequestStatusEnum status;
    private Instant createdDate;
    private List<MergeRequestResponseExistingUser> foundResidents;

    public MergeRequestResponseLineItem(MergeRequest mergeRequest, ProfileService profileService) {
        this(mergeRequest, MergeRequestUtil.buildResidentDtos(List.of(mergeRequest), profileService));
    }

    public MergeRequestResponseLineItem(MergeRequest mergeRequest, Map<UUID, IAssociableDto> residents) {
        this.mergeRequestEntityId = mergeRequest.getUuid();
        this.requestedIndividualId = mergeRequest.getRequestedUserId();
        this.foundResidents = mergeRequest.getExistingUsers().stream()
                .map(x -> new MergeRequestResponseExistingUser(x, residents))
                .toList();
        this.tagNumber = mergeRequest.getTagNumber();
        this.licenseNumber = mergeRequest.getLicenseNumber();
        this.status = mergeRequest.getStatus();
        this.createdDate = mergeRequest.getCreatedDate();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class MergeRequestResponseExistingUser {
        private IAssociableDto resident;
        private Integer probability;
        private String matchType;

        public MergeRequestResponseExistingUser(MergeRequestExistingUser user, Map<UUID, IAssociableDto> residents) {
            this.resident = residents.get(user.getExistingUserId());
            this.probability = user.getProbability();
            this.matchType = user.getMatchType();
        }
    }
}
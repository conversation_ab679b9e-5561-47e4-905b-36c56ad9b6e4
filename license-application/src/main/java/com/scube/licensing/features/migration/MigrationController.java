package com.scube.licensing.features.migration;

import com.scube.licensing.features.permission.Permissions;
import com.scube.multi.tenant.tenancy.switch_tenant.SwitchTenant;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/migration")
@AllArgsConstructor
@Validated
public class MigrationController {
    private final MigrationService migrationService;

    @PostMapping("/codeLookup")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Migration.MIGRATE_CODE_LOOKUP)
    @SwitchTenant
    public void migrateCodeLookup(@RequestParam @NotBlank String sourceTenant) {
        migrationService.migrateCodeLookup(sourceTenant);
    }

    @PostMapping("/codeLookup/allTenant")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Migration.MIGRATE_CODE_LOOKUP_ALL_TENANT)
    @SwitchTenant
    public void migrateCodeLookupToAllTenant() {
        migrationService.migrateCodeLookupToAllTenant();
    }
}

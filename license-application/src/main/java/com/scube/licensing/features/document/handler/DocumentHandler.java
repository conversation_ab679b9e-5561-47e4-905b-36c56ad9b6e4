package com.scube.licensing.features.document.handler;

import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import lombok.RequiredArgsConstructor;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Component
@RequiredArgsConstructor
public class DocumentHandler {
    private final DocumentService documentService;

    /**
     * Get a document by its ID.
     * @param query
     * @return
     */
    @QueryHandler
    public Document findDocumentByIdOrElseThrow(FindDocumentByIdOrElseThrowQuery query) {
        return documentService.getDocument(query.id());
    }

    @QueryHandler
    public Document findDocumentByEntityIdOrElseThrow(FindDocumentByEntityIdOrElseThrowQuery query) {
        return documentService.getDocumentByEntityIdOrElseThrow(query.entityId());
    }

    @QueryHandler
    public Optional<Document> findDocumentByEntityId(FindDocumentByEntityId query) {
        return documentService.getDocumentByEntityId(query.entityId());
    }

    @QueryHandler
    public List<DocumentDto> getDocuments(GetDocumentsByAssociableQuery query) {
        return documentService.getDocuments(query.associable());
    }

    public record GetDocumentsByAssociableQuery(Associable associable) implements IRequestAxon<List<DocumentDto>> {}
    public record FindDocumentByIdOrElseThrowQuery(Long id) implements IRequestAxon<Document> {}
    public record FindDocumentByEntityIdOrElseThrowQuery(UUID entityId) implements IRequestAxon<Document> {}
    public record FindDocumentByEntityId(UUID entityId) implements IRequestAxon<Optional<Document>> {}
}

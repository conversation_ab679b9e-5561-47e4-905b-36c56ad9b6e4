package com.scube.licensing.features.qr_codes;

import com.scube.auth.library.ITokenService;
import com.scube.client.document.generated.DocumentServiceConnection;
import com.scube.config_utils.json_storage.JsonStorageValue;
import com.scube.document.dto.gen_dto.FileUploadResponseDTO;
import com.scube.document.generated.FileUploadRequestDTO__QueryParams;
import com.scube.licensing.features.code_lookup.CodeLookupService;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.TenantDto;
import com.scube.licensing.features.qr_codes.dto.QrCodeResponse;
import com.scube.licensing.features.qr_codes.rabbit.GenerateQrCodeCommand;
import com.scube.licensing.features.qr_codes.rabbit.GenerateQrCodeCommandResponse;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.multi.tenant.TenantContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.springframework.http.HttpStatus.BAD_REQUEST;

@Slf4j
@Service
@RequiredArgsConstructor
public class QrCodeService {
    private final DocumentServiceConnection documentServiceConnection;
    private final CodeLookupService codeLookupService;
    private final ProfileService profileService;
    private final ITokenService tokenService;

    @JsonStorageValue({"config=tenant"})
    private TenantDto tenantDto;

    private boolean isUUID(String str) {
        try {
            UUID.fromString(str);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public QrCodeResponse generateByEntityIfNotExists(@NonNull String entityType, @NonNull String entityId, CodeLookupActionEnum action) {
        return generateByEntityIfNotExists(entityType, entityId, action.getValue());
    }

    public QrCodeResponse generateByEntityIfNotExists(@NonNull String entityType, @NonNull String entityId, String action) {
        if (isUUID(entityId)) {
            var associable = profileService.getProfileOrElseNull(entityType, UUID.fromString(entityId));
            if (ObjectUtils.isEmpty(associable)) {
                return generateByEntityAndUpload(entityType, entityId, action);
            }
            if (associable.qrCodes().hasQrCodeDocumentUUID(action)) {
                return new QrCodeResponse(associable.qrCodes().getQrCodeDocumentUUID(action));
            }
            var documentUUID = generateByEntityAndUpload(entityType, entityId, action).getQrCodeDocumentUUID();
            associable.qrCodes().setQrCodeDocumentUUID(documentUUID, action);
            profileService.save(associable);
            return new QrCodeResponse(documentUUID);
        }

        return generateByEntityAndUpload(entityType, entityId, action);
    }

    public QrCodeResponse generateByEntityAndUpload(@NonNull String entityType, @NonNull String entityId, CodeLookupActionEnum action) {
        return generateByEntityAndUpload(entityType, entityId, action.getValue());
    }


    public QrCodeResponse generateByEntityAndUpload(@NonNull String entityType, @NonNull String entityId, String action) {
        var codeLookup = codeLookupService.createOrLookup(entityType, entityId, action, TenantContext.getTenantId());
        var file = generateByCode(codeLookup.getCode()).getFile();
        var uploadRequest = new FileUploadRequestDTO__QueryParams().file(file);
        // save the file to the document service
        var token = tokenService.getNewAccessTokenFromCurrentRealm();
        FileUploadResponseDTO result = documentServiceConnection.document().handleFileUpload(uploadRequest, (CharSequence) token);
        return new QrCodeResponse(result.getDocumentUUID(), file);
    }

    public QrCodeResponse generateByEntityNoUpload(@NonNull String entityType, @NonNull String entityId, CodeLookupActionEnum action) {
        return generateByEntityNoUpload(entityType, entityId, action.getValue());
    }

    public QrCodeResponse generateByEntityNoUpload(@NonNull String entityType, @NonNull String entityId, String action) {
        var codeLookup = codeLookupService.createOrLookup(entityType, entityId, action, TenantContext.getTenantId());
        return generateByCode(codeLookup.getCode());
    }

    public QrCodeResponse generateByCode(String code) {
        // generate the barcode text
        var barcodeText = String.format("%s/f/%s", tenantDto.getClerkXpressUrl(), code);
        return generateByText(barcodeText);
    }

    public QrCodeResponse generateByText(String qrCodeText) {
        return new QrCodeResponse(QrCodeUtil.generate(qrCodeText));
    }

    public GenerateQrCodeCommandResponse getGenerateQrCodeCommandResponse(GenerateQrCodeCommand event) {
        QrCodeResponse response;
        if (event.byCode()) {
            response = generateByCode(event.getCode());
        } else if (event.byEntity()) {
            response = generateByEntityNoUpload(event.getEntityType(), event.getEntityId(), event.getAction());
        } else if (event.byQrCodeText()) {
            response = generateByText(event.getQrCodeText());
        } else {
            throw new ResponseStatusException(BAD_REQUEST, "Please provide a code, entity or qrCodeText");
        }
        return new GenerateQrCodeCommandResponse(response.getFile(), event);
    }

    @Async
    public CompletableFuture<GenerateQrCodeCommandResponse> getGenerateQrCodeCommandResponseAsync(GenerateQrCodeCommand event) {
        return CompletableFuture.completedFuture(getGenerateQrCodeCommandResponse(event));
    }
}
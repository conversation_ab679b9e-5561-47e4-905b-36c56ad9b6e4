package com.scube.licensing.features.profile.save;

import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerVoidAxon;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class SaveProfileEventHandler implements IRequestHandlerVoidAxon<SaveProfileEvent> {
    private final ProfileService profileService;

    @Override
    @Transactional
    @CommandHandler
    public void handle(SaveProfileEvent command) {
        profileService.save(command.entity());
    }
}
package com.scube.licensing.features.profile.get_profile_and_associations;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.features.profile.dto.ProfileAndAssociationsDto;
import lombok.RequiredArgsConstructor;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetProfileAndAssociationsHandler {
    private final ProfileService profileService;

    @QueryHandler
    public GetProfileAndAssociationsResponse handle(GetProfileAndAssociationsQuery query) {
        var profileAndAssociations = profileService.getProfileAndAssociations(query.entityId(), query.profileType());

        return new GetProfileAndAssociationsResponse(profileAndAssociations.getProfile(), profileAndAssociations.getAssociables());
    }

    @QueryHandler
    public ProfileAndAssociationsDto handle(GetProfileAndAssociationsDtoQuery query) {
        return profileService.getProfileAndAssociationsDto(query.entityId(), query.profileType());
    }

    @QueryHandler
    public JsonNode handle(GetProfileAndAssociationsAsJsonQuery query) {
        return profileService.getProfileAndAssociationsAsJsonReadOnly(query.entityId(), query.profileType());
    }
}
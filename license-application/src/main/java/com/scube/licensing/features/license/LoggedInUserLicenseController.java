package com.scube.licensing.features.license;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.licensing.features.license.fee.GetAllLicenseFeeQueryHandler;
import com.scube.licensing.features.license.fee.GetAllLicenseFeesQuery;
import com.scube.licensing.features.license.fee.sql_fees.FeeCalculationByDurationResponse;
import com.scube.licensing.features.license.fee.sql_fees.LicenseFeeService;
import com.scube.licensing.features.license.partial_save.create_final_license.CreateFinalLicenseCommand;
import com.scube.licensing.features.license.partial_save.create_final_license.CreateFinalLicenseResponse;
import com.scube.licensing.features.license.partial_save.create_initial_license.CreateInitialLicenseCommand;
import com.scube.licensing.features.license.partial_save.create_initial_license.CreateInitialLicenseResponse;
import com.scube.licensing.features.license.renewals.LicenseRenewalCommand;
import com.scube.licensing.features.license.validation.CheckLicenseExists;
import com.scube.licensing.features.license.validation.CheckLicenseTypeExists;
import com.scube.licensing.features.participant.dog.DogService;
import com.scube.licensing.features.participant.dto.CreateParticipantResponseDTO;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.validation.NullOrUndefinedToNull;
import com.scube.rabbit.core.AmqpGateway;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("me/license")
@Slf4j
@Validated
@AllArgsConstructor
public class LoggedInUserLicenseController {
    private final AxonGateway axonGateway;
    private final DogService dogService;
    private final AmqpGateway amqpGateway;
    private final LicenseService licenseService;
    private final LicenseFeeService licenseFeeService;

    @PostMapping(value = "create", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.LoggedInUserLicense.CREATE_LICENSE)
    public CreateLicenseResponse createLicense(@AuthenticationPrincipal OpenidClaimSet jwt,
                                               @RequestParam @CheckLicenseTypeExists @Size(max = 255) String licenseType,
                                               @RequestParam @NullOrUndefinedToNull Map<String, String> fields,
                                               @RequestParam Map<String, MultipartFile> files,
                                               @RequestParam(required = false, defaultValue = "1") @Min(0) @Max(3) Integer duration,
                                               @RequestParam(required = false) @Min(2000) @Max(2099) Integer startYear,
                                               @RequestParam(required = false) @Min(2000) @Max(2099) Integer endYear) {
        CreateInitialLicenseResponse pendingLicense = createPendingLicense(licenseType, jwt);
        CreateParticipantResponseDTO dog = addDogToLicense(pendingLicense.entityId(), fields, files);
        CreateFinalLicenseResponse finalLicense = createFinalLicense(pendingLicense.entityId(), duration, startYear, endYear);

        return new CreateLicenseResponse(finalLicense.getEntityType(), finalLicense.getEntityId(), dog.getEntityType(), dog.getEntityId(), dog.getDocuments());
    }

    @PostMapping("pending")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.LoggedInUserLicense.CREATE_PENDING_LICENSE)
    public CreateInitialLicenseResponse createPendingLicense(@RequestParam @CheckLicenseTypeExists @Size(max = 255) String licenseType,
                                                             @AuthenticationPrincipal OpenidClaimSet jwt) {
        return axonGateway.sendAndWait(new CreateInitialLicenseCommand(UUID.fromString(jwt.getSubject()), licenseType));
    }

    @PostMapping("{licenseEntityId}/submit")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserLicense.CREATE_FINAL_LICENSE)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public CreateFinalLicenseResponse createFinalLicense(@PathVariable @CheckLicenseExists final UUID licenseEntityId,
                                                         @RequestParam(required = false, defaultValue = "1") @Min(1) @Max(3) Integer duration,
                                                         @RequestParam(required = false) @Min(2000) @Max(3099) Integer startYear,
                                                         @RequestParam(required = false) @Min(2000) @Max(3099) Integer endYear) {
        return axonGateway.sendAndWait(new CreateFinalLicenseCommand(licenseEntityId, duration, false, startYear, endYear));
    }

    @PostMapping(value = "{licenseEntityId}/add-dog", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.LoggedInUserLicense.ADD_DOG_TO_LICENSE)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public CreateParticipantResponseDTO addDogToLicense(@PathVariable @CheckLicenseExists final UUID licenseEntityId,
                                                        @RequestParam @NullOrUndefinedToNull Map<String, String> fields,
                                                        @RequestParam Map<String, MultipartFile> files) {
        return dogService.addDogToLicense(licenseEntityId, fields, files);
    }

    @PostMapping("{licenseEntityId}/renew")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserLicense.RENEW_LICENSE)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public void renewLicense(@PathVariable @CheckLicenseExists final UUID licenseEntityId,
                             @RequestParam(required = false, defaultValue = "1") @Min(0) @Max(3) Integer duration,
                             @RequestParam(required = false) @Min(2000) @Max(2099) Integer startYear,
                             @RequestParam(required = false) @Min(2000) @Max(2099) Integer endYear) {
        axonGateway.sendAndWait(new LicenseRenewalCommand(licenseEntityId, duration, false, startYear, endYear));
    }

    @DeleteMapping("{licenseEntityId}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserLicense.DELETE_LICENSE_DRAFT)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public void deleteLicenseDraft(@PathVariable final UUID licenseEntityId) {
        //amqpGateway.publish(new DeleteLicenseCommand(entityId));
        licenseService.undoRenewal(licenseEntityId);
    }

    @GetMapping("{licenseEntityId}/fees")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserLicense.GET_LICENSE_FEES)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public ResponseEntity<GetAllLicenseFeeQueryHandler.GetAllLicenseFeeResponse> getLicenseFees(@PathVariable final UUID licenseEntityId) {
        var result = amqpGateway.queryResult(new GetAllLicenseFeesQuery(licenseEntityId));
        if (result.isFailure())
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, result.getErrorMessage());
        return ResponseEntity.ok(result.getResult());
    }

    @GetMapping("{licenseEntityId}/fees/calculate")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserLicense.CALCULATE_LICENSE_FEES)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public FeeCalculationByDurationResponse calculateLicenseFees(@PathVariable final UUID licenseEntityId) {
        return licenseFeeService.getFeePreviews(licenseEntityId, true);
    }

    @PostMapping("{licenseEntityId}/mark-pending-approval")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.LoggedInUserLicense.MARK_LICENSE_AS_PENDING_APPROVAL)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public void markLicenseAsPendingApproval(@PathVariable @CheckLicenseExists final UUID licenseEntityId) {
        licenseService.markAsPendingApproval(licenseEntityId);
    }

    @GetMapping("{licenseEntityId}/license-form")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserLicense.GET_LICENSE_FORM)
    @PreAuthorize("isOwnerOfLicense(#licenseEntityId)")
    public LicenseService.GetLicenseFormResponse getLicenseForm(@PathVariable final UUID licenseEntityId) {
        return licenseService.getLicenseFormResponse(licenseEntityId);
    }
}

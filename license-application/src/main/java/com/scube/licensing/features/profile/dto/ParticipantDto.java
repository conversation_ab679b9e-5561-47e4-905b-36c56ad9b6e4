package com.scube.licensing.features.profile.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.entity_group.dtos.CustomFeeDto;
import com.scube.licensing.features.participant.opt_in.OptInDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
public class ParticipantDto implements IEntityDto, IAssociableDto {
    private UUID entityId;
    private String entityType;
    private List<EventDto> events;
    private String name;
    private String participantType;
    private List<ContactDto> contacts;
    private String status;
    private boolean isActive;
    private boolean registered;
    private List<ParticipantAddressDto> addresses;
    @JsonIgnore
    private Map<String, Object> customFields;
    @JsonIgnore
    private String tableName;
    private String createdBy;
    private String lastModifiedBy;
    private Instant createdDate;
    private Instant lastModifiedDate;
    private List<DocumentDto> documents;
    private List<OptInDto> optIns;
    private CustomFeeDto fees;

    @JsonProperty("avatarUUID")
    public String getAvatarUrl() {
        if (ObjectUtils.isEmpty(documents)) return null;
        return documents.stream()
                .filter(document -> document.getKey().equalsIgnoreCase("AVATAR"))
                .findFirst()
                .map(DocumentDto::getDocumentUuid)
                .orElse(null);
    }

    @JsonProperty("primaryEmail")
    public String getPrimaryEmail() {
        if (ObjectUtils.isEmpty(contacts)) return null;
        return contacts.stream()
                .filter(x -> x.isPrimary() && x.isEmail())
                .findFirst()
                .map(ContactDto::getValue)
                .orElse(null);
    }

    @JsonProperty("primaryPhone")
    public String getPrimaryPhone() {
        if (ObjectUtils.isEmpty(contacts)) return null;
        return contacts.stream()
                .filter(x -> x.isPrimary() && x.isPhone())
                .findFirst()
                .map(ContactDto::getValue)
                .orElse(null);
    }

    @JsonAnyGetter
    public Map<String, Object> getFlattenedFields() {
        Map<String, Object> flattenedFields = new HashMap<>();
        flattenedFields.putAll(getCustomFields());
        flattenedFields.putAll(getOptInMap());
        return flattenedFields;
    }

    @JsonIgnore
    public Map<String, Object> getCustomFields() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    @JsonIgnore
    private Map<String, Boolean> getOptInMap() {
        return optIns.stream()
                .collect(HashMap::new, (m, o) -> m.put(o.getName(), o.isActive()), Map::putAll);
    }

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}
package com.scube.licensing.features.business.dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.features.entity.EntityRequest;
import com.scube.licensing.features.entity.dtos.CreateAddressRequestDto;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.infrastructure.db.entity.business.Business;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


public class BusinessRequest extends EntityRequest<BusinessRequest> {
    public BusinessRequest(Map<String, String> map) {
        super(map, Business.TABLE_NAME);
    }

    @JsonProperty("address")
    public String getAddress() {
        return getAndValidate("address");
    }

    @JsonProperty("address2")
    public String getAddress2() {
        return getAndValidate("address2");
    }

    @JsonProperty("city")
    public String getCity() {
        return getAndValidate("city");
    }

    @JsonProperty("state")
    public String getState() {
        return getAndValidate("state");
    }

    @JsonProperty("zip")
    public String getZip() {
        return getAndValidate("zip");
    }

    @Override
    public List<CreateAddressRequestDto> getAddresses() {
        var result = new ArrayList<CreateAddressRequestDto>();
        if (!ObjectUtils.isEmpty(getAddress())) {
            result.add(CreateAddressRequestDto.createAddress("Home", getAddress(), getAddress2(), getCity(), getState(), getZip()));
        }
        return result;
    }

    @Override
    public List<CreateCustomFieldsDto> getCustomFields() {
        var result = new ArrayList<CreateCustomFieldsDto>();

        getAdditionalFields()
                .forEach((key, value) -> result.add(CreateCustomFieldsDto.createCustomField("object", Business.TABLE_NAME, key, value)));

        return result;
    }
}
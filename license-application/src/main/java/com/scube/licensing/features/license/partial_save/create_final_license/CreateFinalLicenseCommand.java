package com.scube.licensing.features.license.partial_save.create_final_license;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public final class CreateFinalLicenseCommand implements IRequestAxon<CreateFinalLicenseResponse> {
    private UUID entityId;
    @Nullable
    private Integer licenseDuration;
    private boolean autoApproval;
    private Integer startYear;
    private Integer endYear;
}
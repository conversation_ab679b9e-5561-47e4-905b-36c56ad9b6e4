package com.scube.licensing.features.participant.dog.dog_impoundment_notification;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.config_utils.json_storage.JsonStorageValue;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.profile.dto.TenantDto;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.notification.client.model.Email;
import com.scube.notification.client.rabbit.ScheduleEmailFanoutEvent;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class DogImpoundmentNotificationService {
    private final ParticipantService participantService;
    private final ObjectMapper objectMapper;
    private final AmqpGateway amqpGateway;

    @JsonStorageValue({"config=tenant"})
    private TenantDto tenant;

    @Transactional
    public void scheduleDogImpoundmentNotification() {
        log.debug("DogImpoundmentNotificationService.scheduleDogImpoundmentNotification()");
        List<Participant> dogs = participantService.getImpoundedDogNotNotifiedAlready();
        var tenantData = getTenantData();
        for (var dog : dogs) {
            HashMap<String, Object> emailData = new HashMap<>(dog.getProperties());
            emailData.putAll(tenantData);
            emailData.putAll(getOwnerData(dog));
            emailData.putAll(getLicenseData(dog));
            emailData.put("dogImpoundmentDate", dog.getImpoundmentDateAsString());

            var from = "<EMAIL>";
            var to = (String) emailData.get("ownerEmail");
            var emailBodyTemplate = "dogImpoundmentEmailBody";
            var subjectTemplate = "dogImpoundmentEmailSubject";
            var subject = amqpGateway.queryResult(new GenerateTextCommand(subjectTemplate, emailData)).orElseThrow();
            var emailBody = amqpGateway.queryResult(new GenerateTextCommand(emailBodyTemplate, emailData)).orElseThrow();

            ScheduleEmailFanoutEvent emailEvent = ScheduleEmailFanoutEvent.builder()
                    .email(Email.builder()
                            .to(to)
                            .from(from)
                            .subject(subject)
                            .contentType("text/html")
                            .body(emailBody)
                            .build()
                    )
                    .tag((String) emailData.get("licenseType"))
                    .topic("Dog_Impoundment_Notification")
                    .correlationId(String.valueOf(dog.getUuid()))
                    .createdBy("LicenseService")
                    .build();

            amqpGateway.publish(emailEvent);
            dog.markAsDogImpoundmentNotified();
        }
    }

    public Map<String, Object> getTenantData() {
        return objectMapper.convertValue(tenant, new TypeReference<>() {
        });
    }

    public Map<String, Object> getOwnerData(Participant dog) {
        Participant owner = dog.getPrimaryIndividual();
        Map<String, Object> ownerData = new HashMap<>(owner.getProperties());
        ownerData.put("ownerPhoneNumber", owner.getPrimaryPhone());
        ownerData.put("ownerEmail", owner.getPrimaryEmail());
        owner.getHomeAddresses().stream().findFirst()
                .ifPresent(address -> {
                    ownerData.put("ownerStreetAddress", address.getStreetAddress());
                    ownerData.put("ownerCity", address.getCity());
                    ownerData.put("ownerState", address.getState());
                    ownerData.put("ownerZipCode", address.getZip());
                    ownerData.put("ownerFullStreetAddress", "%s, %s, %s %s".formatted(
                            address.getStreetAddress(),
                            address.getCity(),
                            address.getState(),
                            address.getZip()
                    ));
                });
        return ownerData;
    }

    public Map<String, Object> getLicenseData(Participant dog) {
        Map<String, Object> licenseData = new HashMap<>(dog.getProperties());
        dog.getLicenses().stream()
                .filter(license -> !license.isDraft())
                .findFirst()
                .ifPresent(license -> {
                    licenseData.put("licenseNumber", license.getLicenseNumber());
                    licenseData.put("licenseStatus", license.getLicenseStatus().getName());
                    licenseData.put("licenseType", license.getLicenseType().getName());
                });
        return licenseData;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    private static class GenerateTextCommand implements IRabbitFanoutPublisherRpc<String> {
        private String nameKey;
        private Map<String, Object> data;
    }
}
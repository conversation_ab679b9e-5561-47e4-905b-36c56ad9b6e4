package com.scube.licensing.features.association.save_association;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class SaveAssociationCommand implements IRequestAxon<Association> {
    private Associable parent;
    private Associable child;
    private Map<String, Object> properties;

    public SaveAssociationCommand(Associable parent, Associable child, Map<String, Object> properties) {
        this(parent, child);
        this.properties = properties;
    }

    public SaveAssociationCommand(Associable parent, Associable child) {
        if (parent == null || parent.getId() == null || parent.getAssociationType() == null)
            throw new IllegalArgumentException("Parent/parentId cannot be null");

        if (child == null || child.getId() == null || child.getAssociationType() == null)
            throw new IllegalArgumentException("Child/childId cannot be null");
        this.parent = parent;
        this.child = child;
    }
}
package com.scube.licensing.features.merge_request.dtos.me;

import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import lombok.Data;

import java.util.List;

@Data
public class LoggedInUserMergeRequestResponse {
    private List<LoggedInUserMergeRequestResponseLineItem> contents;

    public LoggedInUserMergeRequestResponse(List<MergeRequest> requests) {
        this.contents = requests.stream()
                .map(LoggedInUserMergeRequestResponseLineItem::new)
                .toList();
    }
}
package com.scube.licensing.features.license.validation;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.license.License;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.scube.licensing.features.license.validation.CheckNotMoreThanThreeLicensesValidator.checkIfInStatus;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HouseHoldCountResponse {
    private Set<HouseHoldAddress> pendingLicensesByAddress = new HashSet<>();
    private Set<HouseHoldAddress> currentHouseHoldLicensesByAddress = new HashSet<>();

    @JsonProperty("pendingCount")
    public int getPendingCount() {
        return pendingLicensesByAddress.stream()
                .flatMap(address -> address.getLicenses().stream())
                .toList()
                .size();
    }

    @JsonProperty("houseHoldCount")
    public int getCurrentHouseHoldCount() {
        return currentHouseHoldLicensesByAddress.stream()
                .flatMap(address -> address.getLicenses().stream())
                .toList()
                .size();
    }

    public HouseHoldCountResponse(Map<Address, List<License>> houseHoldMap) {
        houseHoldMap.forEach((address, licenses) -> licenses.forEach(license -> {
            Set<HouseHoldLicense> pendingLicense = new HashSet<>();
            Set<HouseHoldLicense> holdHoldLicense = new HashSet<>();
            if (checkIfInStatus(license)) {
                holdHoldLicense.add(new HouseHoldLicense(license));
            } else {
                pendingLicense.add(new HouseHoldLicense(license));
            }

            add(pendingLicensesByAddress, address, pendingLicense);
            add(currentHouseHoldLicensesByAddress, address, holdHoldLicense);
        }));
    }

    private void add(Set<HouseHoldAddress> houseHoldAddresses, Address address, Set<HouseHoldLicense> licenses) {
        // check if the address already exists in the list
        houseHoldAddresses.stream()
                .filter(houseHoldAddress -> houseHoldAddress.equalAddress(address))
                .findFirst()
                .ifPresentOrElse(
                        houseHoldAddress -> houseHoldAddress.addAll(licenses),
                        () -> houseHoldAddresses.add(new HouseHoldAddress(address, licenses))
                );
    }
}
package com.scube.licensing.features.participant.dog;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.client.documentTemplate.generated.DocumentTemplateServiceConnection;
import com.scube.config_utils.json_storage.JsonStorageValue;
import com.scube.licensing.features.association.find_all_child_associations_by_parent.FindAllChildAssociationsByParentQuery;
import com.scube.licensing.features.code_lookup.CodeLookupController;
import com.scube.licensing.features.code_lookup.client.ICodeLookupExchange;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.features.entity.AssociableService;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.license.change_status.ChangeStatusHandler;
import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.features.participant.dog.dto.CreateDogRequestDto;
import com.scube.licensing.features.participant.dog.dto.DogSighting;
import com.scube.licensing.features.participant.dto.CreateParticipantResponseDTO;
import com.scube.licensing.features.participant.type.ParticipantTypeGroupService;
import com.scube.licensing.features.profile.dto.TenantDto;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import com.scube.notification.client.model.Email;
import com.scube.notification.client.rabbit.ScheduleEmailFanoutEvent;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;
import com.scube.multi.tenant.TenantContext;

import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class DogService {
    private final AxonGateway axonGateway;
    private final ParticipantTypeGroupService participantTypeGroupService;
    private final ParticipantService participantService;
    private final DocumentService documentService;
    private final LicenseService licenseService;
    private final DocumentTemplateServiceConnection documentTemplateServiceConnection;
    private final ObjectMapper objectMapper;
    private final AmqpGateway amqpGateway;
    private final AssociableService associableService;
    private final ICodeLookupExchange codeLookupControllerExchange;

    @JsonStorageValue({"config=tenant"})
    private TenantDto tenantDto;

    @NotNull
    protected List<Participant> getAssociatedDogs(Associable profile) {
        List<Association> participantAssociations = getAssociations(profile)
                .stream()
                .filter(association -> association.getChildAssociationType().equals(AssociationType.PARTICIPANT))
                .toList();

        List<Participant> dogs = participantAssociations.stream()
                .map(association -> axonGateway.query(new ParticipantService.FindParticipantByIdOrElseThrowQuery(association.getChildId())))
                .filter(participant -> participant.getParticipantTypeGroup().getParticipantGroup().getName().equals("Dog"))
                .collect(Collectors.toList());

        return dogs;
    }

    protected void changeAllLicenseStatusesForADog(Associable dog, String status, String modifier) {
        var associations = getAssociations(dog);

        associations.stream()
                .filter(association -> association.getChildAssociationType().equals(AssociationType.LICENSE))
                .forEach(association -> {
                    License license = axonGateway.query(new LicenseService.FindLicenseByIdOrElseThrowQuery(association.getChildId()));

                    if (license.getLicenseType().getName().equalsIgnoreCase("Purebred License")) {
                        List<Participant> dogs = getAssociatedDogs(license)
                                .stream()
                                .filter(dogg -> dogg.getParticipantStatus().getName().equalsIgnoreCase("Active"))
                                .toList();

                        //If its purebred and there is no active dogs cancel the license
                        if (dogs.isEmpty() && status.equals("Canceled")) {
                            changeLicenseStatus(license, status, modifier);
                        }
                    } else {
                        changeLicenseStatus(license, status, modifier);
                    }
                });
    }

    private List<Association> getAssociations(Associable profile) {
        return axonGateway.query(new FindAllChildAssociationsByParentQuery(profile));
    }

    private void changeLicenseStatus(License license, String status, String modifier) {
        axonGateway.send(new ChangeStatusHandler.ChangeLicenseStatusWithModifierCommand(license, status, modifier, false));
    }

    public CreateParticipantResponseDTO createDog(Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Creating dog with fields: {}", fields);
        var requestDto = new CreateDogRequestDto(fields);
        var participant = createAndSaveParticipant(requestDto);

        var result = createResponseDTO(participant);

        List<DocumentDto> documents = documentService.upload(participant, files);
        result.setDocuments(documents);

        finalizeParticipant(participant);

        log.info("Done creating dog");
        return result;
    }

    public CreateParticipantResponseDTO addDogToLicense(UUID licenseEntityId, Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Adding dog to license with id: {}", licenseEntityId);
        var requestDto = new CreateDogRequestDto(fields);
        var participant = createAndSaveParticipant(requestDto);

        licenseService.addDogToLicense(licenseEntityId, participant, requestDto);

        var result = createResponseDTO(participant);

        List<DocumentDto> documents = documentService.upload(participant, files);
        result.setDocuments(documents);

        finalizeParticipant(participant);

        log.info("Dog added to license with id: {}", licenseEntityId);
        return result;
    }

    private Participant createAndSaveParticipant(CreateDogRequestDto requestDto) {
        var participant = new Participant();
        var participantTypeGroup = participantTypeGroupService.getParticipantTypeGroup("Normal Dog", "Dog");
        participant.setParticipantTypeGroup(participantTypeGroup);

        associableService.upsertAssociable(participant, requestDto);

        participantService.save(participant);

        for (CreateCustomFieldsDto custom : requestDto.getCustomFields()) {
            var type = PropertyTypeEnum.fromValue(custom.getType());
            participant.setProperty(type, custom.getKey(), custom.getValue());
        }

        var tagNumber = requestDto.getTagNumber();
        if (!ObjectUtils.isEmpty(tagNumber)) {
            //reset the old tag number
            participant.getTagNumber().ifPresent(tag -> {
                codeLookupControllerExchange.updateTag(new CodeLookupController.TagUpdateRequest(tag,"tag",null,CodeLookupActionEnum.CREATE,TenantContext.getTenantId()));
            });
            // make the new tag number as a lookup
            codeLookupControllerExchange.updateTag(new CodeLookupController.TagUpdateRequest(tagNumber,"dog",participant.getUuid().toString(),CodeLookupActionEnum.LOOKUP,TenantContext.getTenantId()));
        }

        return participant;
    }

    private CreateParticipantResponseDTO createResponseDTO(Participant participant) {
        var entityId = participant.getUuid();
        var participantGroup = participant.getParticipantTypeGroup().getParticipantGroup().getName();
        return new CreateParticipantResponseDTO(entityId, participantGroup);
    }

    private void finalizeParticipant(Participant participant) {
        participantService.markDogApproval(participant);
        participantService.save(participant);
    }

    public void reportSighting(UUID entityId, DogSighting dogSighting) {
        Participant dog = participantService.getParticipantOrElseThrow(entityId);

        if (dogSighting.getLocation().getDatetime() == null) {
            dogSighting.getLocation().setDatetime(Instant.now());
        }

        if (dog.getProperties().get("sightings") == null) {
            dog.setProperty("sightings", new ArrayList(List.of(dogSighting)));
        } else {
            List<DogSighting> sightings = (List<DogSighting>) dog.getProperties().get("sightings");
            sightings.add(dogSighting);
            dog.setProperty("sightings", sightings);
        }

        dog.addEvent(new Event("dogSighted", null));

        participantService.save(dog);

        sendSightingEmails(dog, dogSighting);
    }

    public void sendSightingEmails(Participant dog, DogSighting dogSighting) {
        Map sightingData = new HashMap(dog.getProperties());
        sightingData.put("location", dogSighting.getLocation().getLocation());
        sightingData.put("datetime", dogSighting.getLocation().getDatetime()
                .atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("MM/dd/yyyy hh:mma")));
        sightingData.put("reporterName", dogSighting.getReportedBy().getName());
        sightingData.put("reporterEmail", dogSighting.getReportedBy().getEmail());
        sightingData.put("reporterPhone", dogSighting.getReportedBy().getPhone());
        sightingData.put("dogAge", calculateAge(LocalDate.parse((String) dog.getProperty("dogBirthDate").get(), DateTimeFormatter.ofPattern("yyyy-MM-dd"))));
        sightingData.put("clerkXpressUrl", tenantDto.getClerkXpressUrl());

        JsonNode json = objectMapper.valueToTree(sightingData);

        var emailBody = documentTemplateServiceConnection.documentTemplate()
                .fillTextTemplateAsync("LostDogSightingEmailBody", json).block().getTemplate();

        var subject = documentTemplateServiceConnection.documentTemplate()
                .fillTextTemplateAsync("LostDogSightingEmailSubject", json).block().getTemplate();

        // email clerk
        var clerkEmail = Email.builder().from("<EMAIL>").to(tenantDto.getClerkEmail()).subject(subject).contentType("text/html").body(emailBody).build();

        amqpGateway.publish(ScheduleEmailFanoutEvent.builder()
                .email(clerkEmail)
                .tag("dog")
                .topic("Lost-Dog-Sighting-Notify-Clerk")
                .correlationId(dog.getUuid().toString())
                .createdBy("LicenseService")
                .build());

        //email dog owner
        Optional.ofNullable(dog.getLicenses())
                .filter(licenses -> !licenses.isEmpty())
                .map(licenses -> licenses.getFirst())
                .map(License::getLicenseHolders)
                .filter(holders -> !holders.isEmpty())
                .map(holders -> holders.getFirst())
                .map(Participant::getPrimaryEmail)
                .filter(ownerEmail -> ownerEmail != null && !ownerEmail.isEmpty())
                .ifPresent(ownerEmail -> {
                    amqpGateway.publish(ScheduleEmailFanoutEvent.builder()
                            .email(Email.builder().from("<EMAIL>").to(ownerEmail).subject(subject).contentType("text/html").body(emailBody).build())
                            .tag("dog")
                            .topic("Lost-Dog-Sighting-Notify-Owner")
                            .correlationId(dog.getUuid().toString())
                            .createdBy("LicenseService")
                            .build());
                });
    }

    public static int calculateAge(LocalDate birthDate) {
        LocalDate currentDate = LocalDate.now();
        if ((birthDate != null) && (currentDate != null)) {
            return Period.between(birthDate, currentDate).getYears();
        } else {
            return 0;
        }
    }
}

package com.scube.licensing.features.business;

import com.scube.audit.auditable.services.AuditableEntityService;
import com.scube.licensing.features.business.dtos.BusinessDto;
import com.scube.licensing.features.business.dtos.BusinessRequest;
import com.scube.licensing.features.business.mapper.BusinessDtoMapper;
import com.scube.licensing.features.document.service.DocumentService;
import com.scube.licensing.features.entity.AssociableService;
import com.scube.licensing.infrastructure.db.entity.business.Business;
import com.scube.licensing.infrastructure.db.repository.business.BusinessRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class BusinessService extends AuditableEntityService<Long, Business, BusinessRepository> {
    private final AssociableService associableService;
    private final DocumentService documentService;
    private final BusinessDtoMapper mapper;

    public BusinessDto createBusiness(Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Start createBusiness...");
        files = documentService.processFieldToBeDeleted(fields, files);
        var requestDto = new BusinessRequest(fields);
        var business = new Business();
        associableService.upsertAssociable(business, requestDto);
        repository.save(business);
        documentService.upload(business, files);
        var result = mapper.toDto(business);
        log.info("End createBusiness...");
        return result;
    }

    public BusinessDto updateBusiness(Business business, Map<String, String> fields, Map<String, MultipartFile> files) {
        log.info("Start updateBusiness...");
        files = documentService.processFieldToBeDeleted(fields, files);
        var requestDto = new BusinessRequest(fields);
        associableService.updateAssociable(business, requestDto);
        repository.save(business);
        documentService.upload(business, files);
        var result = mapper.toDto(business);
        log.info("End updateBusiness...");
        return result;
    }

    public BusinessDto updateBusiness(UUID entityId, Map<String, String> fields, Map<String, MultipartFile> files) {
        Business business = repository.findByUuidOrThrow(entityId);
        return updateBusiness(business, fields, files);
    }
}
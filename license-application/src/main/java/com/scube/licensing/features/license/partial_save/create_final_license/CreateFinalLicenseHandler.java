package com.scube.licensing.features.license.partial_save.create_final_license;

import com.scube.licensing.features.license.LicenseService;
import com.scube.licensing.features.license.exception.LicenseStatusNotFoundException;
import com.scube.licensing.features.license.fee.license_created_event.LicenseCreatedEvent;
import com.scube.licensing.infrastructure.axon.AxonGateway;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

@Component
@Slf4j
@AllArgsConstructor
public class CreateFinalLicenseHandler implements IRequestHandlerAxon<CreateFinalLicenseCommand, CreateFinalLicenseResponse> {
    private final LicenseService licenseService;
    private final AxonGateway axonGateway;


    @Override
    @CommandHandler
    @Transactional
    public CreateFinalLicenseResponse handle(CreateFinalLicenseCommand command) {
        log.info("Creating final license for entity: {}", command.getEntityId());
        var license = licenseService.findLicenseByEntityId(new LicenseService.FindLicenseByEntityIdQuery(command.getEntityId()))
                .orElseThrow();

        license.setStartEndDuration(command.getStartYear(), command.getEndYear(), command.getLicenseDuration());

        if (command.isAutoApproval()) {
            // clerk is auto approval
            license.markAsApproved();
        } else {
            // clear the approval so that the clerk can approve the renewal
            license.markAsPendingApproval();
        }

        var setting = licenseService.findSettingByLicenseTypeIdOrElseThrow(license.getLicenseType().getId());
        var licenseStatus = setting.getOnFormSubmitLicenseStatus();
        if (ObjectUtils.isEmpty(licenseStatus))
            throw new LicenseStatusNotFoundException("OnFormSubmit license status not found");

        license.setLicenseStatus(licenseStatus);

        licenseService.save(license);

        axonGateway.sendAndWait(new LicenseCreatedEvent(license.getUuid(), license.getLicenseType().getCode(), command.isAutoApproval()));

        log.info("End creating final license for entity: {}", license);

        return new CreateFinalLicenseResponse(license.getUuid(), "license");
    }
}
package com.scube.licensing.features.merge_request.dtos.me;

import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestStatusEnum;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
public class LoggedInUserMergeRequestResponseLineItem {
    private UUID mergeRequestEntityId;
    private String tagNumber;
    private String licenseNumber;
    private MergeRequestStatusEnum status;
    private Instant createdDate;

    public LoggedInUserMergeRequestResponseLineItem(MergeRequest mergeRequest) {
        this.mergeRequestEntityId = mergeRequest.getUuid();
        this.tagNumber = mergeRequest.getTagNumber();
        this.licenseNumber = mergeRequest.getLicenseNumber();
        this.status = mergeRequest.getStatus();
        this.createdDate = mergeRequest.getCreatedDate();
    }
}
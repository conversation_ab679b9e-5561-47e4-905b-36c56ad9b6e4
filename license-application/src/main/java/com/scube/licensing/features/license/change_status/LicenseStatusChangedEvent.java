package com.scube.licensing.features.license.change_status;

import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.util.UUID;

@NoArgsConstructor
@AllArgsConstructor
@Data
public final class LicenseStatusChangedEvent implements IRabbitFanoutPublisher {
    @Serial
    private static final long serialVersionUID = -2051167983581220564L;
    private Long licenseId;
    private UUID licenseEntityId;
    private String licenseType;
    private Long licenseStatusId;
    private String licenseStatusName;

    public LicenseStatusChangedEvent(License license) {
        this.licenseId = license.getId();
        this.licenseEntityId = license.getUuid();
        this.licenseType = license.getLicenseType().getCode();
        this.licenseStatusId = license.getLicenseStatus().getId();
        this.licenseStatusName = license.getLicenseStatus().getName();
    }
}
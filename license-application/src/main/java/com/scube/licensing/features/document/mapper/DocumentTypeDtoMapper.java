package com.scube.licensing.features.document.mapper;

import com.scube.licensing.features.document.dto.DocumentTypeDto;
import com.scube.licensing.infrastructure.db.entity.document.DocumentType;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class DocumentTypeDtoMapper {
    public abstract DocumentTypeDto toDto(DocumentType documentType);
    public abstract List<DocumentTypeDto> toDto(List<DocumentType> documentTypes);
    public abstract DocumentType toEntity(DocumentTypeDto documentTypeDto);
    public abstract List<DocumentType> toEntity(List<DocumentTypeDto> documentTypeDtos);
}

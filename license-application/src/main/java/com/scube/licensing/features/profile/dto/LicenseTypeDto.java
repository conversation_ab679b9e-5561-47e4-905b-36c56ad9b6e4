package com.scube.licensing.features.profile.dto;

import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LicenseTypeDto {
    private String code;
    private String name;
    private String groupName;

    public static LicenseTypeDto of(LicenseType licenseType) {
        return new LicenseTypeDto(licenseType.getCode(), licenseType.getName(), licenseType.getGroupName());
    }
}
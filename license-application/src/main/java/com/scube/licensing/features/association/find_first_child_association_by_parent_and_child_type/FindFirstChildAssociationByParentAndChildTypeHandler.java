package com.scube.licensing.features.association.find_first_child_association_by_parent_and_child_type;

import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import org.axonframework.queryhandling.QueryHandler;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class FindFirstChildAssociationByParentAndChildTypeHandler implements IRequestHandlerAxon<FindFirstChildAssociationByParentAndChildTypeQuery, Optional<Association>> {
    private final AssociationRepository associationRepository;

    public FindFirstChildAssociationByParentAndChildTypeHandler(AssociationRepository associationRepository) {
        this.associationRepository = associationRepository;
    }

    @Override
    @QueryHandler
    public Optional<Association> handle(FindFirstChildAssociationByParentAndChildTypeQuery query) {
        return associationRepository.findFirstByParentIdAndParentAssociationTypeAndChildAssociationType(
                query.parent().getId(),
                query.parent().getAssociationType(),
                query.childAssociationType()
        );
    }
}
package com.scube.licensing.features.profile.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity.dtos.IEntityDto;
import com.scube.licensing.features.profile.dto.config.IAssociableDto;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Data
public class LicenseDto implements IEntityDto, IAssociableDto {
    private UUID entityId;
    private List<EventDto> events;
    private String licenseNumber;
    private Instant issuedDate;
    private Instant applicationDate;
    private Instant validFromDate;
    private Instant validToDate;
    private LicenseTypeDto licenseType;
    private String description;
    private String status;
    private String activityType;
    private String modifier;
    private boolean approved;
    private Instant approvedDate;
    private String deniedComment;
    private List<String> actions;
    private String createdBy;
    private String lastModifiedBy;
    private Instant createdDate;
    private Instant lastModifiedDate;
    private List<ParticipantDto> dogs;
    private int householdDogLicenseCount;
    private UUID receiptId;

    @JsonIgnore
    private Map<String, Object> customFields;
    @JsonIgnore
    private String tableName;
    private List<DocumentDto> documents;

    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        return customFields.entrySet().stream()
                .filter(entry -> !entry.getKey().equalsIgnoreCase("entityId"))
                .collect(HashMap::new, (m, e) -> m.put(e.getKey(), e.getValue()), Map::putAll);
    }

    public boolean isDraftLicense() {
        return status != null && status.equalsIgnoreCase("draft");
    }

    @Override
    public void putProperties(Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(customFields)) customFields = new HashMap<>();
        customFields.putAll(properties);
    }
}
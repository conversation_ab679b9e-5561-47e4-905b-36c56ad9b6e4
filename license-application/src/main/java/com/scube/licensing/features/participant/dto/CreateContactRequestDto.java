package com.scube.licensing.features.participant.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class CreateContactRequestDto {
    private String type;
    private String group;
    private String value;

    public static CreateContactRequestDto createPhone(String contactType, String value) {
        return CreateContactRequestDto.builder()
                .type(contactType)
                .group("Phone")
                .value(value)
                .build();
    }

    public static CreateContactRequestDto createEmail(String contactType, String value) {
        return CreateContactRequestDto.builder()
                .type(contactType)
                .group("Email")
                .value(value)
                .build();
    }
}
package com.scube.licensing.features.entity_fee.dtos;

import com.scube.licensing.features.entity_fee.mapper.EntityFeeDtoMapper;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

@Data
@NoArgsConstructor
public class GetAllEntityFeeResponse {
    private List<EntityFeeDto> items = new ArrayList<>();
    private BigDecimal totalAmount;
    private BigDecimal totalPaidAmount;
    private BigDecimal totalOutstandingAmount;
    private BigDecimal totalDiscountAmount;
    private BigDecimal totalSubtotal;

    public GetAllEntityFeeResponse(Associable associable, EntityFeeDtoMapper mapper) {
        this(associable, mapper, entityFee -> true);
    }

    public GetAllEntityFeeResponse(Associable associable, EntityFeeDtoMapper mapper, Predicate<? super EntityFee> entityFeeFilter) {
        var entityFees = associable.entityFees();
        var fees = entityFees.getFees();
        if (!ObjectUtils.isEmpty(fees)) {
            this.items = fees.stream()
                    .filter(entityFeeFilter)
                    .map(mapper::toDto)
                    .toList();
        }
        this.totalAmount = entityFees.getTotalAmount();
        this.totalPaidAmount = entityFees.getTotalPaidAmount();
        this.totalOutstandingAmount = entityFees.getTotalOutstandingAmount();
        this.totalDiscountAmount = entityFees.getTotalDiscountAmount();
        this.totalSubtotal = entityFees.getSubTotal();
    }
}
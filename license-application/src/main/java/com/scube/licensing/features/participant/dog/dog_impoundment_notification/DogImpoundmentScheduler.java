package com.scube.licensing.features.participant.dog.dog_impoundment_notification;

import com.scube.scheduling.lib.IScheduledTask;
import com.scube.scheduling.lib.ScheduledTaskConfig;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
@ScheduledTaskConfig(cronExpression = "0 * * * * *", schedulerName = "dogImpoundmentScheduler")
@Profile("!test")
public class DogImpoundmentScheduler implements IScheduledTask {
    private final DogImpoundmentNotificationService service;

    @Override
    public void execute() {
        service.scheduleDogImpoundmentNotification();
    }
}
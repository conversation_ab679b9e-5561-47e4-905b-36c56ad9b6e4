package com.scube.licensing.features.profile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Set;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class RejectedFieldsService {

    private final ProfileService profileService;

    public void addToRejectedFieldList(String entityType, UUID entityId, Set<String> rejectedFields, boolean isResident) {
        var profile = profileService.getProfileOrElseThrow(entityType, entityId);
        if (isResident) {
            profile.reviewFields().addAll(rejectedFields);
            profile.rejectedFields().removeAll(rejectedFields);
        } else {
            profile.rejectedFields().addAll(rejectedFields);
            profile.reviewFields().removeAll(rejectedFields);
        }
        profileService.save(profile);
    }

    public void removeFromRejectedFieldList(String entityType, UUID entityId, Set<String> rejectedFields, boolean isResident) {
        var profile = profileService.getProfileOrElseThrow(entityType, entityId);
        if (isResident)
            profile.rejectedFields().removeAll(rejectedFields);
        else {
            profile.rejectedFields().removeAll(rejectedFields);
            profile.reviewFields().removeAll(rejectedFields);
        }
        profileService.save(profile);
    }

    public void clearRejectedFieldList(String entityType, UUID entityId) {
        var profile = profileService.getProfileOrElseThrow(entityType, entityId);
        profile.rejectedFields().clear();
        profile.reviewFields().clear();
        profileService.save(profile);
    }

    @Transactional(readOnly = true)
    public Set<String> getRejectedFieldList(String entityType, UUID entityId) {
        var profile = profileService.getProfileOrElseThrow(entityType, entityId);
        return profile.rejectedFields().getAll();
    }
}
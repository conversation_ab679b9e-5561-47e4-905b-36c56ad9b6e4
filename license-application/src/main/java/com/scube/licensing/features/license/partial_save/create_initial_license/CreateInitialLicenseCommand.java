package com.scube.licensing.features.license.partial_save.create_initial_license;

import com.scube.licensing.features.license.validation.CheckLicenseTypeExists;
import com.scube.licensing.features.participant.validation.CheckParticipantExists;
import com.scube.licensing.infrastructure.axon.request.IRequestAxon;

import java.util.UUID;

public record CreateInitialLicenseCommand(
        @CheckParticipantExists
        UUID participantId,

        @CheckLicenseTypeExists
        String licenseType) implements IRequestAxon<CreateInitialLicenseResponse> {
}
package com.scube.licensing.features.settings.dto;

import com.scube.licensing.features.settings.entity.AppProperty;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper
public interface AppPropertyMapper {
    AppPropertyDto toDto(AppProperty appProperty);

    AppProperty toEntity(AppPropertyDto appPropertyDto);

    List<AppPropertyDto> toDto(List<AppProperty> appProperties);

    List<AppProperty> toEntity(List<AppPropertyDto> appPropertyDtos);
}
package com.scube.licensing.features.code_lookup;

import com.scube.licensing.features.code_lookup.dto.CodeLookupResponse;
import com.scube.licensing.features.code_lookup.dto.CodeLookupResponseLineItem;
import com.scube.licensing.features.code_lookup.mapper.CodeLookupMapper;
import com.scube.licensing.features.permission.Permissions;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookup;
import com.scube.licensing.infrastructure.db.entity.code_lookup.CodeLookupActionEnum;
import com.scube.multi.tenant.TenantContext;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/code-lookup")
@RequiredArgsConstructor
@Validated
public class CodeLookupController {
    private final CodeLookupService codeLookupService;
    private final CodeLookupMapper codeLookupMapper;

    @GetMapping("/entity/{entityType}/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.CodeLookup.GET_BY_ENTITY_TYPE_AND_ENTITY_ID)
    public CodeLookupResponse getByEntityTypeAndEntityId(@PathVariable @Size(max = 255) @NotBlank String entityType,
                                                         @Size(max = 255) @NotBlank @PathVariable String entityId,
                                                         @RequestParam(required = false) CodeLookupActionEnum action) {
        return codeLookupService.getByEntityTypeAndEntityId(entityType, entityId, action);
    }

    @PostMapping("/create-or-lookup")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.CodeLookup.CREATE_OR_LOOKUP)
    public CodeLookupResponseLineItem createOrLookup(@RequestBody CreateOrLookupCodeLookupRequest request) {
        CodeLookup codeLookup = null;
        if (ObjectUtils.isEmpty(request.entityType)) {
            codeLookup = codeLookupService.createOrLookup(request.action(), request.tenant());
        } else {
            codeLookup = codeLookupService.createOrLookup(request.entityType(), request.entityId(), request.action(), request.tenant());
        }
        return codeLookupMapper.toDto(codeLookup);
    }

    @PostMapping("/create")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.CodeLookup.CREATE)
    public CodeLookupResponseLineItem create(@RequestBody CreateCodeLookupRequest request) {
        var codeLookup = codeLookupService.createByEntity(request.entityType(), request.entityId(), request.action(), request.tenant());
        return codeLookupMapper.toDto(codeLookup);
    }

    @PostMapping("/batch-tag-create/{count}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.CodeLookup.BATCH_TAG_CREATE)
    public ResponseEntity<Resource> batchTagCreate(@PathVariable int count) {
        var resource = codeLookupService.batchTagCreate(count, TenantContext.getTenantId());
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=batch_tags.csv")
                .contentType(MediaType.parseMediaType("text/csv"))
                .body(resource);
    }

    @PatchMapping("/tag-update")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.CodeLookup.TAG_UPDATE)
    public void updateTag(
            @RequestBody TagUpdateRequest request
    ) {
        codeLookupService.update(request.code(), request.entityType(), request.entityId(), request.action(), request.tenant());
    }

    public record CreateOrLookupCodeLookupRequest(@NotBlank @Size(max = 255) String entityType,
                                                  @NotBlank @Size(max = 255) String entityId,
                                                  @NotBlank @Size(max = 255) String action,
                                                  @NotBlank @Size(max = 255) String tenant) {
    }

    public record CreateCodeLookupRequest(@NotBlank @Size(max = 255) String entityType,
                                          @NotBlank @Size(max = 255) String entityId,
                                          @NotBlank @Size(max = 255) String action,
                                          @NotBlank @Size(max = 255) String tenant) {
    }

    public record TagUpdateRequest(@NotBlank @Size(max = 255) String code,
                                   @NotBlank @Size(max = 255) String entityType,
                                   @NotBlank @Size(max = 255) String entityId,
                                   @NotBlank @Size(max = 255) CodeLookupActionEnum action,
                                   @NotBlank @Size(max = 255) String tenant) {
    }
}
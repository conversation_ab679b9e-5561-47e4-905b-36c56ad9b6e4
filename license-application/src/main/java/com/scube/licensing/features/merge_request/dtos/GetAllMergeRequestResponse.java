package com.scube.licensing.features.merge_request.dtos;

import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.licensing.features.merge_request.MergeRequestUtil;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestExistingUser;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class GetAllMergeRequestResponse {
    private List<GetAllMergeRequestResponseLineItem> content = new ArrayList<>();

    public GetAllMergeRequestResponse(List<MergeRequest> requests, ProfileService profileService) {
        Map<UUID, Participant> residents = MergeRequestUtil.buildResident(requests, profileService);

        Map<UUID, List<MergeRequest>> requestsByRequestedIndividualId = requests.stream()
                .collect(Collectors.groupingBy(MergeRequest::getRequestedUserId));

        for (Map.Entry<UUID, List<MergeRequest>> entry : requestsByRequestedIndividualId.entrySet()) {
            UUID requestedUserId = entry.getKey();
            List<MergeRequest> requestsByRequestedUserId = entry.getValue();
            this.content.add(new GetAllMergeRequestResponseLineItem(requestedUserId, requestsByRequestedUserId, residents));
        }

        content.removeIf(x -> ObjectUtils.isEmpty(x.getEntityId()) || x.foundResidentCount == 0);
    }

    @Data
    @NoArgsConstructor
    public static class GetAllMergeRequestResponseLineItem {
        private UUID entityId;
        private String firstName;
        private String lastName;
        private String email;
        private String phone;
        private long foundResidentCount;
        private Instant createdDate;

        public GetAllMergeRequestResponseLineItem(UUID requestedUserId, List<MergeRequest> requests, Map<UUID, Participant> residents) {
            Participant resident = residents.getOrDefault(requestedUserId, null);
            if (resident == null) return;
            this.entityId = requestedUserId;
            this.firstName = resident.getFirstName();
            this.lastName = resident.getLastName();
            this.email = resident.getPrimaryEmail();
            this.phone = resident.getPrimaryPhone();
            this.foundResidentCount = requests.stream()
                    .flatMap(x -> x.getExistingUsers().stream())
                    .filter(MergeRequestExistingUser::isExact)
                    .map(MergeRequestExistingUser::getExistingUserId)
                    .distinct()
                    .count();
            this.createdDate = requests.stream()
                    .filter(x -> x.getCreatedDate() != null)
                    .max(Comparator.comparing(AuditableBase::getCreatedDate))
                    .map(AuditableBase::getCreatedDate)
                    .orElse(null);
        }
    }
}
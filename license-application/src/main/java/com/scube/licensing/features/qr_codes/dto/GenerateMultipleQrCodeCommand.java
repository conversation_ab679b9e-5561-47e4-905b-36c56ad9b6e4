package com.scube.licensing.features.qr_codes.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.licensing.features.code_lookup.dto.CodeLookupRequest;
import com.scube.licensing.features.qr_codes.rabbit.GenerateQrCodeCommand;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GenerateMultipleQrCodeCommand extends CodeLookupRequest {
    private List<GenerateQrCodeCommand> requests;
}
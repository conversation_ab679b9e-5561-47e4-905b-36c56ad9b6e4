package com.scube.licensing.features.entity_fee;

import com.scube.audit.auditable.services.AuditableEntityService;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.entity.AssociableService;
import com.scube.licensing.features.entity.dtos.EntityTypeEnum;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeRequest;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.entity_fee.mapper.EntityFeeDtoMapper;
import com.scube.licensing.features.profile.ProfileService;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.licensing.infrastructure.db.repository.entity_fee.EntityFeeRepository;
import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class EntityFeeService extends AuditableEntityService<Long, EntityFee, EntityFeeRepository> {
    private final ProfileService profileService;
    private final EntityFeeDtoMapper mapper;
    private final AssociableService associableService;
    private final EntityManager entityManager;


    public EntityFeeDto createFee(EntityFeeRequest request) {
        log.info("Start addFee...");
        EntityFee fee = new EntityFee();
        fee.createFee(request);
        repository.save(fee);
        log.info("End addFee...");
        return mapper.toDto(fee);
    }

    public EntityFeeDto updateFee(EntityFee fee, EntityFeeRequest request) {
        log.info("Start updateFee with feeEntityId {}", fee.getUuid());
        fee.updateFee(request);
        repository.save(fee);
        log.info("End updateFee...");
        return mapper.toDto(fee);
    }

    public EntityFeeDto updateFee(UUID feeEntityId, EntityFeeRequest request) {
        EntityFee fee = repository.findByUuidOrThrow(feeEntityId);
        return updateFee(fee, request);
    }

    public GetAllEntityFeeResponse getAllFees(String entityType, UUID entityId) {
        log.info("Start getAllFee with entityType: {}, entityId: {}", entityType, entityId);
        Associable associable = profileService.getProfileOrElseThrow(entityType, entityId);
        GetAllEntityFeeResponse response = new GetAllEntityFeeResponse(associable, mapper);
        log.info("End getAllFee...");
        return response;
    }

    public List<EntityFeeDto> getUnPaidFees(EntityTypeEnum entityType, UUID entityId) {
        log.info("Start getUnPaidFees with entityType: {}, entityId: {}", entityType, entityId);
        Associable associable = profileService.getProfileOrElseThrow(entityType.getKey(), entityId);
        List<EntityFee> fees = associable.getEntityFees().stream().filter(EntityFee::isUnpaid).toList();
        List<EntityFeeDto> response = mapper.toDto(fees);
        log.info("End getUnPaidFees...");
        return response;
    }

    public EntityFeeDto getFee(UUID feeEntityId) {
        log.info("Start getFee with feeEntityId {}", feeEntityId);
        var fee = repository.findByUuidOrThrow(feeEntityId);
        var response = mapper.toDto(fee);
        log.info("End getFee...");
        return response;
    }

    public void deleteFee(UUID feeEntityId) {
        log.info("Start deleteFee with feeEntityId {}", feeEntityId);
        EntityFee fee = repository.findByUuidOrThrow(feeEntityId);
        fee.removeAssociations();
        repository.delete(fee);
        log.info("End deleteFee...");
    }

    public void updateEntityFeeAsPaid(OrderInvoiceResponse orderInvoice) {
        log.info("Start updateEntityFeeAsPaid with orderInvoiceResponse {}", orderInvoice);

        List<UUID> entityFeeIds = orderInvoice.getItems()
                .stream()
                .filter(item -> item.getProperties().containsKey("entityFeeIds"))
                .map(item -> (List<String>) item.getProperties().get("entityFeeIds"))
                .flatMap(Collection::stream)
                .map(UUID::fromString)
                .toList();
        var entityFees = repository.findByUuidIn(entityFeeIds);
        for (EntityFee entityFee : entityFees) {
            entityFee.markAsPaid(orderInvoice.getOrderId(), orderInvoice.getOrderNumber());
            if (entityFee.isRecurringActive()) {
                var newEntityFee = entityFee.cloneEntityFee(repository); // important here
                Optional<LocalDateTime> endDate = entityFee.getRecurringEndDate(); // get date b4 update
                // update the paid entity Fee
                entityFee.setRecurringEndDate(LocalDateTime.now());
                entityFee.pauseRecurringFee();

                // update the newly created entity Fee
                newEntityFee.setRecurringStartDate(LocalDateTime.now());
                endDate.ifPresent(newEntityFee::setRecurringEndDate);
                newEntityFee.setAmount(BigDecimal.ZERO);
                newEntityFee.setPaidDate(null);
            }
        }

        log.info("End updateParticipantFeeAsPaid...");
    }

    public Boolean updateEntityFeeWithOrderId(EntityFeeDto entityFeeResponse, UUID orderId) {
        log.info("Start updateEntityFeeWithOrderId with entityFeeResponse {} and orderId {}", entityFeeResponse, orderId);
        EntityFee participantFee = repository.findByUuidOrThrow(entityFeeResponse.getEntityId());
        participantFee.setOrderId(orderId);
        repository.save(participantFee);
        log.info("End updateParticipantFeeWithOrderId...");
        return true;
    }

    public boolean isOwnerOfEntityFee(UUID entityFeeId, UUID userId) {
        log.info("Checking if user is owner of entity fee: {}", entityFeeId);
        EntityFee entityFee = repository.findByUuidOrElseNull(entityFeeId);
        if (entityFee == null) return false;
        return entityFee.permissions().hasOwner(userId);
    }

    public List<DocumentDto> addFiles(UUID feeEntityId, Map<String, MultipartFile> files) {
        log.info("Start addFiles with feeEntityId {}", feeEntityId);
        EntityFee fee = repository.findByUuidOrThrow(feeEntityId);
        return associableService.addFiles(fee, files);
    }

    public EntityFeeDto addAssociation(UUID feeEntityId, EntityFeeRequest request) {
        EntityFee fee = repository.findByUuidOrThrow(feeEntityId);
        fee.addAssociations(request.getAssociations());
        repository.save(fee);
        return mapper.toDto(fee);
    }

    public List<EntityFee> findAllRecurringFees() {
        return repository.findActiveEntityFeesWithCronExpression();
    }

    public void processRecurringFees() {
        List<EntityFee> entityFees = findAllRecurringFees();
        for (EntityFee entityFee : entityFees) {
            entityFee.processRecurringFee();
        }
    }

    public void markActiveRecurringFeesAsStopped(EntityTypeEnum entityType, UUID entityId) {
        var profile = profileService.getProfileOrElseThrow(entityType.getKey(), entityId);
        var entityFees = profile.getEntityGroups().stream()
                .flatMap(x -> x.getEntityFees().stream())
                .filter(EntityFee::isRecurring)
                .filter(EntityFee::isRecurringActive)
                .toList();
        for (EntityFee entityFee : entityFees) {
            entityFee.pauseRecurringFee();
        }
    }

    public void markActiveRecurringFeesAsResumed(EntityTypeEnum entityType, UUID entityId) {
        var profile = profileService.getProfileOrElseThrow(entityType.getKey(), entityId);
        var entityFees = profile.getEntityGroups().stream()
                .flatMap(x -> x.getEntityFees().stream())
                .filter(EntityFee::isRecurring)
                .filter(EntityFee::isRecurringEndDateInFuture)
                .toList();
        for (EntityFee entityFee : entityFees) {
            entityFee.resumeRecurringFee();
            entityFee.processRecurringFee();
        }
    }

    public void resumeRecurringFee(UUID feeEntityId) {
        log.info("Start resumeRecurringFee with feeEntityId {}", feeEntityId);
        EntityFee entityFee = repository.findByUuidOrThrow(feeEntityId);
        entityFee.resumeRecurringFee();
        entityFee.processRecurringFee();
        repository.save(entityFee);
        log.info("End resumeRecurringFee...");
    }

    public void pauseRecurringFee(UUID feeEntityId) {
        log.info("Start pauseRecurringFee with feeEntityId {}", feeEntityId);
        EntityFee fee = repository.findByUuidOrThrow(feeEntityId);
        fee.pauseRecurringFee();
        repository.save(fee);
        log.info("End pauseRecurringFee...");
    }
}
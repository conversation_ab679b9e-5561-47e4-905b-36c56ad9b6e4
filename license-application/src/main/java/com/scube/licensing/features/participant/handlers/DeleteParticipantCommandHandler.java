package com.scube.licensing.features.participant.handlers;

import com.scube.licensing.features.participant.ParticipantService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@AllArgsConstructor
@Slf4j
public class DeleteParticipantCommandHandler extends FanoutListener<DeleteParticipantCommandHandler.DeleteParticipantCommand> {
    private final ParticipantService participantService;

    public void consume(DeleteParticipantCommand command) {
        log.debug("DeleteParticipantCommandHandler.consume: {}", command.entityId());
        participantService.deleteParticipant(command.entityId());
    }

    public record DeleteParticipantCommand(UUID entityId) implements IRabbitFanoutSubscriber {}
}
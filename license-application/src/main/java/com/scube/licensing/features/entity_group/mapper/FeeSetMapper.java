package com.scube.licensing.features.entity_group.mapper;

import com.scube.lib.misc.BeanUtils;
import com.scube.licensing.features.document.dto.DocumentDto;
import com.scube.licensing.features.document.mapper.DocumentDtoMapper;
import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeDto;
import com.scube.licensing.features.entity_fee.dtos.GetAllEntityFeeResponse;
import com.scube.licensing.features.entity_fee.dtos.Totals;
import com.scube.licensing.features.entity_fee.mapper.EntityFeeDtoMapper;
import com.scube.licensing.features.entity_group.dtos.FeeSetDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.entity_group.EntityGroup;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.lang.Nullable;

import java.util.List;

@Slf4j
@Mapper(componentModel = "spring", uses = {EventMapper.class})
public abstract class FeeSetMapper {
    @Mapping(target = "groupName", source = "entityGroup.name")
    @Mapping(target = "entityId", source = "entityGroup.uuid")
    @Mapping(target = "entityType", constant = EntityGroup.ENTITY_TYPE)
    @Mapping(target = "tableName", constant = EntityGroup.ENTITY_TYPE)
    @Mapping(target = "createdDateTime", source = "entityGroup.createdDate")
    @Mapping(target = "updatedDateTime", source = "entityGroup.lastModifiedDate")
    @Mapping(target = "createdBy", expression = "java(getAssociationByEmail(entityGroup))")
    @Mapping(target = "updatedBy", expression = "java(getAssociationByEmail(entityGroup))")
    @Mapping(target = "associations", expression = "java(getAssociations(entityGroup))")
    @Mapping(target = "customFields", source = "entityGroup.properties")
    @Mapping(target = "label", expression = "java(getLabel(entityGroup))")
    @Mapping(target = "description", expression = "java(getDescription(entityGroup))")
    @Mapping(target = "fees", expression = "java(getAllFee(feeData))")
    @Mapping(target = "totals", expression = "java(getAllFeeTotal(feeData))")
    @Mapping(target = "documents", expression = "java(getDocuments(entityGroup))")
    public abstract FeeSetDto toDto(EntityGroup entityGroup, GetAllEntityFeeResponse feeData);

    public abstract List<FeeSetDto> toDto(List<EntityGroup> entityGroups);

    public List<EntityAssociation> getAssociations(EntityGroup entityGroup) {
        return entityGroup.getParentAssociables().stream()
                .map(EntityAssociation::new)
                .toList();
    }

    public EntityAssociation getAssociationByEmail(EntityGroup entityGroup) {
        var username = entityGroup.getCreatedBy();
        return entityGroup.getParentAssociables().stream()
                .map(EntityAssociation::new)
                .filter(entityAssociation -> username.equals(entityAssociation.getEmail()))
                .findFirst()
                .orElse(null);
    }

    public List<EntityFeeDto> getAllFee(@NotNull GetAllEntityFeeResponse feeData) {
        if (feeData == null) return null;
        return feeData.getItems();
    }

    public Totals getAllFeeTotal(@NotNull GetAllEntityFeeResponse feeData) {
        if (feeData == null) return null;
        Totals totals = new Totals();
        totals.setTotalSubtotal(feeData.getTotalSubtotal());
        totals.setTotalDiscountAmount(feeData.getTotalDiscountAmount());
        totals.setTotalOutstandingAmount(feeData.getTotalOutstandingAmount());
        totals.setTotalAmount(feeData.getTotalAmount());
        totals.setTotalPaidAmount(feeData.getTotalPaidAmount());
        return totals;
    }

    public String getLabel(EntityGroup entityGroup) {
        return (String) entityGroup.getProperties().getOrDefault("label", "");
    }
    public String getDescription(EntityGroup entityGroup) {
        return (String) entityGroup.getProperties().getOrDefault("description", "");
    }

    public GetAllEntityFeeResponse getEntityFeeResponse(@Nullable Associable associable) {
        if (associable == null) return null;
        var mapper = BeanUtils.getBean(EntityFeeDtoMapper.class);
        return new GetAllEntityFeeResponse(associable,mapper);
    }

    public List<DocumentDto> getDocuments(Associable associable) {
        if (associable == null) return null;
        var mapper = BeanUtils.getBean(DocumentDtoMapper.class);
        return associable.getDocuments().stream().map(mapper::toDto).toList();
    }

    public FeeSetDto mapToDto(EntityGroup entityGroup) {
        GetAllEntityFeeResponse feeData = getEntityFeeResponse(entityGroup);
        FeeSetDto dto = toDto(entityGroup, feeData);
        dto.setFeeStatus();
        return dto;
    }
}

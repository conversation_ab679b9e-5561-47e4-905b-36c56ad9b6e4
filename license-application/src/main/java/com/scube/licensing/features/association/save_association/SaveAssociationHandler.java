package com.scube.licensing.features.association.save_association;

import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerAxon;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class SaveAssociationHandler implements IRequestHandlerAxon<SaveAssociationCommand, Association> {
    private final AssociationRepository associationRepository;

    public SaveAssociationHandler(AssociationRepository associationRepository) {
        this.associationRepository = associationRepository;
    }

    @Override
    @CommandHandler
    public Association handle(SaveAssociationCommand command) {
        var existingAssociation = getAssociationByParentAndChild(command.getParent(), command.getChild());
        return existingAssociation.orElseGet(() -> {
            var association = new Association(command.getParent(), command.getChild());
            if (command.getProperties() != null) {
                association.setProperties(command.getProperties());
            }
            return associationRepository.save(association);
        });
    }

    private Optional<Association> getAssociationByParentAndChild(Associable parent, Associable child) {
        return associationRepository.findByParentIdAndParentAssociationTypeAndChildIdAndChildAssociationType(
                parent.getId(),
                parent.getAssociationType(),
                child.getId(),
                child.getAssociationType()
        );
    }
}
package com.scube.licensing.features.code_lookup;

import com.scube.auth.library.ITokenService;
import com.scube.licensing.features.code_lookup.dto.CodeLookupResponseLineItem;
import com.scube.licensing.features.code_lookup.mapper.CodeLookupMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/public/code-lookup")
@RequiredArgsConstructor
@Validated
public class PublicCodeLookupController {
    private final CodeLookupService codeLookupService;
    private final CodeLookupMapper codeLookupMapper;
    private final ITokenService tokenService;

    @GetMapping("/{code}")
    public CodeLookupResponseLineItem getByCode(@PathVariable @Size(max = 30) @NotBlank String code) {
        return codeLookupMapper.toDto(codeLookupService.findByCodeOrElseThrow(code));
    }
}
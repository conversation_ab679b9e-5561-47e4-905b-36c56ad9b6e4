package com.scube.licensing.features.association.delete_association;

import com.scube.licensing.infrastructure.db.repository.association.AssociationRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class DeleteAssociationHandler {
    private final AssociationRepository associationRepository;

    @Transactional
    @CommandHandler
    public void handle(DeleteAssociationCommand command) {
        associationRepository.deleteById(command.association().getId());
    }

    @Transactional
    @CommandHandler
    public void handle(DeleteAllAssociationsCommand command) {
        associationRepository.deleteAllByChildIdAndChildAssociationType(command.associable().getId(), command.associable().getAssociationType());
        associationRepository.deleteAllByParentIdAndParentAssociationType(command.associable().getId(), command.associable().getAssociationType());
    }
}
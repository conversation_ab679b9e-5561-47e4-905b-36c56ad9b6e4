package com.scube.licensing.features.participant.dog;

import com.scube.auth.library.ITokenService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.licensing.features.participant.dog.dto.DogSighting;
import com.scube.multi.tenant.TenantContext;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("public/dog")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.LICENSE_SERVICE)
@Validated
public class PublicDogController {
    private final DogService dogService;
    private final ITokenService tokenService;

    @PostMapping("report-sighting/{dogEntityId}")
    @ResponseStatus(HttpStatus.OK)
    public void reportSighting(@PathVariable @NotNull final UUID dogEntityId, @RequestBody DogSighting dogSighting) {
        final String tenant = "schenectady";

        TenantContext.setTenantId(tenant);

        tokenService.getNewTokenAndAuthenticate(tenant);

        dogService.reportSighting(dogEntityId, dogSighting);

        TenantContext.clear();
    }
}
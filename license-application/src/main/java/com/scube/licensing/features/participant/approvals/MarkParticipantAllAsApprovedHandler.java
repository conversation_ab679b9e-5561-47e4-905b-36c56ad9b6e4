package com.scube.licensing.features.participant.approvals;

import com.scube.licensing.features.participant.ParticipantService;
import com.scube.licensing.infrastructure.axon.handler.IRequestHandlerVoidAxon;
import lombok.RequiredArgsConstructor;
import org.axonframework.commandhandling.CommandHandler;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MarkParticipantAllAsApprovedHandler implements IRequestHandlerVoidAxon<MarkParticipantAllAsApprovedCommand> {
    private final ParticipantService participantService;

    @Override
    @CommandHandler
    public void handle(MarkParticipantAllAsApprovedCommand command) {
        participantService.markAllAsApproved(command.participantEntityIds());
    }
}
package com.scube.licensing.features.entity_note.mapper;

import com.scube.licensing.features.entity_note.dtos.EntityNoteDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.infrastructure.db.entity.entity_note.EntityNote;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Slf4j
@Mapper(componentModel = "spring", uses = {EventMapper.class})
public abstract class EntityNoteDtoMapper {
    @Mapping(target = "entityId", source = "uuid")
    @Mapping(target = "entityType", constant = EntityNote.ENTITY_TYPE)
    @Mapping(target = "tableName", constant = EntityNote.ENTITY_TYPE)
    @Mapping(target = "customFields", source = "properties")
    public abstract EntityNoteDto toDto(EntityNote entityNote);
}
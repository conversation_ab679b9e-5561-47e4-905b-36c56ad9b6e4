package com.scube.licensing.features.business.mapper;

import com.scube.licensing.features.business.dtos.BusinessDto;
import com.scube.licensing.features.profile.mapper.EventMapper;
import com.scube.licensing.infrastructure.db.entity.business.Business;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Slf4j
@Mapper(componentModel = "spring", uses = {EventMapper.class})
public abstract class BusinessDtoMapper {
    @Mapping(target = "entityId", source = "uuid")
    @Mapping(target = "entityType", constant = Business.ENTITY_TYPE)
    @Mapping(target = "tableName", constant = Business.TABLE_NAME)
    @Mapping(target = "customFields", source = "properties")
    public abstract BusinessDto toDto(Business business);
}
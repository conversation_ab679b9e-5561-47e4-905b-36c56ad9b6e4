package com.scube.licensing.features.license.license_actions_query;

import com.scube.licensing.features.license.LicenseService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
@AllArgsConstructor
public class GetLicenseActionsQueryHandler extends FanoutListenerRpc<GetLicenseActionsQueryHandler.GetLicenseActionsQuery, GetLicenseActionsQueryHandler.GetLicenseActionsQueryResponse> {
    private final LicenseService licenseService;

    public RabbitResult<GetLicenseActionsQueryResponse> consume(GetLicenseActionsQuery event) {
        return RabbitResult.of(() -> new GetLicenseActionsQueryResponse(licenseService.getLicenseAction(event.entityId())));
    }

    // @formatter:off
    public record GetLicenseActionsQuery(UUID entityId) implements IRabbitFanoutSubscriberRpc<GetLicenseActionsQueryResponse> { }
    public record GetLicenseActionsQueryResponse(List<String> actions) { }
}

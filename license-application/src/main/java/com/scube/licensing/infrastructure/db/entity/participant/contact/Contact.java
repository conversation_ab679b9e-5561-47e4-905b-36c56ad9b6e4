package com.scube.licensing.infrastructure.db.entity.participant.contact;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(
        name = Contact.TABLE_NAME,
        indexes = {
                @Index(name = "idx_contact_participant_id", columnList = Participant.PARTICIPANT_ID),
                @Index(name = "idx_contact_contact_value", columnList = Contact.CONTACT_VALUE),
                @Index(name = "idx_contact_contact_type_group_id", columnList = ContactTypeGroup.CONTACT_TYPE_GROUP_ID)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class Contact extends BaseEntity {
    public static final String TABLE_NAME = "contact";
    public static final String CONTACT_ID = "contact_id";
    public static final String CONTACT_VALUE = "contact_value";

    @Column(name = CONTACT_VALUE)
    @Size(max = 250)
    private String value;

    @Column(name = ContactTypeGroup.CONTACT_TYPE_GROUP_ID, nullable = false, insertable = false, updatable = false)
    private Long contactTypeGroupId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = ContactTypeGroup.CONTACT_TYPE_GROUP_ID, nullable = false)
    private ContactTypeGroup contactTypeGroup;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = Participant.PARTICIPANT_ID, nullable = false)
    private Participant participant;

    public boolean isEmail() {
        return contactTypeGroup.isEmail();
    }

    public boolean isPhone() {
        return contactTypeGroup.isPhone();
    }

    public boolean isPrimaryEmail() {
        return contactTypeGroup.isPrimaryEmail();
    }

    public boolean isPrimaryPhone() {
        return contactTypeGroup.isPrimaryPhone();
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}
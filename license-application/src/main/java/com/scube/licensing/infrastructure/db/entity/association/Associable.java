package com.scube.licensing.infrastructure.db.entity.association;

import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.licensing.features.association.add_new_association.AddNewAssociationDomainEvent;
import com.scube.licensing.features.association.delete_association.DeleteAssociationDomainEvent;
import com.scube.licensing.features.entity.IEntityRequest;
import com.scube.licensing.features.entity.dtos.CreateCustomFieldsDto;
import com.scube.licensing.features.entity_fee.dtos.EntityFeeRequest;
import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.events.EventToJsonConverter;
import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.licensing.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.envers.Audited;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@MappedSuperclass
@Getter
@Setter
@Accessors(chain = true)
@Audited
public abstract class Associable extends BaseEntity {
    public static final String ASSOCIATION_TYPE = "association_type";

    @Convert(converter = EventToJsonConverter.class)
    @Column(name = "events", columnDefinition = "jsonb")
    @ColumnTransformer(write = "?::jsonb")
    private List<Event> events = new ArrayList<>();

    public void addEvent(Event event) {
        if (ObjectUtils.isEmpty(events)) {
            events = new ArrayList<>();
        }
        events.add(event);
    }

    public void removeEvent(Event event) {
        if (ObjectUtils.isEmpty(events)) {
            return;
        }
        this.events.remove(event);
    }

    public void removeEvent(UUID uuid) {
        if (ObjectUtils.isEmpty(events)) {
            return;
        }
        this.events.removeIf(event -> event.getUuid().equals(uuid));
    }

    /*
     * Associations
     */
    public abstract Set<Association> getChildAssociations();

    public abstract Set<Association> getParentAssociations();


    public abstract AssociationType getAssociationType();

    public abstract String getEntityType();

    public List<Associable> getChildAssociables() {
        return getChildAssociations().stream()
                .map(Association::getChildAssociable)
                .filter(Objects::nonNull)
                .toList();
    }

    public <T extends Associable> List<T> getChildAssociables(Class<T> clazz) {
        return getChildAssociables().stream()
                .filter(clazz::isInstance)
                .map(clazz::cast)
                .toList();
    }

    public List<Associable> getChildAssociablesByType(AssociationType... associationTypes) {
        return getChildAssociations().stream()
                .filter(a -> ObjectUtils.isEmpty(associationTypes) || ObjectUtils.containsElement(associationTypes, a.getChildAssociationType()))
                .map(Association::getChildAssociable)
                .filter(Objects::nonNull)
                .toList();
    }

    public List<Associable> getParentAssociables() {
        return getParentAssociations().stream()
                .map(Association::getParentAssociable)
                .filter(Objects::nonNull)
                .toList();
    }

    public <T extends Associable> List<T> getParentAssociables(Class<T> clazz) {
        return getParentAssociables().stream()
                .filter(clazz::isInstance)
                .map(clazz::cast)
                .toList();
    }

    public List<Associable> getParentAssociablesByType(AssociationType... associationTypes) {
        return getParentAssociations().stream()
                .filter(a -> ObjectUtils.isEmpty(associationTypes) || ObjectUtils.containsElement(associationTypes, a.getParentAssociationType()))
                .map(Association::getParentAssociable)
                .filter(Objects::nonNull)
                .toList();
    }

    public List<Document> getDocuments() {
        return getChildAssociablesByType(AssociationType.DOCUMENT).stream()
                .map(Document.class::cast)
                .toList();
    }

    public List<Address> getAddresses() {
        return getChildAssociablesByType(AssociationType.ADDRESS).stream()
                .map(Address.class::cast)
                .toList();
    }

    public List<Participant> getAssociatedParticipants() {
        return getChildAssociablesByType(AssociationType.PARTICIPANT).stream()
                .filter(Objects::nonNull)
                .map(Participant.class::cast)
                .toList();
    }

    public List<Participant> getDogs() {
        return getChildAssociablesByType(AssociationType.PARTICIPANT).stream()
                .map(Participant.class::cast)
                .filter(Participant::isDog)
                .toList();
    }

    public List<Participant> getIndividuals() {
        return getChildAssociablesByType(AssociationType.PARTICIPANT).stream()
                .map(Participant.class::cast)
                .filter(Participant::isIndividual)
                .toList();
    }

    public Participant getPrimaryIndividual() {
        //get first if exists for now
        return getIndividuals().stream()
                .findFirst()
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "No primary individual found"));
    }

    public List<License> getLicenses() {
        return getChildAssociablesByType(AssociationType.LICENSE).stream()
                .map(License.class::cast)
                .toList();
    }

    public List<EntityFee> getEntityFees() {
        return getChildAssociablesByType(AssociationType.ENTITY_FEE).stream()
                .map(EntityFee.class::cast)
                .toList();
    }

    public List<EntityGroup> getEntityGroups() {
        return getChildAssociablesByType(AssociationType.ENTITY_GROUP).stream()
                .map(EntityGroup.class::cast)
                .toList();
    }

    public void addBiDirectionalAssociable(@Nullable Associable associable) {
        addParentAssociable(associable);
        addChildAssociable(associable);
    }

    public void addBiDirectionalAssociable(@Nullable Associable associable, Map<String, Object> properties) {
        addParentAssociable(associable, properties);
        addChildAssociable(associable, properties);
    }

    public void addBiDirectionalAssociable(@Nullable String entityType, @Nullable UUID entityId) {
        addParentAssociable(entityType, entityId);
        addChildAssociable(entityType, entityId);
    }

    public void addBiDirectionalAssociable(@Nullable String entityType, @Nullable UUID entityId, Map<String, Object> properties) {
        addParentAssociable(entityType, entityId, properties);
        addChildAssociable(entityType, entityId, properties);
    }

    public void addChildAssociable(@Nullable Associable childAssociable) {
        if (ObjectUtils.isEmpty(childAssociable)) return;
        this.registerEvent(new AddNewAssociationDomainEvent(this, childAssociable));
    }

    public void addChildAssociable(@Nullable Associable childAssociable, Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(childAssociable)) return;
        this.registerEvent(new AddNewAssociationDomainEvent(this, childAssociable, properties));
    }

    public void addChildAssociable(@Nullable String childEntityType, @Nullable UUID childEntityId) {
        if (ObjectUtils.isEmpty(childEntityType)) return;
        if (ObjectUtils.isEmpty(childEntityId)) return;
        this.registerEvent(AddNewAssociationDomainEvent.child(this, childEntityType, childEntityId));
    }

    public void addChildAssociable(@Nullable String childEntityType, @Nullable UUID childEntityId, Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(childEntityType)) return;
        if (ObjectUtils.isEmpty(childEntityId)) return;
        this.registerEvent(AddNewAssociationDomainEvent.child(this, childEntityType, childEntityId, properties));
    }

    public void addParentAssociable(@Nullable Associable parentAssociable) {
        if (ObjectUtils.isEmpty(parentAssociable)) return;
        this.registerEvent(new AddNewAssociationDomainEvent(parentAssociable, this));
    }

    public void addParentAssociable(@Nullable Associable parentAssociable, Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(parentAssociable)) return;
        this.registerEvent(new AddNewAssociationDomainEvent(parentAssociable, this, properties));
    }

    public void addParentAssociable(@Nullable String parentEntityType, @Nullable UUID parentEntityId) {
        if (ObjectUtils.isEmpty(parentEntityType)) return;
        if (ObjectUtils.isEmpty(parentEntityId)) return;
        this.registerEvent(AddNewAssociationDomainEvent.parent(this, parentEntityType, parentEntityId));
    }

    public void addParentAssociable(@Nullable String parentEntityType, @Nullable UUID parentEntityId, Map<String, Object> properties) {
        if (ObjectUtils.isEmpty(parentEntityType)) return;
        if (ObjectUtils.isEmpty(parentEntityId)) return;
        this.registerEvent(AddNewAssociationDomainEvent.parent(this, parentEntityType, parentEntityId, properties));
    }

    public void removeBiDirectionalAssociable(Associable associable) {
        if (ObjectUtils.isEmpty(associable)) return;
        removeChildAssociable(associable);
        removeParentAssociable(associable);
    }

    public void removeBiDirectionalAssociable(String entityType, UUID entityId) {
        if (ObjectUtils.isEmpty(entityType)) return;
        if (ObjectUtils.isEmpty(entityId)) return;
        removeChildAssociable(entityType, entityId);
        removeParentAssociable(entityType, entityId);
    }

    public void removeChildAssociable(Associable childAssociable) {
        if (ObjectUtils.isEmpty(childAssociable)) return;
        this.registerEvent(new DeleteAssociationDomainEvent(this, childAssociable));
    }

    public void removeChildAssociable(String childEntityType, UUID childEntityId) {
        if (ObjectUtils.isEmpty(childEntityType)) return;
        if (ObjectUtils.isEmpty(childEntityId)) return;
        this.registerEvent(DeleteAssociationDomainEvent.child(this, childEntityType, childEntityId));
    }

    public void removeParentAssociable(Associable parentAssociable) {
        if (ObjectUtils.isEmpty(parentAssociable)) return;
        this.registerEvent(new DeleteAssociationDomainEvent(parentAssociable, this));
    }

    public void removeParentAssociable(String parentEntityType, UUID parentEntityId) {
        if (ObjectUtils.isEmpty(parentEntityType)) return;
        if (ObjectUtils.isEmpty(parentEntityId)) return;
        this.registerEvent(DeleteAssociationDomainEvent.parent(this, parentEntityType, parentEntityId));
    }

    public void removeAllAssociables() {
        removeAllChildAssociables();
        removeAllParentAssociables();
    }

    public void updateCustomFields(IEntityRequest request) {
        for (CreateCustomFieldsDto custom : request.getCustomFields()) {
            PropertyTypeEnum type = PropertyTypeEnum.fromValue(custom.getType());
            this.setProperty(type, custom.getKey(), custom.getValue());
        }
    }

    public void removeAllChildAssociables() {
        getChildAssociables().forEach(this::removeChildAssociable);
    }

    public void removeAllParentAssociables() {
        getParentAssociables().forEach(this::removeParentAssociable);
    }

    public QRCodeAssociable qrCodes() {
        return new QRCodeAssociable();
    }

    public RejectedFieldsAssociable rejectedFields() {
        return new RejectedFieldsAssociable();
    }

    public ReviewFieldsAssociable reviewFields(){
        return new ReviewFieldsAssociable();
    }

    public EntityFeesAssociable entityFees() {
        return new EntityFeesAssociable();
    }

    public PermissionAssociable permissions() {
        return new PermissionAssociable();
    }

    /*
        QR Codes
     */
    public class QRCodeAssociable {
        public String buildQRCodeProperty(String action) {
            Assert.hasText(action, "Action must not be empty");
            var capitalizedAction = action.substring(0, 1).toUpperCase() + action.substring(1);
            return String.format("qrCode%sDocumentUUID", capitalizedAction);
        }

        public void setQrCodeDocumentUUID(UUID qrCodeDocumentUUID, String action) {
            Associable.this.setProperty(buildQRCodeProperty(action), qrCodeDocumentUUID);
        }

        public boolean hasQrCodeDocumentUUID(String action) {
            return !ObjectUtils.isEmpty(this.getQrCodeDocumentUUID(action));
        }

        public UUID getQrCodeDocumentUUID(String action) {
            return Associable.this.getPropertyAsString(buildQRCodeProperty(action))
                    .filter(ObjectUtils::isEmpty)
                    .map(UUID::fromString)
                    .orElse(null);
        }
    }

    /*
        rejectedField
     */
    public abstract class KeyedFieldSetProperty {
        protected abstract String getPropertyKey();

        public @NotNull Set<String> getAll() {
            var result = getProperty(getPropertyKey()).orElse(new HashSet<>());
            if (ObjectUtils.isEmpty(result)) return new HashSet<>();
            if (result instanceof Collection<?> set)
                return set.stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .collect(Collectors.toSet());
            throw new IllegalStateException("Unable to convert field set to Set<String>");
        }

        public void set(@NotNull Set<String> values) {
            setProperty(getPropertyKey(), values);
        }

        public void addAll(@NotNull Collection<String> values) {
            if (ObjectUtils.isEmpty(values)) return;
            var newSet = getAll();
            newSet.addAll(values);
            set(newSet);
        }

        public void add(@NotNull String... values) {
            addAll(Arrays.asList(values));
        }

        public void removeAll(@NotNull Collection<String> values) {
            if (ObjectUtils.isEmpty(values)) return;
            var newSet = getAll();
            newSet.removeAll(values);
            set(newSet);
        }

        public void remove(@NotNull String... values) {
            removeAll(Arrays.asList(values));
        }

        public void clear() {
            set(new HashSet<>());
        }

        public boolean contains(String... values) {
            if (ObjectUtils.isEmpty(values)) return false;
            return getAll().containsAll(Arrays.asList(values));
        }
    }

    public class RejectedFieldsAssociable extends KeyedFieldSetProperty {
        public static final String REJECTED_FIELDS_PROPERTY =  "rejectedFields";
        @Override
        protected String getPropertyKey() {
            return REJECTED_FIELDS_PROPERTY;
        }
    }


    /*
     * Review Fields
     */
    public class ReviewFieldsAssociable extends KeyedFieldSetProperty {
        public static final String REVIEW_FIELDS_PROPERTY =  "reviewFields";
        @Override
        protected String getPropertyKey() {
            return REVIEW_FIELDS_PROPERTY;
        }
    }


    /*
     * Entity Fees
     */
    public class EntityFeesAssociable {
        public List<EntityFee> getFees() {
            return getChildAssociablesByType(AssociationType.ENTITY_FEE).stream()
                    .map(EntityFee.class::cast)
                    .toList();
        }

        public EntityFee createFee(EntityFeeRequest request) {
            EntityFee fee = new EntityFee();
            fee.createFee(request);
            fee.addParentAssociable(Associable.this);
            return fee;
        }

        public EntityFee getFee(UUID feeEntityId) {
            var entityFees = getFees();
            return entityFees.stream()
                    .filter(f -> f.getUuid().equals(feeEntityId))
                    .findFirst()
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Fee with uuid " + feeEntityId + " not found"));
        }

        public void deleteFee(UUID feeEntityId) {
            var fee = getFee(feeEntityId);
            removeBiDirectionalAssociable(fee);
        }

        public boolean hasAnyPaid() {
            return getFees().stream()
                    .anyMatch(f -> f.getPaymentStatus() == EntityFee.PaymentStatus.PAID);
        }

        public boolean hasAnyUnpaid() {
            return getFees().stream()
                    .anyMatch(f -> f.getPaymentStatus() == EntityFee.PaymentStatus.UNPAID);
        }

        public boolean isUnpaid() {
            return getFees().stream()
                    .allMatch(f -> f.getPaymentStatus() == EntityFee.PaymentStatus.UNPAID);
        }

        public boolean isPaid() {
            return getFees().stream()
                    .allMatch(f -> f.getPaymentStatus() == EntityFee.PaymentStatus.PAID);
        }

        public BigDecimal getTotalAmount() {
            var entityFees = getFees();
            if (ObjectUtils.isEmpty(entityFees)) return BigDecimal.ZERO;
            return entityFees.stream()
                    .map(EntityFee::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        //subTotal
        public BigDecimal getSubTotal() {
            var entityFees = getFees();
            if (ObjectUtils.isEmpty(entityFees)) return BigDecimal.ZERO;
            return entityFees.stream()
                    .map(EntityFee::getAmount)
                    .filter(f -> f.compareTo(BigDecimal.ZERO) > 0)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        public BigDecimal getTotalPaidAmount() {
            var entityFees = getFees();
            if (ObjectUtils.isEmpty(entityFees)) return BigDecimal.ZERO;
            return entityFees.stream()
                    .filter(f -> f.getPaymentStatus() == EntityFee.PaymentStatus.PAID)
                    .map(EntityFee::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        public BigDecimal getTotalDiscountAmount() {
            var entityFees = getFees();
            if (ObjectUtils.isEmpty(entityFees)) return BigDecimal.ZERO;
            return entityFees.stream()
                    .map(EntityFee::getAmount)
                    .filter(f -> f.compareTo(BigDecimal.ZERO) < 0)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        public BigDecimal getTotalOutstandingAmount() {
            var entityFees = getFees();
            if (ObjectUtils.isEmpty(entityFees)) return BigDecimal.ZERO;
            return this.getTotalAmount().subtract(this.getTotalPaidAmount());
        }
    }

    /*
     * Permissions
     */
    public class PermissionAssociable {
        public boolean hasOwner(UUID userId) {
            return hasOwnerRecursive(Associable.this, userId);
        }

        public boolean hasOwnerRecursive(Associable associable, UUID userId) {
            if (ObjectUtils.isEmpty(associable)) return false;
            if (associable.getUuid().equals(userId)) return true;
            return getParentAssociables().stream()
                    .anyMatch(parent -> hasOwnerRecursive(parent, userId));
        }
    }
}
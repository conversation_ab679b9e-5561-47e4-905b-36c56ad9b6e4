package com.scube.licensing.infrastructure.httpclient;

import com.scube.licensing.features.license.fee.sql_fees.Fee;
import com.scube.licensing.features.license.fee.sql_fees.GetFeesQuery;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CalculationService {
    private final AmqpGateway amqpGateway;

    @Cacheable(value = "getFeeByKey", key = "#key", unless = "#result == null")
    public Fee getFeeByKey(String key) {
        var queryResponse = amqpGateway.queryResult(new GetFeesQuery(List.of(key)))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to get fee by key: " + key));
        return Optional.ofNullable(queryResponse.fees())
                .filter(f -> !ObjectUtils.isEmpty(f))
                .map(List::getFirst)
                .orElse(null);
    }
}
package com.scube.licensing.infrastructure.db.repository.participant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ParticipantRepository extends AuditableEntityRepository<Participant, Long> {
    Optional<Participant> findFirstByContactsValueIgnoreCase(@Size(max = 255) String email);

    @Query(nativeQuery = true, value = "SELECT * FROM get_individuals(:searchFirstName, :searchLastName, :searchEmail, :searchPhone, :searchAddress, :searchAddress2, :searchCity, :searchState, :searchZip)")
    List<UUID> getSearchIndividuals(
            @Param("searchFirstName") @Size(max = 255) String searchFirstName,
            @Param("searchLastName") @Size(max = 255) String searchLastName,
            @Param("searchEmail") @Size(max = 255) String searchEmail,
            @Param("searchPhone") @Size(max = 255) String searchPhone,
            @Param("searchAddress") @Size(max = 255) String searchAddress,
            @Param("searchAddress2") @Size(max = 255) String searchAddress2,
            @Param("searchCity") @Size(max = 255) String searchCity,
            @Param("searchState") @Size(max = 255) String searchState,
            @Param("searchZip") @Size(max = 255) String searchZip
    );

    @Query(nativeQuery = true, value = "SELECT * FROM get_dogs(:searchLicenseNumber, :searchDogName, :searchTagNumber, :searchBirthYear, :searchMicrochipNumber, :searchPureBred, :searchBreed, :searchPrimaryColor, :searchSecondaryColor, :searchSex, :searchSpayedOrNeutered)")
    List<UUID> getSearchDogs(
            @Param("searchLicenseNumber") @Size(max = 255) String searchLicenseNumber,
            @Param("searchDogName") @Size(max = 255) String searchDogName,
            @Param("searchTagNumber") @Size(max = 255) String searchTagNumber,
            @Param("searchBirthYear") @Size(max = 255) String searchBirthYear,
            @Param("searchMicrochipNumber") @Size(max = 255) String searchMicrochipNumber,
            @Param("searchPureBred") @Size(max = 255) String searchPureBred,
            @Param("searchBreed") @Size(max = 255) String searchBreed,
            @Param("searchPrimaryColor") @Size(max = 255) String searchPrimaryColor,
            @Param("searchSecondaryColor") @Size(max = 255) String searchSecondaryColor,
            @Param("searchSex") @Size(max = 255) String searchSex,
            @Param("searchSpayedOrNeutered") @Size(max = 255) String searchSpayedOrNeutered
    );

    @Query(nativeQuery = true, value = """
    select * from license.participant p where p.properties->>'tagNumber' = :tagNumber
    """)
    Optional<Participant> findByTagNumber(@Size(max = 255) String tagNumber);
}
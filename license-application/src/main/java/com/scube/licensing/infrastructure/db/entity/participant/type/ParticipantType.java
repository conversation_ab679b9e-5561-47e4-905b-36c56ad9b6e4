package com.scube.licensing.infrastructure.db.entity.participant.type;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(
        name = ParticipantType.TABLE_NAME,
        indexes = {
                @Index(name = "idx_participant_type_name", columnList = ParticipantType.P_NAME)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ParticipantType extends BaseEntity {
    public static final String TABLE_NAME = "participant_type";
    public static final String PARTICIPANT_TYPE_ID = "participant_type_id";
    public static final String P_NAME = "name";

    @Size(max = 100)
    @Column(name = P_NAME, unique = true, length = 100)
    private String name;

    @Size(max = 250)
    @Column(length = 250)
    private String description;

    public ParticipantType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}

package com.scube.licensing.infrastructure.db.entity.merge_request;

import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.util.ObjectUtils;

import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MergeRequestExistingUser {
    private UUID existingUserId;
    private Integer probability;

    @Size(max = 255)
    @Getter(AccessLevel.NONE)
    private String matchType;

    public MergeRequestExistingUser(Map<String, Object> map) {
        var mpExistingUserId = UUID.fromString((String) map.get("existingUserId"));
        var mpProbability = map.get("probability");
        var mpMatchType = map.get("matchType");
        this.existingUserId = ObjectUtils.isEmpty(mpExistingUserId) ? null : mpExistingUserId;
        this.probability = mpProbability == null ? null : Integer.parseInt(mpProbability.toString());
        this.matchType = mpMatchType == null ? null : mpMatchType.toString();
    }

    public boolean isSuggested() {
        return this.probability < 100;
    }

    public boolean isExact() {
        return this.probability == 100;
    }

    public String getMatchType() {
        return this.isSuggested() ? "suggested" : "exact";
    }

    public String getSearchBy() {
        return this.matchType;
    }
}
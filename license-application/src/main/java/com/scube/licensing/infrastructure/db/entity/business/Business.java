package com.scube.licensing.infrastructure.db.entity.business;

import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(
        name = Business.TABLE_NAME,
        indexes = {
                @Index(name = "idx_business_business_uuid", columnList = Business.BUSINESS_UUID)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class Business extends Associable {
    public static final String TABLE_NAME = "business";
    public static final String ENTITY_TYPE = "business";
    public static final String BUSINESS_UUID = "business_uuid";

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.BUSINESS;


    @OneToMany(mappedBy = "parentBusiness", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childBusiness", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}
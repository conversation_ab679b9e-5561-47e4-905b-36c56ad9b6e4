package com.scube.licensing.infrastructure.db.entity.participant;

import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.participant.opt_in.OptInToJsonConverter;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddress;
import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddressType;
import com.scube.licensing.infrastructure.db.entity.participant.contact.Contact;
import com.scube.licensing.infrastructure.db.entity.participant.contact.ContactTypeGroup;
import com.scube.licensing.infrastructure.db.entity.participant.opt_in.OptIn;
import com.scube.licensing.infrastructure.db.entity.participant.status.ParticipantStatus;
import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantTypeGroup;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.springframework.util.ObjectUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Entity
@Table(
        name = Participant.TABLE_NAME,
        indexes = {
                @Index(name = "idx_participant_participant_uuid", columnList = Participant.PARTICIPANT_UUID),
                @Index(name = "idx_participant_participant_type_group_id", columnList = ParticipantTypeGroup.PARTICIPANT_TYPE_GROUP_ID),
                @Index(name = "idx_participant_participant_status_id", columnList = ParticipantStatus.PARTICIPANT_STATUS_ID),
                @Index(name = "idx_participant_registration_code", columnList = Participant.REGISTRATION_CODE)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Audited
@Accessors(chain = true)
public class Participant extends Associable {
    public static final String TABLE_NAME = "participant";
    public static final String PARTICIPANT_ID = "participant_id";
    public static final String P_NAME = "name";
    public static final String PARTICIPANT_UUID = "participant_uuid";
    public static final String REGISTRATION_CODE = "registrationCode";

    @Size(max = 250)
    @Column(name = P_NAME, length = 250)
    private String name;

    @OneToMany(
            mappedBy = "participant",
            cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST},
            orphanRemoval = true
    )
    private Set<Contact> contacts = new HashSet<>();

    @Column(name = ParticipantTypeGroup.PARTICIPANT_TYPE_GROUP_ID, insertable = false, updatable = false)
    private Long participantTypeGroupId;

    @ManyToOne(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST})
    @JoinColumn(name = ParticipantTypeGroup.PARTICIPANT_TYPE_GROUP_ID, nullable = false)
    private ParticipantTypeGroup participantTypeGroup;

    @OneToMany(
            mappedBy = "participant",
            cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH, CascadeType.PERSIST},
            orphanRemoval = true
    )
    private Set<ParticipantAddress> participantAddresses = new HashSet<>();

    @ManyToOne
    @JoinColumn(name = ParticipantStatus.PARTICIPANT_STATUS_ID, nullable = false)
    private ParticipantStatus participantStatus;

    private boolean registered;

    @Column(nullable = false)
    private boolean approved;
    private Instant approvedDate;

    @Convert(converter = OptInToJsonConverter.class)
    @Column(name = "opt_ins", columnDefinition = "jsonb")
    @ColumnTransformer(write = "?::jsonb")
    private List<OptIn> optIns = new ArrayList<>();

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.PARTICIPANT;


    @OneToMany(mappedBy = "parentParticipant", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childParticipant", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();


    public List<Address> getHomeAddresses() {
        return participantAddresses.stream()
                .filter(ParticipantAddress::isHomeAddress)
                .map(ParticipantAddress::getAddress)
                .toList();
    }

    public void markAsApproved() {
        approved = true;
        approvedDate = Instant.now();
    }

    public void clearApproval() {
        approved = false;
        approvedDate = null;
    }

    public boolean isIndividual() {
        return this.getParticipantTypeGroup().getParticipantGroup().getName().equalsIgnoreCase("individual");
    }

    public boolean isOrganization() {
        return this.getParticipantTypeGroup().getParticipantGroup().getName().equalsIgnoreCase("organization");
    }

    public boolean isDog() {
        return this.getParticipantTypeGroup().getParticipantGroup().getName().equalsIgnoreCase("dog");
    }

    public boolean isLostDog() {
        if (!isDog()) return false;
        return getParticipantStatus().getName().equals("Lost");
    }

    public void addContact(Contact contact, ContactTypeGroup contactTypeGroup) {
        contacts.add(contact);
        contact.setParticipant(this);
        contact.setContactTypeGroup(contactTypeGroup);
    }

    public String getPrimaryEmail() {
        return contacts.stream()
                .filter(Contact::isPrimaryEmail)
                .map(Contact::getValue)
                .findFirst()
                .orElse(null);
    }

    public String getPrimaryPhone() {
        return contacts.stream()
                .filter(Contact::isPrimaryPhone)
                .map(Contact::getValue)
                .findFirst()
                .orElse(null);
    }

    public ParticipantAddress addAddress(Address address, ParticipantAddressType participantAddressType) {
        var participantAddress = new ParticipantAddress();
        participantAddress.setParticipant(this);
        participantAddress.setAddress(address);
        participantAddress.setParticipantAddressType(participantAddressType);
        participantAddresses.add(participantAddress);
        return participantAddress;
    }

    public void addUpdateOptIn(Map<String, Boolean> optIns) {
        optIns.forEach(this::addUpdateOptIn);
    }

    public void addUpdateOptIn(String name, boolean active) {
        var optIn = new OptIn(name, active);
        addUpdateOptIn(optIn);
    }

    public void addUpdateOptIn(OptIn optIn) {
        var existingOptIn = optIns.stream()
                .filter(o -> o.getName().equalsIgnoreCase(optIn.getName()))
                .findFirst()
                .orElse(null);
        if (!ObjectUtils.isEmpty(existingOptIn)) {
            existingOptIn.update(optIn.isActive());
            return;
        }

        optIns.add(optIn);
    }

    public String getFirstName() {
        return getPropertyAsString("firstName").orElse(null);
    }

    public String getLastName() {
        return getPropertyAsString("lastName").orElse(null);
    }

    public String getDataOfBirth() {
        return getPropertyAsString("dateOfBirth").orElse(null);
    }

    /*
        Registration Code
     */
    public String getRegistrationCode() {
        return getPropertyAsString(REGISTRATION_CODE).orElse(null);
    }

    public void setRegistrationCode(String registrationCode) {
        setProperty(REGISTRATION_CODE, registrationCode);
    }

    public void mergeByRegistrationCode(Participant matchedParticipant) {
        this.addEvent(new Event("individualChanged", "Merged by registration code: " + matchedParticipant.getRegistrationCode()));
        this.addEvent(new Event("individualChanged", "Merged with participant " + matchedParticipant.getUuid()));
        matchedParticipant.addEvent(new Event("individualChanged", "Merged with participant " + this.getUuid()));
        matchedParticipant.setRegistrationCode(null);
    }

    public String getEntityTypeToLowerCase() {
        var result = getEntityType();
        return result == null ? null : result.toLowerCase();
    }

    @Override
    public String getEntityType() {
        if (ObjectUtils.isEmpty(participantTypeGroup)) return null;
        return participantTypeGroup.getGroupNameToLowerCase();
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public Document getAvatarDocument() {
        if (ObjectUtils.isEmpty(getDocuments())) return null;
        return getDocuments().stream()
                .filter(document -> document.getDocumentType().getKey().equalsIgnoreCase("AVATAR"))
                .findFirst()
                .orElse(null);
    }

    public Optional<String> getTagNumber() {
        return getPropertyAsString("tagNumber");
    }

    public void setImpoundmentDate(String dogImpoundmentDate) {
        this.setProperty("dogImpoundmentNotified", false);
        this.setProperty(PropertyTypeEnum.DATE, "dogImpoundmentDate", dogImpoundmentDate);
    }

    public void clearImpoundmentDate() {
        this.setProperty("dogImpoundmentDate", null);
    }

    public Optional<LocalDate> getImpoundmentDate() {
        return this.getPropertyAsDate("dogImpoundmentDate");
    }

    public String getImpoundmentDateAsString() {
        Optional<LocalDate> date = getImpoundmentDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM dd, yyyy");
        return date.map(d -> d.format(formatter)).orElse("");
    }

    public void markAsDogImpoundmentNotified() {
        this.setProperty("dogImpoundmentNotified", true);
    }
}
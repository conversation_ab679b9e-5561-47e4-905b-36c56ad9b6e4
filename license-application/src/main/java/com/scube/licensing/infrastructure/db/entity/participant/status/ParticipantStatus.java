package com.scube.licensing.infrastructure.db.entity.participant.status;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantGroup;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(name = ParticipantStatus.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ParticipantStatus extends BaseEntity {
    public static final String TABLE_NAME = "participant_status";
    public static final String PARTICIPANT_STATUS_ID = "participant_status_id";
    public static final String C_NAME = "name";

    @ManyToOne
    @JoinColumn(name = ParticipantGroup.PARTICIPANT_GROUP_ID, nullable = false)
    private ParticipantGroup participantGroup;

    @Size(max = 255)
    @Column(name = C_NAME)
    private String name;

    @Enumerated(EnumType.STRING)
    @Column(name = "code", nullable = false)
    private ParticipantStatusCodeEnum code;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public ParticipantStatus(String name, ParticipantStatusCodeEnum code) {
        this.name = name;
        this.code = code;
    }

    public boolean isActive() {
        return code == ParticipantStatusCodeEnum.ACTIVE;
    }

    public boolean isInactive() {
        return code == ParticipantStatusCodeEnum.INACTIVE;
    }
}
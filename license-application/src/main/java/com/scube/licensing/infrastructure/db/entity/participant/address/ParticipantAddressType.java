package com.scube.licensing.infrastructure.db.entity.participant.address;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(name = ParticipantAddressType.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ParticipantAddressType extends BaseEntity {
    public static final String TABLE_NAME = "participant_address_type";
    public static final String PARTICIPANT_ADDRESS_TYPE_ID = "participant_address_type_id";
    public static final String P_NAME = "name";

    @Size(max = 50)
    @Column(unique = true, name = P_NAME)
    private String name;

    @Size(max = 250)
    private String description;

    public ParticipantAddressType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public boolean isHomeAddress() {
        return name.equalsIgnoreCase("Home");
    }
}

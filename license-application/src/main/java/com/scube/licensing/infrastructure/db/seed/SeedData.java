package com.scube.licensing.infrastructure.db.seed;

import com.scube.audit.config.AuditContextHolder;
import com.scube.licensing.infrastructure.db.seed.license.LicenseStatusSeed;
import com.scube.licensing.infrastructure.db.seed.license.LicenseTypeSeed;
import com.scube.licensing.infrastructure.db.seed.participant.ContactGroupTypeSeed;
import com.scube.licensing.infrastructure.db.seed.participant.ParticipantAddressTypeSeed;
import com.scube.licensing.infrastructure.db.seed.participant.ParticipantGroupTypeSeed;
import com.scube.licensing.infrastructure.db.seed.profile.ProfileTypeSeed;
import com.scube.licensing.infrastructure.db.seed.sql.SqlSeed;
import com.scube.licensing.infrastructure.db.seed.validation.PropertyTypeSeed;
import com.scube.multi.tenant.tenancy.tenant_runner.LoopPerTenant;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class SeedData implements CommandLineRunner {
    private final ContactGroupTypeSeed contactGroupTypeSeed;
    private final ParticipantGroupTypeSeed participantGroupTypeSeed;
    private final ParticipantAddressTypeSeed participantAddressTypeSeed;
    private final LicenseTypeSeed licenseTypeSeed;
    private final LicenseStatusSeed licenseStatusSeed;
    private final ProfileTypeSeed profileTypeSeed;
    private final SqlSeed sqlSeed;
    private final PropertyTypeSeed propertyTypeSeed;

    @Override
    @LoopPerTenant
    public void run(String... args) throws Exception {
        log.info("Start seeding data...");
        AuditContextHolder.setAuditorUser("seed");

        contactGroupTypeSeed.seed();
        participantGroupTypeSeed.seed();
        participantAddressTypeSeed.seed();

        licenseStatusSeed.seed();
        licenseTypeSeed.seed();

        //profile builder
        profileTypeSeed.seed();

        propertyTypeSeed.seed();

        //search
        sqlSeed.seed();

        AuditContextHolder.clearAuditUser();

        log.info("End seeding data...");
    }
}

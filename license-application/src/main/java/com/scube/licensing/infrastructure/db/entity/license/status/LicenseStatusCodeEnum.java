package com.scube.licensing.infrastructure.db.entity.license.status;

import lombok.Getter;

@Getter
public enum LicenseStatusCodeEnum {
    PENDING("pending"),
    PENDING_PAYMENT("pending_payment"),
    PENDING_APPROVAL("pending_approval"),
    DRAFT("draft"),
    SUBMITTED("submitted"),
    REJECTED("rejected"),
    APPROVED("approved"),
    ACTIVE("active"),
    EXPIRED("expired"),
    CLOSED("closed"),
    CANCELED("canceled");

    private final String name;

    LicenseStatusCodeEnum(String name) {
        this.name = name;
    }
}
package com.scube.licensing.infrastructure.db;

import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class PostgresService {
    private final JdbcTemplate jdbcTemplate;

    @Cacheable("doesFunctionExist")
    public boolean doesFunctionExist(String functionName) {
        var sql = "SELECT 1 FROM pg_proc WHERE proname ILIKE ?";
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, functionName);
        return !result.isEmpty();
    }
}
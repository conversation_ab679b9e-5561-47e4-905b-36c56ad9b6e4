package com.scube.licensing.infrastructure.db.seed.license;

import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import com.scube.licensing.infrastructure.db.entity.license.type.LicenseTypeSetting;
import com.scube.licensing.infrastructure.db.repository.license.LicenseStatusRepository;
import com.scube.licensing.infrastructure.db.repository.license.LicenseTypeRepository;
import com.scube.licensing.infrastructure.db.repository.license.LicenseTypeSettingRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LicenseTypeSeed {
    public static final String DOG_LICENSE_EXEMPT_FIELD = "dog.licenseExempt";
    public static final String DOG_DOG_SPAYED_OR_NEUTERED_FIELD = "dog.dogSpayedOrNeutered";
    private final LicenseTypeRepository licenseTypeRepository;
    private final LicenseStatusRepository licenseStatusRepository;
    private final LicenseTypeSettingRepository licenseTypeSettingRepository;

    @Autowired
    public LicenseTypeSeed(LicenseTypeRepository licenseTypeRepository, LicenseStatusRepository licenseStatusRepository, LicenseTypeSettingRepository licenseTypeSettingRepository) {
        this.licenseTypeRepository = licenseTypeRepository;
        this.licenseStatusRepository = licenseStatusRepository;
        this.licenseTypeSettingRepository = licenseTypeSettingRepository;
    }

    @Transactional
    public void seed() {
        log.info("Start LicenseTypeSeed.seed()...");

        var dogLicense = licenseTypeRepository.findByCodeIgnoreCase("dogLicense").orElse(null);
        if (dogLicense == null)
            dogLicense = new LicenseType("dogLicense", "Dog License", "Normal dog license type", "Dog");

        dogLicense = licenseTypeRepository.save(dogLicense);

        var licenseTypeSetting = licenseTypeSettingRepository.findByLicenseTypeId(dogLicense.getId()).orElse(null);
        if (licenseTypeSetting == null)
            licenseTypeSetting = new LicenseTypeSetting()
                    .setLicenseType(dogLicense)
                    .setOnInitialFormCreateLicenseStatus(
                            licenseStatusRepository.findByNameIgnoreCase("Draft").orElseThrow()
                    )
                    .setPartialSaveLicenseStatus(
                            licenseStatusRepository.findByNameIgnoreCase("Draft").orElseThrow()
                    )
                    .setOnFormSubmitLicenseStatus(
                            licenseStatusRepository.findByNameIgnoreCase("Pending Payment").orElseThrow()
                    );
        licenseTypeSettingRepository.save(licenseTypeSetting);


        //purebred dog license type
        var pureBred = licenseTypeRepository.findByCodeIgnoreCase("purebredDogLicense").orElse(null);
        if (pureBred == null)
            pureBred = new LicenseType("purebredDogLicense", "Purebred", "purebred dog license type", "Dog");

        pureBred = licenseTypeRepository.save(pureBred);

        var pureBredLicenseTypeSetting = licenseTypeSettingRepository.findByLicenseTypeId(pureBred.getId()).orElse(null);
        if (pureBredLicenseTypeSetting == null)
            pureBredLicenseTypeSetting = new LicenseTypeSetting()
                    .setLicenseType(pureBred)
                    .setOnInitialFormCreateLicenseStatus(
                            licenseStatusRepository.findByNameIgnoreCase("Draft").orElseThrow()
                    )
                    .setPartialSaveLicenseStatus(
                            licenseStatusRepository.findByNameIgnoreCase("Draft").orElseThrow()
                    )
                    .setOnFormSubmitLicenseStatus(
                            licenseStatusRepository.findByNameIgnoreCase("Pending Payment").orElseThrow()
                    );

        licenseTypeSettingRepository.save(pureBredLicenseTypeSetting);

        log.info("End LicenseTypeSeed.seed()...");
    }
}
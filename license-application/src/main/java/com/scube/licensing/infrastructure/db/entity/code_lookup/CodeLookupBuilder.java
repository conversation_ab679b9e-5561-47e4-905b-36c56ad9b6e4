package com.scube.licensing.infrastructure.db.entity.code_lookup;

import com.scube.licensing.infrastructure.db.repository.code_lookup.CodeLookupRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.web.server.ResponseStatusException;

import static com.scube.licensing.utils.code_generator.CGOptions.LOWER_UPPER_ALPHABET_NUMBERS;
import static com.scube.licensing.utils.code_generator.CGOptions.UPPER_ALPHABET_NUMBERS;
import static com.scube.licensing.utils.code_generator.CodeGeneratorUtil.generateCode;
import static org.springframework.http.HttpStatus.NOT_FOUND;

@RequiredArgsConstructor
public class CodeLookupBuilder {
    private final CodeLookupRepository repository;
    private final String tenant;

    /*
        License
    */
    public CodeLookup license(@Nullable String entityId, CodeLookupActionEnum action) {
        var code = generateCode(12, repository::existsByCode, LOWER_UPPER_ALPHABET_NUMBERS);
        return new CodeLookup("license", entityId, action, code, tenant);
    }

    public CodeLookup licenseLookup(String entityId) {
        Assert.notNull(entityId, "Entity id cannot be null");
        return license(entityId, CodeLookupActionEnum.LOOKUP);
    }

    public CodeLookup licenseRenew(String entityId) {
        Assert.notNull(entityId, "Entity id cannot be null");
        return license(entityId, CodeLookupActionEnum.RENEW);
    }

    public CodeLookup licenseCreate() {
        return license(null, CodeLookupActionEnum.CREATE);
    }

    /*
        Individual
     */
    public CodeLookup individual(@Nullable String entityId, CodeLookupActionEnum action) {
        var code = generateCode(12, repository::existsByCode, LOWER_UPPER_ALPHABET_NUMBERS);
        return new CodeLookup("individual", entityId, action, code, tenant);
    }

    public CodeLookup individualLookup(String entityId) {
        Assert.notNull(entityId, "Entity id cannot be null");
        return individual(entityId, CodeLookupActionEnum.LOOKUP);
    }

    public CodeLookup individualRegister() {
        return individual(null, CodeLookupActionEnum.REGISTER);
    }

    /*
        Tags
     */
    public CodeLookup tag(@Nullable String entityId, CodeLookupActionEnum action) {
        var code = generateCode(6, repository::existsByCode, UPPER_ALPHABET_NUMBERS);
        return new CodeLookup("tag", entityId, action, code, tenant);
    }

    public CodeLookup tagLookup(@Nullable String entityId) {
        Assert.notNull(entityId, "Entity id cannot be null");
        return tag(entityId, CodeLookupActionEnum.LOOKUP);
    }

    public CodeLookup tagCreate() {
        return tag(null, CodeLookupActionEnum.CREATE);
    }

    /*
        By Public
     */
    public CodeLookup landingPageCreate() {
        var code = generateCode(8, repository::existsByCode, LOWER_UPPER_ALPHABET_NUMBERS);
        return new CodeLookup("landing_page", null, CodeLookupActionEnum.LANDING_PAGE, code, tenant);
    }

    public CodeLookup loginPageCreate() {
        var code = generateCode(8, repository::existsByCode, LOWER_UPPER_ALPHABET_NUMBERS);
        return new CodeLookup("login_page", null, CodeLookupActionEnum.LOGIN_PAGE, code, tenant);
    }

    public CodeLookup signUpPageCreate() {
        var code = generateCode(8, repository::existsByCode, LOWER_UPPER_ALPHABET_NUMBERS);
        return new CodeLookup("sign_up_page", null, CodeLookupActionEnum.SIGN_UP_PAGE, code, tenant);
    }

    public CodeLookup demoPageCreate() {
        var code = generateCode(8, repository::existsByCode, LOWER_UPPER_ALPHABET_NUMBERS);
        return new CodeLookup("demo_page", null, CodeLookupActionEnum.DEMO_PAGE, code, tenant);
    }

    /*
        By Action
     */
    public CodeLookup licenseByAction(@Nullable String entityId, CodeLookupActionEnum action) {
        return licenseByAction(entityId, action.getValue());
    }

    public CodeLookup licenseByAction(@Nullable String entityId, String action) {
        return switch (CodeLookupActionEnum.fromValue(action)) {
            case LOOKUP -> licenseLookup(entityId);
            case RENEW -> licenseRenew(entityId);
            case CREATE -> licenseCreate();
            default -> throw new ResponseStatusException(NOT_FOUND, "Cannot find code for action " + action);
        };
    }

    public CodeLookup individualByAction(@Nullable String entityId, CodeLookupActionEnum action) {
        return individualByAction(entityId, action.getValue());
    }

    public CodeLookup individualByAction(@Nullable String entityId, String action) {
        return switch (CodeLookupActionEnum.fromValue(action)) {
            case LOOKUP -> individualLookup(entityId);
            case REGISTER -> individualRegister();
            default -> throw new ResponseStatusException(NOT_FOUND, "Cannot find code for action " + action);
        };
    }

    public CodeLookup tagByAction(@Nullable String entityId, CodeLookupActionEnum action) {
        return tagByAction(entityId, action.getValue());
    }

    public CodeLookup tagByAction(@Nullable String entityId, String action) {
        return switch (CodeLookupActionEnum.fromValue(action)) {
            case LOOKUP -> tagLookup(entityId);
            case CREATE -> tagCreate();
            default -> throw new ResponseStatusException(NOT_FOUND, "Cannot find code for action " + action);
        };
    }

    /*
        By Entity
     */
    public CodeLookup createByEntity(@NonNull String entityType, @Nullable String entityId, CodeLookupActionEnum action) {
        return createByEntity(entityType, entityId, action.getValue());
    }

    public CodeLookup createByEntity(@NonNull String entityType, @Nullable String entityId, String action) {
        return switch (entityType) {
            case "license" -> licenseByAction(entityId, action);
            case "individual" -> individualByAction(entityId, action);
            case "tag" -> tagByAction(entityId, action);
            default -> throw new ResponseStatusException(NOT_FOUND, "Cannot find code for entity type " + entityType);
        };
    }

    public CodeLookup createByPublic(CodeLookupActionEnum action) {
        return switch (action) {
            case LANDING_PAGE -> landingPageCreate();
            case LOGIN_PAGE -> loginPageCreate();
            case SIGN_UP_PAGE -> signUpPageCreate();
            case DEMO_PAGE -> demoPageCreate();
            default -> throw new ResponseStatusException(NOT_FOUND, "Cannot find code for action " + action);
        };
    }
}

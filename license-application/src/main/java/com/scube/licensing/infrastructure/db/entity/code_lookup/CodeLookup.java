package com.scube.licensing.infrastructure.db.entity.code_lookup;

import com.scube.auth.library.enabled_true.AuthUtils;
import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.repository.code_lookup.CodeLookupRepository;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

@Entity
@Table(
        name = CodeLookup.TABLE_NAME,
        indexes = {
                @Index(name = "idx_code_lookup_code", columnList = CodeLookup.C_CODE),
                @Index(name = "idx_code_lookup_entity_type_and_entity_id", columnList = CodeLookup.C_ENTITY_TYPE + "," + CodeLookup.C_ENTITY_ID),
                @Index(name = "idx_code_lookup_entity_type_and_entity_id_and_action", columnList = CodeLookup.C_ENTITY_TYPE + "," + CodeLookup.C_ENTITY_ID + "," + CodeLookup.C_ACTION)
        }
)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class CodeLookup extends BaseEntity {
    public static final String TABLE_NAME = "code_lookup";
    public static final String C_CODE = "code";
    public static final String C_ENTITY_ID = "entity_id";
    public static final String C_ENTITY_TYPE = "entity_type";
    public static final String C_ACTION = "action";

    @Size(max = 30)
    @Column(name = C_CODE, unique = true, nullable = false)
    private String code;

    @Size(max = 255)
    @Column(name = C_ENTITY_TYPE)
    private String entityType;

    @Size(max = 255)
    @Column(name = C_ENTITY_ID)
    private String entityId;

    @Size(max = 255)
    private String realm;

    @Enumerated(EnumType.STRING)
    @Column(name = C_ACTION)
    private CodeLookupActionEnum action;

    public CodeLookup() {
        // TODO: Remove this after multi-tenancy is implemented
        var authRealm = AuthUtils.getUserInfo().getRealm();
        this.realm = ObjectUtils.isEmpty(authRealm) ? "schenectady" : authRealm;
    }

    public CodeLookup(@NonNull String entityType) {
        this();
        this.entityType = entityType;
    }

    public CodeLookup(@NonNull String entityType, @Nullable String entityId) {
        this(entityType);
        this.entityId = entityId;
    }

    public CodeLookup(@NonNull String entityType, @Nullable String entityId, @NonNull CodeLookupActionEnum action) {
        this(entityType, entityId);
        this.action = action;
    }

    public CodeLookup(@NonNull String entityType, @Nullable String entityId, @NonNull CodeLookupActionEnum action, String code, String realm) {
        this(entityType, entityId, action);
        this.code = code;
        this.realm = realm;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public static CodeLookupBuilder builderFor(CodeLookupRepository repository, String tenant) {
        return new CodeLookupBuilder(repository, tenant);
    }
}
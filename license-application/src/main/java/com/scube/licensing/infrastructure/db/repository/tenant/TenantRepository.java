package com.scube.licensing.infrastructure.db.repository.tenant;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.tenant.Tenant;
import jakarta.validation.constraints.Size;

import java.util.Optional;

public interface TenantRepository extends AuditableEntityRepository<Tenant, Long> {
    Optional<Tenant> findByTemplateKey(@Size(max = 255) String templateKey);
}

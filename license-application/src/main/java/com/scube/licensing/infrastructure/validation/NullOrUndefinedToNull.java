package com.scube.licensing.infrastructure.validation;

import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Map;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {NullOrUndefinedToNull.NullOrUndefinedToNullValidator.class, NullOrUndefinedToNull.NullOrUndefinedToNullMapObjectValidator.class})
public @interface NullOrUndefinedToNull {
    String message() default "Null or Undefined values are not allowed";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Component
    class NullOrUndefinedToNullValidator implements ConstraintValidator<NullOrUndefinedToNull, Map<String, String>> {
        @Override
        public void initialize(NullOrUndefinedToNull constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(Map<String, String> map, ConstraintValidatorContext constraintValidatorContext) {
            for (var entry : map.entrySet()) {
                String key = entry.getKey();
                var value = entry.getValue();
                if (ObjectUtils.isEmpty(value)) continue;
                if (value.equalsIgnoreCase("null") || value.equalsIgnoreCase("undefined")) {
                    map.put(key, null);
                }
            }
            return true;
        }
    }

    @Component
    class NullOrUndefinedToNullMapObjectValidator implements ConstraintValidator<NullOrUndefinedToNull, Map<String, Object>> {
        @Override
        public void initialize(NullOrUndefinedToNull constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(Map<String, Object> map, ConstraintValidatorContext constraintValidatorContext) {
            for (var entry : map.entrySet()) {
                String key = entry.getKey();
                var value = entry.getValue();
                if (ObjectUtils.isEmpty(value)) continue;
                if (value.toString().equalsIgnoreCase("null") || value.toString().equalsIgnoreCase("undefined")) {
                    map.put(key, null);
                }
            }
            return true;
        }
    }
}
package com.scube.licensing.infrastructure.axon;

import com.scube.licensing.infrastructure.axon.request.IRequestAxon;
import com.scube.licensing.infrastructure.axon.request.IRequestVoidAxon;
import org.axonframework.commandhandling.gateway.CommandGateway;
import org.axonframework.messaging.responsetypes.ResponseTypes;
import org.axonframework.queryhandling.QueryGateway;
import org.springframework.stereotype.Component;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.List;
import java.util.Optional;

@Component
public class AxonGateway {
    private final QueryGateway queryGateway;
    private final CommandGateway commandGateway;

    public AxonGateway(QueryGateway queryGateway, CommandGateway commandGateway) {
        this.queryGateway = queryGateway;
        this.commandGateway = commandGateway;
    }

    public QueryGateway queryGateway() {
        return queryGateway;
    }

    public CommandGateway commandGateway() {
        return commandGateway;
    }

    public <R> R sendAndWait(IRequestAxon<R> request) {
        return commandGateway.sendAndWait(request);
    }

    public void send(IRequestAxon<?> request) {
        commandGateway.send(request);
    }

    //void
    public void sendAndWait(IRequestVoidAxon request) {
        commandGateway.sendAndWait(request);
    }

    public void send(IRequestVoidAxon request) {
        commandGateway.send(request);
    }

    @SuppressWarnings("unchecked")
    public <R> R query(IRequestAxon<R> request) {
        Type returnType = ((ParameterizedType) request.getClass().getGenericInterfaces()[0]).getActualTypeArguments()[0];
        if (returnType instanceof Class<?> clazz) {
            var result = queryGateway.query(request, clazz).join();
            if (result == null) {
                return null;
            } else if (clazz.isInstance(result)) {
                return (R) result;
            } else {
                throw new ClassCastException("Result was type " + result.getClass() + " but expected " + returnType + ".");
            }
        } else if (returnType instanceof ParameterizedType parameterizedTypeReturnType) {
            //list and optional
            var rawType = parameterizedTypeReturnType.getRawType();
            var actualTypeArgument = parameterizedTypeReturnType.getActualTypeArguments()[0];
            if (rawType instanceof Class<?> clazz && actualTypeArgument instanceof Class<?> actualTypeArgumentClass) {
                if (List.class.isAssignableFrom(clazz)) {
                    var resultList = queryGateway.query(request, ResponseTypes.multipleInstancesOf(actualTypeArgumentClass)).join();
                    return (R) resultList;
                } else if (Optional.class.isAssignableFrom(clazz)) {
                    var resultOptional = queryGateway.query(request, ResponseTypes.optionalInstanceOf(actualTypeArgumentClass)).join();
                    return (R) resultOptional;
                }
            } else {
                throw new ClassCastException("Result was type " + rawType.getClass() + " but expected " + returnType + ".");
            }
        }

        throw new ClassCastException("Return type not supported " + returnType + ".");
    }

}
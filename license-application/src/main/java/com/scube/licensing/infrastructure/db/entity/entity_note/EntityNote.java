package com.scube.licensing.infrastructure.db.entity.entity_note;

import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = EntityNote.TABLE_NAME)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class EntityNote extends Associable {
    public static final String TABLE_NAME = "entity_note";
    public static final String ENTITY_TYPE = "entityNote";

    @Column(columnDefinition = "TEXT")
    private String note;

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.ENTITY_NOTE;


    @OneToMany(mappedBy = "parentEntityNote", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childEntityNote", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}

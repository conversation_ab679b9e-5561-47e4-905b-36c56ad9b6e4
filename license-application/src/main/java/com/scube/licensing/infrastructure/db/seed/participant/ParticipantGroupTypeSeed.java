package com.scube.licensing.infrastructure.db.seed.participant;

import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantGroup;
import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantType;
import com.scube.licensing.infrastructure.db.entity.participant.type.ParticipantTypeGroup;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantGroupRepository;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantTypeGroupRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
@Slf4j
public class ParticipantGroupTypeSeed {

    public static final String INDIVIDUAL_PARTICIPANT_GROUP = "Individual";
    public static final String ORGANIZATION_PARTICIPANT_GROUP = "Organization";
    public static final String DOG_PARTICIPANT_GROUP = "Dog";
    public static final String LICENSE_HOLDER_PARTICIPANT_TYPE = "License Holder";
    public static final String DOG_SITTER_PARTICIPANT_TYPE = "Dog Sitter";
    public static final String DOG_WALKER_PARTICIPANT_TYPE = "Dog Walker";
    public static final String NORMAL_DOG_PARTICIPANT_TYPE = "Normal Dog";
    public static final String BAD_DOG_PARTICIPANT_TYPE = "Bad Dog";
    public static final String PURE_BRED_DOG_PARTICIPANT_TYPE = "Pure Bred Dog";
    @Autowired
    private ParticipantTypeGroupRepository participantTypeGroupRepository;

    @Autowired
    private ParticipantGroupRepository participantGroupRepository;

    @Transactional
    public void seed() {
        log.info("Start ParticipantGroupTypeSeed.seed()...");

        if (participantTypeGroupRepository.count() > 0) {
            log.info("ParticipantGroupTypeSeed.seed() already seeded...");
            return;
        }

        var licenseHolderParticipantType = new ParticipantType(LICENSE_HOLDER_PARTICIPANT_TYPE, "license holder participant type");
        var dogSittingParticipantType = new ParticipantType(DOG_SITTER_PARTICIPANT_TYPE, "Dog sitter participant type");
        var dogWalkerParticipantType = new ParticipantType(DOG_WALKER_PARTICIPANT_TYPE, "Dog walker participant type");

        var personParticipantGroup = participantGroupRepository.findByNameIgnoreCase(INDIVIDUAL_PARTICIPANT_GROUP);
        if (ObjectUtils.isEmpty(personParticipantGroup)) {
            personParticipantGroup = participantGroupRepository.save(new ParticipantGroup(INDIVIDUAL_PARTICIPANT_GROUP, "Person participant group"));
        }

        participantTypeGroupRepository.save(new ParticipantTypeGroup(personParticipantGroup, licenseHolderParticipantType));
        participantTypeGroupRepository.save(new ParticipantTypeGroup(personParticipantGroup, dogSittingParticipantType));
        participantTypeGroupRepository.save(new ParticipantTypeGroup(personParticipantGroup, dogWalkerParticipantType));

        var organizationParticipantGroup = participantGroupRepository.findByNameIgnoreCase(ORGANIZATION_PARTICIPANT_GROUP);
        if (ObjectUtils.isEmpty(organizationParticipantGroup)) {
            organizationParticipantGroup = participantGroupRepository.save(new ParticipantGroup(ORGANIZATION_PARTICIPANT_GROUP, "Organization participant group"));
        }

        participantTypeGroupRepository.save(new ParticipantTypeGroup(organizationParticipantGroup, licenseHolderParticipantType));
        participantTypeGroupRepository.save(new ParticipantTypeGroup(organizationParticipantGroup, dogSittingParticipantType));
        participantTypeGroupRepository.save(new ParticipantTypeGroup(organizationParticipantGroup, dogWalkerParticipantType));

        var dogParticipantGroup = participantGroupRepository.findByNameIgnoreCase(DOG_PARTICIPANT_GROUP);
        if (ObjectUtils.isEmpty(dogParticipantGroup)) {
            dogParticipantGroup = participantGroupRepository.save(new ParticipantGroup(DOG_PARTICIPANT_GROUP, "Dog participant group"));
        }

        participantTypeGroupRepository.save(new ParticipantTypeGroup(dogParticipantGroup, new ParticipantType(NORMAL_DOG_PARTICIPANT_TYPE, "Dog participant type")));
        participantTypeGroupRepository.save(new ParticipantTypeGroup(dogParticipantGroup, new ParticipantType(BAD_DOG_PARTICIPANT_TYPE, "Dog participant type")));
        participantTypeGroupRepository.save(new ParticipantTypeGroup(dogParticipantGroup, new ParticipantType(PURE_BRED_DOG_PARTICIPANT_TYPE, "Dog participant type")));

        log.info("End ParticipantGroupTypeSeed.seed()...");
    }
}
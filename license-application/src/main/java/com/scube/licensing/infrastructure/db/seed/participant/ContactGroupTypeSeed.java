package com.scube.licensing.infrastructure.db.seed.participant;

import com.scube.licensing.infrastructure.db.entity.participant.contact.ContactGroup;
import com.scube.licensing.infrastructure.db.entity.participant.contact.ContactType;
import com.scube.licensing.infrastructure.db.entity.participant.contact.ContactTypeGroup;
import com.scube.licensing.infrastructure.db.repository.participant.ContactGroupRepository;
import com.scube.licensing.infrastructure.db.repository.participant.ContactTypeGroupRepository;
import com.scube.licensing.infrastructure.db.repository.participant.ContactTypeRepository;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@AllArgsConstructor
public class ContactGroupTypeSeed {
    private final ContactTypeGroupRepository contactTypeGroupRepository;
    private final ContactTypeRepository contactTypeRepository;
    private final ContactGroupRepository contactGroupRepository;

    public static final String GROUP_EMAIL = "Email";
    public static final String GROUP_PHONE = "Phone";
    public static final String TYPE_PRIMARY = "Primary";
    public static final String TYPE_WORK = "Work";


    @Transactional
    public void seed() {
        log.info("Start ContactSeed.seed()...");

        var primaryContactType = contactTypeRepository.findByNameIgnoreCase(TYPE_PRIMARY)
                .orElse(new ContactType(TYPE_PRIMARY, "Home email contact type"));
        var homeContactType = contactTypeRepository.findByNameIgnoreCase("Home")
                .orElse(new ContactType("Home", "Home email contact type"));
        var workContactType = contactTypeRepository.findByNameIgnoreCase(TYPE_WORK)
                .orElse(new ContactType(TYPE_WORK, "Work email contact type"));
        var otherContactType = contactTypeRepository.findByNameIgnoreCase("Other")
                .orElse(new ContactType("Other", "Other email contact type"));
        var faxContactType = contactTypeRepository.findByNameIgnoreCase("Fax")
                .orElse(new ContactType("Fax", "Fax email contact type"));
        var cellContactType = contactTypeRepository.findByNameIgnoreCase("Cell")
                .orElse(new ContactType("Cell", "Cell email contact type"));

        var emailContactGroup = contactGroupRepository.findByNameIgnoreCase(GROUP_EMAIL)
                .orElse(new ContactGroup(GROUP_EMAIL, "Email contact group"));

        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(primaryContactType.getName(), emailContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(emailContactGroup, primaryContactType));
        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(workContactType.getName(), emailContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(emailContactGroup, workContactType));

        var phoneContactGroup = contactGroupRepository.findByNameIgnoreCase(GROUP_PHONE)
                .orElse(new ContactGroup(GROUP_PHONE, "Phone contact group"));

        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(primaryContactType.getName(), phoneContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(phoneContactGroup, primaryContactType));

        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(workContactType.getName(), phoneContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(phoneContactGroup, workContactType));

        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(homeContactType.getName(), phoneContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(phoneContactGroup, homeContactType));

        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(otherContactType.getName(), phoneContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(phoneContactGroup, otherContactType));

        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(faxContactType.getName(), phoneContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(phoneContactGroup, faxContactType));

        if (contactTypeGroupRepository.findByContactTypeNameAndContactGroupName(cellContactType.getName(), phoneContactGroup.getName()).isEmpty())
            contactTypeGroupRepository.save(new ContactTypeGroup(phoneContactGroup, cellContactType));

        log.info("End ContactSeed.seed()...");
    }
}
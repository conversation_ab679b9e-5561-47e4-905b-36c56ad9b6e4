package com.scube.licensing.infrastructure.db.repository.document;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.document.DocumentType;
import jakarta.validation.constraints.Size;

import java.util.Optional;

public interface DocumentTypeRepository extends AuditableEntityRepository<DocumentType, Long> {
    Optional<DocumentType> findByKeyIgnoreCase(@Size(max = 255) String key);
}

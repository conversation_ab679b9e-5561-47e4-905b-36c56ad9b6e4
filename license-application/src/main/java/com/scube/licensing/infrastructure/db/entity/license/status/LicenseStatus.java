package com.scube.licensing.infrastructure.db.entity.license.status;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;

@Entity
@Table(name = LicenseStatus.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class LicenseStatus extends BaseEntity {
    public static final String TABLE_NAME = "license_status";
    public static final String LICENSE_STATUS_ID = "license_status_id";
    public static final String C_NAME = "name";

    @Size(max = 255)
    @Column(name = C_NAME, unique = true)
    private String name;

    @Enumerated(EnumType.STRING)
    @Column(name = "code", nullable = false)
    private LicenseStatusCodeEnum code;

    public boolean isAllowableRenewalStatus() {
        return isActive() || isExpired();
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public LicenseStatus(String name, LicenseStatusCodeEnum code) {
        this.name = name;
        this.code = code;
    }

    public boolean isDraft() {
        return code == LicenseStatusCodeEnum.DRAFT;
    }

    public boolean isPending() {
        return code == LicenseStatusCodeEnum.PENDING;
    }

    public boolean isSubmitted() {
        return code == LicenseStatusCodeEnum.SUBMITTED;
    }

    public boolean isActive() {
        return code == LicenseStatusCodeEnum.ACTIVE;
    }

    public boolean isExpired() {
        return code == LicenseStatusCodeEnum.EXPIRED;
    }

    public boolean isCanceled() {
        return code == LicenseStatusCodeEnum.CANCELED;
    }

    public boolean isPendingPayment() {
        return code == LicenseStatusCodeEnum.PENDING_PAYMENT;
    }

    public boolean isPendingApproval() {
        return code == LicenseStatusCodeEnum.PENDING_APPROVAL;
    }
}
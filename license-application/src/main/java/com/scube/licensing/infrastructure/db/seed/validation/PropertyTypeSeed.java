package com.scube.licensing.infrastructure.db.seed.validation;

import com.fasterxml.jackson.core.type.TypeReference;
import com.scube.audit.auditable.properties.type.PropertyType;
import com.scube.audit.auditable.properties.type.PropertyTypeRepository;
import com.scube.licensing.utils.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class PropertyTypeSeed {
    private final PropertyTypeRepository propertyTypeRepository;

    public void seed() {
        log.info("Start PropertyTypeSeed.seed()...");

        var files = JsonUtils.readJsonFiles("seed/validation", new TypeReference<List<Map<String, Object>>>() {
        }).stream().flatMap(Collection::stream).toList();
        for (var propertyTypeMap : files) {
            var propertyType = new PropertyType(propertyTypeMap);
            var exists = propertyTypeRepository.findByTableNameAndName(propertyType.getTableName(), propertyType.getName());
            if (exists.isEmpty()) {
                propertyTypeRepository.save(propertyType);
            } else {
                exists.get().updatePropertyType(propertyTypeMap);
                propertyTypeRepository.save(exists.get());
            }
        }

        log.info("End PropertyTypeSeed.seed()...");
    }
}

package com.scube.licensing.infrastructure.db.entity.document;

import com.scube.licensing.features.document.dto.DocumentTypeDto;
import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.springframework.util.ObjectUtils;

@Entity
@Table(name = DocumentType.TABLE_NAME)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class DocumentType extends BaseEntity {
    public static final String TABLE_NAME = "document_type";
    public static final String DOCUMENT_TYPE_ID = "document_type_id";

    @Size(max = 255)
    @Column(unique = true)
    private String key;

    @Size(max = 255)
    private String name;

    @Size(max = 255)
    private String groupName;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public void update(DocumentTypeDto documentTypeDto) {
        this.key = documentTypeDto.key();
        if (ObjectUtils.isEmpty(documentTypeDto.name()))
            this.name = documentTypeDto.name();
        if (ObjectUtils.isEmpty(documentTypeDto.groupName()))
            this.groupName = documentTypeDto.groupName();
    }
}
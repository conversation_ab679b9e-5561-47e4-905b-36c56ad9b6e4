package com.scube.licensing.infrastructure.db.entity.license;

import com.scube.licensing.features.events.Event;
import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Entity
@Table(
        name = LicenseActivity.TABLE_NAME,
        indexes = {
                @Index(name = "idx_license_activity_license_id", columnList = License.LICENSE_ID),
                @Index(name = "idx_license_activity_activity_type", columnList = LicenseActivity.ACTIVITY_TYPE)
        }
)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class LicenseActivity extends BaseEntity {
    public static final String TABLE_NAME = "license_activity";
    public static final String LICENSE_ACTIVITY_ID = "license_activity_id";
    public static final String VALID_TO_DATE = "valid_to_date";
    public static final String VALID_FROM_DATE = "valid_from_date";
    public static final String ACTIVITY_TYPE = "activity_type";
    public static final String ISSUED_DATE = "issued_date";

    @ManyToOne
    @JoinColumn(name = License.LICENSE_ID, nullable = false)
    private License license;

    @Column(name = VALID_FROM_DATE)
    private Instant validFromDate;

    @Column(name = VALID_TO_DATE)
    private Instant validToDate;

    @Column(name = ISSUED_DATE)
    private Instant issuedDate;

    @Enumerated(EnumType.STRING)
    @Column(name = ACTIVITY_TYPE)
    private ActivityType activityType;

    @OneToMany(mappedBy = "licenseActivity", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private Set<LicenseActivityFee> licenseActivityFees = new HashSet<>();

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public boolean isRenewal() {
        return activityType == ActivityType.RENEWAL;
    }

    public boolean isNew() {
        return activityType == ActivityType.NEW;
    }

    public boolean isPaid() {
        return licenseActivityFees.stream()
                .allMatch(f -> f.getPaymentStatus() == LicenseActivityFee.PaymentStatus.PAID);
    }

    public boolean hasAnyPaid() {
        return licenseActivityFees.stream()
                .anyMatch(f -> f.getPaymentStatus() == LicenseActivityFee.PaymentStatus.PAID);
    }

    public boolean isUnpaid() {
        return licenseActivityFees.stream()
                .allMatch(f -> f.getPaymentStatus() == LicenseActivityFee.PaymentStatus.UNPAID);
    }

    public boolean hasAnyUnpaid() {
        return licenseActivityFees.stream()
                .anyMatch(f -> f.getPaymentStatus() == LicenseActivityFee.PaymentStatus.UNPAID);
    }

    public void addLicenseFee(String feeCode, BigDecimal value, LicenseActivityFee.PaymentStatus paymentStatus, Instant paidDate) {
        LicenseActivityFee licenseActivityFee = new LicenseActivityFee();
        licenseActivityFee.setFeeCode(feeCode);
        licenseActivityFee.setPaymentStatus(paymentStatus);
        licenseActivityFee.setPaidDate(paidDate);
        licenseActivityFee.setAmount(value);
        licenseActivityFees.add(licenseActivityFee);
        licenseActivityFee.setLicenseActivity(this);
    }

    public void addLicenseFee(String feeCode, BigDecimal value, String reason) {
        var licenseActivityFee = addLicenseFee(feeCode, value);
        licenseActivityFee.setReason(reason);
        licenseActivityFee.logChangeEvent("Fee added to license", value, reason);
    }

    public LicenseActivityFee addLicenseFee(String feeCode, BigDecimal value) {
        LicenseActivityFee licenseActivityFee = new LicenseActivityFee();
        licenseActivityFee.setFeeCode(feeCode);
        licenseActivityFee.setAmount(value);
        licenseActivityFee.setPaymentStatus(LicenseActivityFee.PaymentStatus.UNPAID);
        licenseActivityFees.add(licenseActivityFee);
        licenseActivityFee.setLicenseActivity(this);
        return licenseActivityFee;
    }

    public void addLicenseFee(String feeCode) {
        addLicenseFee(feeCode, BigDecimal.ZERO);
    }

    public void setFeeAmountByFeeCode(String feeCode, BigDecimal amount) {
        findLicenseFeeByFeeCode(feeCode).forEach(licenseActivityFee -> {
            licenseActivityFee.setAmount(amount);
        });
    }

    public List<LicenseActivityFee> findLicenseFeeByFeeCode(String feeCode) {
        return getLicenseActivityFees().stream()
                .filter(f -> f.getFeeCode().equals(feeCode))
                .toList();
    }

    public String[] getLicenseFeeCodes() {
        return licenseActivityFees.stream()
                .map(LicenseActivityFee::getFeeCode)
                .toArray(String[]::new);
    }

    public void setFeePaidByFeeCode(String fee) {
        findLicenseFeeByFeeCode(fee).forEach(licenseActivityFee -> {
            licenseActivityFee.setPaymentStatus(LicenseActivityFee.PaymentStatus.PAID);
        });
    }

    public List<LicenseActivityFee> getUnpaidLicenseFees() {
        return licenseActivityFees.stream()
                .filter(f -> f.getPaymentStatus() == LicenseActivityFee.PaymentStatus.UNPAID)
                .sorted(Comparator.comparing(BaseEntity::getCreatedDate))
                .toList();
    }

    public BigDecimal getTotalAmount() {
        if (ObjectUtils.isEmpty(licenseActivityFees)) return BigDecimal.ZERO;
        return licenseActivityFees.stream()
                .map(LicenseActivityFee::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    //subTotal
    public BigDecimal getSubTotal() {
        if (ObjectUtils.isEmpty(licenseActivityFees)) return BigDecimal.ZERO;
        return licenseActivityFees.stream()
                .map(LicenseActivityFee::getAmount)
                .filter(f -> f.compareTo(BigDecimal.ZERO) > 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalPaidAmount() {
        if (ObjectUtils.isEmpty(licenseActivityFees)) return BigDecimal.ZERO;
        return licenseActivityFees.stream()
                .filter(f -> f.getPaymentStatus() == LicenseActivityFee.PaymentStatus.PAID)
                .map(LicenseActivityFee::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalDiscountAmount() {
        if (ObjectUtils.isEmpty(licenseActivityFees)) return BigDecimal.ZERO;
        return licenseActivityFees.stream()
                .map(LicenseActivityFee::getAmount)
                .filter(f -> f.compareTo(BigDecimal.ZERO) < 0)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalOutstandingAmount() {
        if (ObjectUtils.isEmpty(licenseActivityFees)) return BigDecimal.ZERO;
        return this.getTotalAmount().subtract(this.getTotalPaidAmount());
    }

    public boolean isExempted() {
        return getTotalOutstandingAmount().compareTo(BigDecimal.ZERO) == 0;
    }

    public void markFeesAsPaid(String reason) {
        licenseActivityFees.forEach(f -> {
            f.setPaymentStatus(LicenseActivityFee.PaymentStatus.PAID);
            //f.set(reason);
        });
    }

    public void clearFees() {
        licenseActivityFees.clear();
    }

    public void addPureBredDogFee(boolean isSpayedOrNeutered) {
        // TODO: come up with a proper way to do this.
        if (isSpayedOrNeutered)
            addLicenseFee("DLP-S-ALT", BigDecimal.valueOf(1.00));
        else
            addLicenseFee("DLP-S-UNALT", BigDecimal.valueOf(3.00));
    }

    public void removeLicenseFee(UUID feeId, @Nullable String reason) {
        if (ObjectUtils.isEmpty(licenseActivityFees)) return;
        licenseActivityFees.removeIf(f -> f.getUuid().equals(feeId));
        license.addEvent(new Event(
                "licenseChanged",
                "Fee removed from license",
                Map.of(
                        "licenseActivityFeeId", feeId.toString(),
                        "licenseActivityId", getUuid().toString(),
                        "reason", reason == null ? "" : reason
                )
        ));
    }

    public void removeFeeByFeeCode(String key) {
        licenseActivityFees.removeIf(f -> f.getFeeCode().equals(key));
    }

    @AllArgsConstructor
    @Getter
    public enum ActivityType {
        NEW("new"),
        RENEWAL("renewal"),
        ADD_PUREBRED_DOG("addPurebredDog");

        private final String key;
    }
}
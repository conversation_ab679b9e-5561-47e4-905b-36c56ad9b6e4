package com.scube.licensing.infrastructure.db.entity.address;

import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;

import java.util.HashSet;
import java.util.Set;

import static org.springframework.util.ObjectUtils.nullSafeEquals;

@Entity
@Table(
        name = Address.TABLE_NAME,
        indexes = {
                @Index(name = "idx_address_address_uuid", columnList = Address.ADDRESS_UUID),
                @Index(name = "idx_address_city", columnList = Address.A_CITY),
                @Index(name = "idx_address_state", columnList = Address.C_STATE),
                @Index(name = "idx_address_street_address", columnList = Address.STREET_ADDRESS),
                @Index(name = "idx_address_street_address_2", columnList = Address.STREET_ADDRESS_2)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class Address extends Associable {
    public static final String TABLE_NAME = "address";
    public static final String ENTITY_TYPE = "address";
    public static final String ADDRESS_ID = "address_id";
    public static final String ADDRESS_UUID = "address_uuid";
    public static final String STREET_ADDRESS = "street_address";
    public static final String STREET_ADDRESS_2 = "street_address_2";
    public static final String A_CITY = "city";
    public static final String C_STATE = "state";
    public static final String A_ZIP = "zip";
    public static final String A_LONGITUDE = "longitude";
    public static final String A_LATITUDE = "latitude";
    public static final String A_FULL_ADDRESS = "full_address";
    public static final String A_HOUSE_NUMBER = "house_number";
    public static final String A_ROAD = "road";
    public static final String A_TOWN = "town";

    @Size(max = 255)
    @Column(name = A_HOUSE_NUMBER, length = 255)
    private String houseNumber;

    @Size(max = 500)
    @Column(name = A_ROAD, length = 500)
    private String streetName;

    @Size(max = 500)
    @Column(name = STREET_ADDRESS, length = 500)
    private String streetAddress;

    @Size(max = 500)
    @Column(name = STREET_ADDRESS_2, length = 500)
    private String streetAddress2;

    @Size(max = 100)
    @Column(name = A_TOWN, length = 100)
    private String town;

    @Size(max = 255)
    @Column(name = A_CITY)
    private String city;

    @Size(max = 255)
    @Column(name = C_STATE)
    private String state;

    @Size(max = 10)
    @Column(name = A_ZIP)
    private String zip;

    @Size(max = 500)
    @Column(name = A_FULL_ADDRESS, length = 500)
    private String fullAddress;


    @Column(name = A_LATITUDE)
    private Double latitude;

    @Column(name = A_LONGITUDE)
    private Double longitude;

    public Address(@Size(max = 255) String streetAddress,
                   @Size(max = 255) String streetAddress2,
                   @Size(max = 255) String city,
                   @Size(max = 255) String state,
                   @Size(max = 255) String zip) {
        this.streetAddress = streetAddress;
        this.streetAddress2 = streetAddress2;
        this.city = city;
        this.state = state;
        this.zip = zip;
    }

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.ADDRESS;


    @OneToMany(mappedBy = "parentAddress", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childAddress", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public boolean matches(Address address) {
        return nullSafeEquals(this.streetAddress, address.streetAddress) &&
               nullSafeEquals(this.streetAddress2, address.streetAddress2) &&
               nullSafeEquals(this.city, address.city) &&
               nullSafeEquals(this.state, address.state) &&
               nullSafeEquals(this.zip, address.zip);
    }
}
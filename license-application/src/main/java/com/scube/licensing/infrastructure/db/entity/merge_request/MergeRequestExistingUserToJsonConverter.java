package com.scube.licensing.infrastructure.db.entity.merge_request;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Converter
@AllArgsConstructor
public class MergeRequestExistingUserToJsonConverter implements AttributeConverter<List<MergeRequestExistingUser>, String> {
    private final ObjectMapper objectMapper;

    @Override
    public String convertToDatabaseColumn(List<MergeRequestExistingUser> mergeRequestExistingUsers) {
        if (mergeRequestExistingUsers == null)
            return null;

        try {
            return objectMapper.writeValueAsString(mergeRequestExistingUsers);
        } catch (Exception e) {
            log.error("EventToJsonConverter.convertToDatabaseColumn() error", e);
            return null;
        }
    }

    @Override
    public List<MergeRequestExistingUser> convertToEntityAttribute(String mergeRequestExistingUsersJson) {
        try {
            if (!ObjectUtils.isEmpty(mergeRequestExistingUsersJson)) {
                return objectMapper.readValue(mergeRequestExistingUsersJson, new TypeReference<>() {
                });
            }
        } catch (Exception e) {
            log.error("MergeRequestExistingUserToJsonConverter.convertToEntityAttribute() error", e);
        }
        return new ArrayList<>();
    }
}
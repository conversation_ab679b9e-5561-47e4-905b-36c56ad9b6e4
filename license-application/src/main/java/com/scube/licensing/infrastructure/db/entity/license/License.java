package com.scube.licensing.infrastructure.db.entity.license;

import com.scube.audit.auditable.entity.AuditableBase;
import com.scube.audit.auditable.properties.type.PropertyTypeEnum;
import com.scube.audit.auditable.properties.validation.PropertyTypeEncoder;
import com.scube.auth.library.enabled_true.AuthUtils;
import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.license.LicenseActivityService;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.entity.license.type.LicenseType;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.FieldNameConstants;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Entity
@Table(
        name = License.TABLE_NAME,
        indexes = {
                @Index(name = "idx_license_license_uuid", columnList = License.LICENSE_UUID),
                @Index(name = "idx_license_license_number", columnList = License.LICENSE_NUMBER),
                @Index(name = "idx_license_license_status_id", columnList = LicenseStatus.LICENSE_STATUS_ID),
                @Index(name = "idx_license_license_type_id", columnList = LicenseType.LICENSE_TYPE_ID),
                @Index(name = "idx_license_valid_from_date", columnList = License.VALID_FROM_DATE)
        }
)
@Getter
@Setter
@Accessors(chain = true)
@Audited
@FieldNameConstants
public class License extends Associable {
    public static final String TABLE_NAME = "license";
    public static final String ENTITY_TYPE = "license";
    public static final String LICENSE_ID = "license_id";
    public static final String LICENSE_NUMBER = "license_number";
    public static final String VALID_TO_DATE = "valid_to_date";
    public static final String VALID_FROM_DATE = "valid_from_date";
    public static final String ISSUED_DATE = "issued_date";
    public static final String APPLICATION_DATE = "application_date";
    public static final String LICENSE_UUID = "license_uuid";

    @Size(max = 20)
    @Column(name = LICENSE_NUMBER, length = 20)
    private String licenseNumber;

    @Column(name = ISSUED_DATE)
    private Instant issuedDate;

    @Column(name = APPLICATION_DATE)
    private Instant applicationDate;

    @Column(name = VALID_FROM_DATE)
    private Instant validFromDate;

    @Column(name = VALID_TO_DATE)
    private Instant validToDate;

    @ManyToOne
    @JoinColumn(name = LicenseType.LICENSE_TYPE_ID, nullable = false)
    private LicenseType licenseType;

    @ManyToOne
    @JoinColumn(name = LicenseStatus.LICENSE_STATUS_ID, nullable = false)
    private LicenseStatus licenseStatus;

    @Size(max = 255)
    private String modifier;

    @Column(nullable = false)
    private boolean approved;

    private Instant approvedDate;

    @Size(max = 255)
    private String approvedBy;

    @Column(columnDefinition = "TEXT")
    private String deniedComment;

    public boolean isPurebredDogLicense() {
        return this.getLicenseType().getCode().equalsIgnoreCase("purebredDogLicense");
    }

    public boolean isDogLicense() {
        return this.getLicenseType().getCode().equalsIgnoreCase("dogLicense");
    }

    public void updateLicenseActivitiesIssuedDate() {
        // update the issued date if it is not set
        licenseActivities.stream()
                .filter(x -> ObjectUtils.isEmpty(x.getIssuedDate()))
                .forEach(x -> x.setIssuedDate(issuedDate));
    }

    public void markAsApproved() {
        clearApproval();
        this.approved = true;
        this.approvedDate = Instant.now();
        this.approvedBy = AuthUtils.getLoggedInUserPreferredUsername();
        this.issuedDate = Instant.now();
        this.updateLicenseDatesWithLastActivityDates();
        this.addEvent(new Event("licenseChanged", "license approved"));
    }

    public void markAsPendingApproval() {
        clearApproval();
        this.addEvent(new Event("licenseChanged", "license pending approval"));
        applicationDate = Instant.now();
    }

    public void clearApproval() {
        this.approved = false;
        this.approvedDate = null;
        this.approvedBy = null;
        // clear the denied reason and comment
        this.modifier = null;
        this.deniedComment = null;
    }

    public void markAsRejected(@NonNull String reason, String comment) {
        clearApproval();
        var reasonMap = Map.of(
                "incomplete", "Incomplete Application",
                "requirements", "Failed to meet Requirements",
                "documentation", "Missing Documentation",
                "other", "Other Reason"
        );
        // encode the reason and comment
        reason = PropertyTypeEncoder.encode(reason);
        comment = PropertyTypeEncoder.encode(comment);
        if (reasonMap.containsKey(reason)) {
            this.modifier = reasonMap.get(reason);
        } else {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid reason for rejection");
        }
        this.deniedComment = comment;
        this.addEvent(new Event("licenseChanged", "License rejected. reason: " + reason + " comment: " + comment));
    }

    @OneToMany(mappedBy = "license", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    private Set<LicenseActivity> licenseActivities = new HashSet<>();

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.LICENSE;


    @OneToMany(mappedBy = "parentLicense", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childLicense", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();

    public List<Participant> getLicenseHolders() {
        return getParentAssociablesByType(AssociationType.PARTICIPANT).stream()
                .map(Participant.class::cast)
                .filter(Participant::isIndividual)
                .toList();
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public LicenseActivity addLicenseActivity(Instant validFrom, Instant validTo, LicenseActivity.ActivityType activityType) {
        var activity = new LicenseActivity()
                .setActivityType(activityType)
                .setValidFromDate(validFrom)
                .setValidToDate(validTo)
                .setLicense(this);
        this.licenseActivities.add(activity);
        return activity;
    }

    public LicenseActivity addNewLicenseActivity(LicenseActivityService licenseActivityService) {
        var existingNewLicenseActivity = getNewLicenseActivity();
        if (!ObjectUtils.isEmpty(getNewLicenseActivity()))
            return existingNewLicenseActivity;

        var validDates = licenseActivityService.determineLicenseActivityDates(this.getUuid());
        var newValidFromDate = validDates.getValidFrom();
        var newValidToDate = validDates.getValidTo();

        return addLicenseActivity(newValidFromDate, newValidToDate, LicenseActivity.ActivityType.NEW);
    }

    public LicenseActivity addRenewalLicenseActivity(LicenseActivityService licenseActivityService) {
        var validDates = licenseActivityService.determineLicenseActivityDates(this.getUuid());
        var newValidFromDate = validDates.getValidFrom();
        var newValidToDate = validDates.getValidTo();
        return addLicenseActivity(newValidFromDate, newValidToDate, LicenseActivity.ActivityType.RENEWAL);
    }

    //ignore from entity
    @Transient
    private Instant currentInstant;

    private Instant getCurrentTime() {
        if (ObjectUtils.isEmpty(currentInstant))
            currentInstant = Instant.now();

        return currentInstant;
    }

    public void setCurrentTime(Instant currentTime) {
        currentInstant = currentTime;
    }


    public LicenseActivity getNewLicenseActivityOrElseThrow() {
        var newLicenseActivity = getNewLicenseActivity();
        if (ObjectUtils.isEmpty(newLicenseActivity))
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "No new license activity found");
        return newLicenseActivity;
    }

    public LicenseActivity getNewLicenseActivity() {
        return licenseActivities.stream()
                .filter(x -> x.getActivityType() == LicenseActivity.ActivityType.NEW)
                .findFirst()
                .orElse(null);
    }

    public LicenseActivity getLatestLicenseActivity() {
        return licenseActivities.stream()
                .filter(x -> !ObjectUtils.isEmpty(x.getValidToDate()))
                .max(Comparator.comparing(LicenseActivity::getValidToDate))
                .orElse(null);
    }

    public boolean isLatestActivityIsRenewal() {
        var latestActivity = getLatestLicenseActivity();
        return !ObjectUtils.isEmpty(latestActivity) && latestActivity.isRenewal();
    }

    public boolean isLatestActivityIsNew() {
        var latestActivity = getLatestLicenseActivity();
        return !ObjectUtils.isEmpty(latestActivity) && latestActivity.isNew();
    }

    public void updateLicenseDatesWithLastActivityDates() {
        Instant newValidFromDate = null;
        var activities = getPaidActivitiesOrderValidFromDateDescending();
        for (LicenseActivity licenseActivity : activities) {
            // if the valid from date is before current date
            if (licenseActivity.getValidFromDate().isBefore(getCurrentTime())) {
                newValidFromDate = licenseActivity.getValidFromDate();
                break;
            }
        }
        if (ObjectUtils.isEmpty(newValidFromDate) && !activities.isEmpty()) {
            newValidFromDate = activities.getLast().getValidFromDate();
        }

        this.validFromDate = newValidFromDate;

        var validToRenewalActivity = licenseActivities.stream()
                .filter(x -> x.isPaid() && !ObjectUtils.isEmpty(x.getValidToDate()) && !ObjectUtils.isEmpty(x.getValidFromDate()))
                .max(Comparator.comparing(LicenseActivity::getValidToDate))
                .orElse(null);
        this.validToDate = Optional.ofNullable(validToRenewalActivity)
                .map(LicenseActivity::getValidToDate)
                .orElse(null);
    }

    private List<LicenseActivity> getPaidActivitiesOrderValidFromDateDescending() {
        return licenseActivities.stream()
                .filter(x -> x.isPaid() && !ObjectUtils.isEmpty(x.getValidToDate()) && !ObjectUtils.isEmpty(x.getValidFromDate()))
                .sorted(Comparator.comparing(LicenseActivity::getValidFromDate).reversed())
                .toList();
    }

    public boolean hasOutstandingApprovals() {
        return !isApproved() || hasOutstandingPayment();
    }

    public List<String> getOutstandingApprovals() {
        var result = new ArrayList<String>();
        if (!approved)
            result.add("pendingApproval");
        if (licenseStatus.isPendingPayment()) {
            result.add("pendingPayment");
            result.add("addToCart");
        }
        return result;
    }

    public boolean hasOutstandingPayment() {
        return getTotalOutstandingAmount().compareTo(BigDecimal.ZERO) > 0;
    }

    public BigDecimal getTotalFees() {
        return licenseActivities.stream()
                .map(LicenseActivity::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal getTotalOutstandingAmount() {
        return licenseActivities.stream()
                .map(LicenseActivity::getTotalOutstandingAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    public List<LicenseActivityFee> getUnpaidLicenseFees() {
        return licenseActivities.stream()
                .flatMap(x -> x.getLicenseActivityFees().stream()
                        .filter(LicenseActivityFee::isUnpaid))
                .sorted(Comparator.comparing(AuditableBase::getCreatedDate))
                .toList();
    }

    public boolean isActive() {
        if (ObjectUtils.isEmpty(licenseStatus)) return false;
        return licenseStatus.isActive();
    }

    public boolean isExpired() {
        if (ObjectUtils.isEmpty(validToDate)) return false;
        return validToDate.isBefore(getCurrentTime());
    }

    public boolean isDraft() {
        if (ObjectUtils.isEmpty(licenseStatus)) return false;
        return licenseStatus.isDraft();
    }

    public boolean isAboutToExpired() {
        if (ObjectUtils.isEmpty(validToDate)) return false;
        //will expire in 30 days
        return validToDate.isBefore(getCurrentTime().plusSeconds(30L * 24L * 60L * 60L));
    }

    public boolean isAllowableDeletionStatus() {
        if (licenseStatus.isDraft() || licenseStatus.isPending()) return true;
        return licenseStatus.isPendingPayment() && licenseActivities.stream().allMatch(x -> ObjectUtils.isEmpty(x.getIssuedDate()));
    }

    public boolean isAllowableRenewalCancelStatus() {
        //renewal can only be canceled if the license is pending payment and the latest activity is renewal
        return licenseStatus.isPendingPayment() && licenseActivities.stream().anyMatch(x -> !ObjectUtils.isEmpty(x.getIssuedDate()));
    }

    public boolean isAllowableRenewalStatus() {
        return licenseStatus.isAllowableRenewalStatus();
    }

    public void addPurebredLicenseActivity(@NonNull boolean isSpayedOrNeutered, @NonNull Long licenseDurationValue) {
        var pureBredActivity = addLicenseActivity(null, null, LicenseActivity.ActivityType.ADD_PUREBRED_DOG);
        for (var i = 0; i < licenseDurationValue; i++) {
            pureBredActivity.addPureBredDogFee(isSpayedOrNeutered);
        }
    }

    public boolean canRenew() {
        return hasMaxRenewals(1);
    }

    public boolean hasMaxRenewals(@Min(1) @Max(3) int duration) {
        var todayPlus30Days = getCurrentTime().plusSeconds(30L * 24L * 60L * 60L);
        var renewalCount = licenseActivities.stream()
                .filter(lr -> !ObjectUtils.isEmpty(lr.getValidToDate()))
                .filter(lr -> lr.getValidToDate().isAfter(todayPlus30Days))
                .filter(lr -> lr.isRenewal() || lr.isNew())
                .count();
        return renewalCount + duration > 3;
    }

    public boolean hasMaxDogs() {
        if (isPurebredDogLicense()) return false;
        // can only have 1 dog per license
        return !getDogs().isEmpty();
    }

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    public void setDuration(Integer duration) {
        setProperty(PropertyTypeEnum.INT, "licenseDuration", duration);
    }

    public Optional<Integer> getDuration() {
        return getPropertyAsInteger("licenseDuration");
    }

    public void putLicenseLabel(String matchingLabel) {
        setProperty(PropertyTypeEnum.STRING, "licenseLabel", matchingLabel);
    }

    public Optional<String> getLicenseLabel() {
        return getPropertyAsString("licenseLabel");
    }

    @PrePersist
    public void onPrePersistAddApplicationDate() {
        if (ObjectUtils.isEmpty(applicationDate)) {
            applicationDate = Instant.now();
        }
    }

    public void setStartYear(Integer startYear) {
        setProperty(PropertyTypeEnum.INT, "startYear", startYear);
    }

    public int getStartYear() {
        return getPropertyAsInteger("startYear").orElse(0);
    }

    public void setEndYear(Integer endYear) {
        setProperty(PropertyTypeEnum.INT, "endYear", endYear);
    }

    public int getEndYear() {
        return getPropertyAsInteger("endYear").orElse(0);
    }

    public void setStartEndDuration(Integer startYear, Integer endYear, Integer licenseDuration) {
        this.setDuration(Optional.ofNullable(licenseDuration)
                .orElse(1));
        if (!ObjectUtils.isEmpty(startYear)) {
            this.setStartYear(startYear);
        } else {
            // set the start year to one year before the end year
            Optional.ofNullable(endYear)
                    .map(ey -> ey - 1)
                    .ifPresent(this::setStartYear);
        }
        if (!ObjectUtils.isEmpty(endYear)) {
            this.setEndYear(endYear);
        }
    }
}
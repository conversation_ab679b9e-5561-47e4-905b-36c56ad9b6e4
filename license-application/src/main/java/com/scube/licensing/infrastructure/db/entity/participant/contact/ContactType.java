package com.scube.licensing.infrastructure.db.entity.participant.contact;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.springframework.util.ObjectUtils;

@Entity
@Table(
        name = ContactType.TABLE_NAME,
        indexes = {
                @Index(name = "idx_contact_type_t_name", columnList = ContactType.C_NAME)
        }
)
@Getter
@Setter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class ContactType extends BaseEntity {
    public static final String TABLE_NAME = "contact_type";
    public static final String CONTACT_TYPE_ID = "contact_type_id";
    public static final String C_NAME = "t_name";

    @Size(max = 50)
    @Column(unique = true, name = C_NAME)
    private String name;

    @Size(max = 250)
    private String description;

    public ContactType(String name, String description) {
        this.name = name;
        this.description = description;
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    public boolean isPrimary() {
        if (ObjectUtils.isEmpty(name)) return false;
        return name.equalsIgnoreCase("primary") || name.equalsIgnoreCase("home");
    }
}

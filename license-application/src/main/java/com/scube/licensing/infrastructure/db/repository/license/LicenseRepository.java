package com.scube.licensing.infrastructure.db.repository.license;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.features.license.reports.projections.ClerksSummaryReportCountsProjection;
import com.scube.licensing.features.license.reports.projections.DogLicenseMonthlyReportProjection;
import com.scube.licensing.features.license.reports.projections.LicenseProjection;
import com.scube.licensing.infrastructure.db.entity.license.License;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface LicenseRepository extends AuditableEntityRepository<License, Long> {

    @Query("SELECT l FROM License l WHERE DATE(l.validToDate) = DATE(:targetDate) and l.licenseStatus.name = 'Active'")
    List<License> findByValidToDateEqualToIgnoringTime(LocalDate targetDate);

    @Query("SELECT l FROM License l WHERE DATE(l.validToDate) = DATE(:targetDate) " +
            "and l.licenseStatus.name = 'Expired'")
    List<License> findExpiredByDate(LocalDate targetDate);


    @Query("SELECT l FROM License l WHERE DATE(l.validToDate) <= DATE(:targetDate) and l.licenseStatus.name = 'Active'")
    List<License> findByValidToDateLessThanEqualToIgnoringTime(LocalDate targetDate);

    @Query(nativeQuery = true, value = """
            SELECT l.* FROM license l
            INNER JOIN license_status ls
            ON ls.license_status_id = l.license_status_id
            WHERE ls.name = 'Expired'
            AND DATE(valid_to_date) BETWEEN :startDate and :endDate""")
    List<License> findDeliquentDogLicenses(LocalDate startDate, LocalDate endDate);

    @Query(value = "SELECT count(*) FROM view_dog_licenses WHERE DATE(valid_from_date) BETWEEN :startDate AND :endDate AND string_value ='yes'", nativeQuery = true)
    int findAlteredDogLicensesByValidFromDateBetween(LocalDate startDate, LocalDate endDate);

    @Query(value = "SELECT count(*) FROM view_dog_licenses WHERE DATE(valid_from_date) BETWEEN :startDate AND :endDate AND string_value ='no'", nativeQuery = true)
    int findUnalteredDogLicensesByValidFromDateBetween(LocalDate startDate, LocalDate endDate);

    @Query(nativeQuery = true, value = "SELECT * FROM view_license_for_reports r WHERE r.\"paidDate\" BETWEEN :startDate AND :endDate")
    List<LicenseProjection> getDogTransactionReport(LocalDate startDate, LocalDate endDate);

    @Query(value = "SELECT * FROM view_license_for_reports r WHERE r.\"entityId\" = :entityId limit 1", nativeQuery = true)
    Optional<LicenseProjection> findDogLicenseProjectionByLicenseEntityId(@Param("entityId") UUID entityId);

    @Query(value = "SELECT fee_code as feeCode, COUNT(*) AS count FROM license_fee lf" +
            " INNER JOIN license l on lf.license_id = l.license_id AND DATE(valid_from_date) BETWEEN :startDate AND :endDate " +
            " GROUP BY fee_code;", nativeQuery = true)
    List<DogLicenseMonthlyReportProjection> getDogLicenseMonthlyReport(LocalDate startDate, LocalDate endDate);

    @Query(value = "SELECT licenseEntityId FROM get_licenses(:participantEntityId, :participantGroupType)", nativeQuery = true)
    List<UUID> findDogLicenseIdByPartipantAndOrGroup(@Param("participantEntityId") UUID participantEntityId, @Param("participantGroupType") @Size(max = 255) String participantGroupType);

    @Query(value = """
            SELECT
            CASE 
            WHEN "activityType" = 'NEW' AND "dogSpayedOrNeutered" = 'yes' AND COALESCE("seniorDiscountStatusText", '') != '*Senior Discount' THEN 'New Altered' 
            WHEN "activityType" = 'NEW' AND "dogSpayedOrNeutered" = 'no' AND COALESCE("seniorDiscountStatusText", '') != '*Senior Discount' THEN 'New Unaltered' 
            WHEN "activityType" = 'NEW' AND "dogSpayedOrNeutered" = 'yes' AND COALESCE("seniorDiscountStatusText", '') = '*Senior Discount' THEN 'New Altered Senior' 
            WHEN "activityType" = 'NEW' AND "dogSpayedOrNeutered" = 'no' AND COALESCE("seniorDiscountStatusText", '') = '*Senior Discount' THEN 'New Unaltered Senior' 
            WHEN "activityType" = 'RENEWAL' AND "dogSpayedOrNeutered" = 'yes' AND COALESCE("seniorDiscountStatusText", '') != '*Senior Discount' THEN 'Renewal Altered' 
            WHEN "activityType" = 'RENEWAL' AND "dogSpayedOrNeutered" = 'no' AND COALESCE("seniorDiscountStatusText", '') != '*Senior Discount' THEN 'Renewal Unaltered' 
            WHEN "activityType" = 'RENEWAL' AND "dogSpayedOrNeutered" = 'yes' AND COALESCE("seniorDiscountStatusText", '') = '*Senior Discount' THEN 'Renewal Altered Senior' 
            WHEN "activityType" = 'RENEWAL' AND "dogSpayedOrNeutered" = 'no' AND COALESCE("seniorDiscountStatusText", '') = '*Senior Discount' THEN 'Renewal Unaltered Senior' 
            END AS category, 
            COUNT(*) AS count 
            FROM view_license_for_reports r 
            WHERE "licenseIssuedDate" BETWEEN :startDate AND :endDate 
            GROUP BY category 
            UNION 
            SELECT 'Dog Tags' AS ltype, COUNT(*) 
            FROM license_activity_fee WHERE fee_code = 'DLP-M-DOG'
            """,
            nativeQuery = true
    )
    List<ClerksSummaryReportCountsProjection> findClerkSummaryReportCountsByDate(LocalDate startDate, LocalDate endDate);

    @Query(value = "SELECT nextval('license_number_seq')", nativeQuery = true)
    Long getNextLicenseNumber();

    @Query(value = "SELECT nextval('purebred_license_number_seq')", nativeQuery = true)
    Long getNextPurebredLicenseNumber();

    @Query(value = "SELECT nextval('pending_license_number_seq')", nativeQuery = true)
    Long getNextPendingDogLicenseNumber();

    @Query(value = "SELECT nextval('pending_purebred_license_number_seq')", nativeQuery = true)
    Long getNextPendingPurebredDogLicenseNumber();
}
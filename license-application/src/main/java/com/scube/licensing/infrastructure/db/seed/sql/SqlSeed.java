package com.scube.licensing.infrastructure.db.seed.sql;

import com.scube.licensing.LicenseServiceApplication;
import com.scube.licensing.infrastructure.db.repository.license.LicenseTypeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@Component
@Slf4j
@RequiredArgsConstructor
public class SqlSeed {
    private final JdbcTemplate jdbcTemplate;
    private final Environment environment;
    private final LicenseTypeRepository licenseTypeRepository;

    public void seed() {
        log.info("Start IndividualSearchFunctionSeed.seed()...");

        var activeProfiles = environment.getActiveProfiles();

        Path seedFolderPath;
        Path seedFolderPath2 = null;
        try {
            seedFolderPath = Paths.get(Objects.requireNonNull(LicenseServiceApplication.class.getClassLoader().getResource("seed/sql")).toURI());
            if (activeProfiles.length > 0 && activeProfiles[0].equals("test")) {
                seedFolderPath2 = Paths.get(Objects.requireNonNull(LicenseServiceApplication.class.getClassLoader().getResource("test-seed/sql")).toURI());
            }
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        Map<String, String> mapQuery = new TreeMap<>();
        mapQuery.putAll(processFiles(seedFolderPath));
        mapQuery.putAll(processFiles(seedFolderPath2));
        //sort the mapQuery by key

        try {
            for (String string : mapQuery.values()) {
                jdbcTemplate.execute(string);
            }
        } catch (Exception e) {
            log.error("Error while seeding sql files", e);
        }

        log.info("End IndividualSearchFunctionSeed.seed()...");
    }

    private Map<String, String> processFiles(@Nullable Path seedFolderPath) {
        if (seedFolderPath == null) {
            return Collections.emptyMap();
        }
        Map<String, String> result = new HashMap<>();
        try (var fileStream = Files.list(seedFolderPath)) {
            fileStream
                    .filter(Files::isRegularFile)
                    .filter(file -> file.toString().endsWith(".sql"))
                    .sorted()
                    .forEach(file -> {
                        try {
                            result.put(file.getFileName().toString(), Files.readString(file));
                        } catch (IOException e) {
                            throw new RuntimeException(e);
                        }
                    });
            return result;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Collections.emptyMap();
    }
}
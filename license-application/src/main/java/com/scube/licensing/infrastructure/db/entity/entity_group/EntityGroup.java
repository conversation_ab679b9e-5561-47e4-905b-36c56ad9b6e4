package com.scube.licensing.infrastructure.db.entity.entity_group;

import com.scube.licensing.features.entity.dtos.EntityAssociation;
import com.scube.licensing.features.entity_group.dtos.EntityGroupRequest;
import com.scube.licensing.features.events.Event;
import com.scube.licensing.features.events.add_event.AddHistoryDomainEvent;
import com.scube.licensing.infrastructure.db.entity.association.Associable;
import com.scube.licensing.infrastructure.db.entity.association.Association;
import com.scube.licensing.infrastructure.db.entity.association.AssociationType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.springframework.util.ObjectUtils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@Entity
@Table(name = EntityGroup.TABLE_NAME)
@Getter
@Setter
@Accessors(chain = true)
@Audited
public class EntityGroup extends Associable {
    public static final String TABLE_NAME = "entity_group";
    public static final String ENTITY_TYPE = "entityGroup";
    public static final String ENTITY_GROUP_ID = "entity_group_id";

    @Column(nullable = false, columnDefinition = "TEXT")
    private String name;

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }

    /*
     * Associations
     */
    // dummy column
    @Enumerated(EnumType.STRING)
    @Column(name = "dummy_column")
    private AssociationType associationType = AssociationType.ENTITY_GROUP;


    @OneToMany(mappedBy = "parentEntityGroup", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> childAssociations = new HashSet<>();

    @OneToMany(mappedBy = "childEntityGroup", cascade = CascadeType.ALL, orphanRemoval = true)
    @NotAudited
    private Set<Association> parentAssociations = new HashSet<>();

    @Override
    public String getEntityType() {
        return ENTITY_TYPE;
    }

    public void createGroup(EntityGroupRequest request) {
        this.name = request.getName();
        updateCustomFields(request);

        if (ObjectUtils.isEmpty(request.getAssociations())) {
            addEvent(new Event("entityGroupAdded", "Added group " + this.name));
        }
        putAssociations(request.getAssociations());
    }

    public void putAssociations(Set<EntityAssociation> entityFeeAssociations) {
        removeAssociations();
        addAssociations(entityFeeAssociations);
    }

    public void removeAssociations() {
        var parentAssociables = this.getParentAssociables();
        for (var parent : parentAssociables) {
            this.removeParentAssociable(parent);
            parent.addEvent(new Event("entityGroupRemoved", "Removed group " + this.name, Map.of(
                    "childEntityType", this.getEntityType(),
                    "childEntityId", this.getUuid().toString(),
                    "groupName", this.name,
                    "entityType", parent.getEntityType(),
                    "entityId", parent.getUuid().toString()
            )));
        }
    }

    public void addAssociations(Set<EntityAssociation> entityFeeAssociations) {
        for (EntityAssociation assoc : entityFeeAssociations) {
            this.addParentAssociable(assoc.getEntityType(), assoc.getEntityId());
            this.addEvent(new Event("entityGroupAdded", "Added group: " + this.name, Map.of(
                    "entityType", this.getEntityType(),
                    "entityId", this.getUuid().toString(),
                    "groupName", this.name,
                    "parentEntityType", assoc.getEntityType(),
                    "parentEntityId", assoc.getEntityId().toString()
            )));

            // add event to the parent entity
            var parentEvent = new Event("entityGroupAdded", "Added Group: " + this.name, Map.of(
                    "entityType", assoc.getEntityType(),
                    "entityId", assoc.getEntityId().toString(),
                    "groupName", this.name,
                    "childEntityType", this.getEntityType(),
                    "childEntityId", this.getUuid().toString()
            ));
            this.registerEvent(new AddHistoryDomainEvent(assoc.getEntityType(), assoc.getEntityId(), parentEvent));
        }
    }

    public void updateGroup(EntityGroupRequest request) {
        if (!ObjectUtils.isEmpty(request.getName()))
            this.setName(request.getName());

        updateCustomFields(request);

        var associations = request.getAssociations();
        if (!ObjectUtils.isEmpty(associations))
            putAssociations(associations);
    }
}
package com.scube.licensing.infrastructure.db.repository.merge_request;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequest;
import com.scube.licensing.infrastructure.db.entity.merge_request.MergeRequestStatusEnum;
import jakarta.validation.constraints.Size;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.UUID;

@Repository
public interface MergeRequestRepository extends AuditableEntityRepository<MergeRequest, Long> {

    List<MergeRequest> findAllByRequestedUserIdAndStatusIn(UUID requestedUserId, List<MergeRequestStatusEnum> statuses);

    boolean existsByUuidAndRequestedUserId(UUID entityId, UUID requestedUserId);

    List<MergeRequest> findAllByStatusIn(List<MergeRequestStatusEnum> statuses);

    @Query(nativeQuery = true, value = "SELECT * FROM fn_search_merge_request(:searchLicenseNumber, :searchTagNumber, :searchRequestedUserId)")
    List<Map<String, Object>> getSearchMergeRequests(@Param("searchLicenseNumber") @Size(max = 255) String searchLicenseNumber,
                                                     @Param("searchTagNumber") @Size(max = 255) String searchTagNumber,
                                                     @Param("searchRequestedUserId") UUID searchRequestedUserId);
}
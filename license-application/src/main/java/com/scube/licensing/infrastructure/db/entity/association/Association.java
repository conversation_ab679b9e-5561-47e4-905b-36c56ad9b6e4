package com.scube.licensing.infrastructure.db.entity.association;

import com.scube.licensing.infrastructure.db.entity.BaseEntity;
import com.scube.licensing.infrastructure.db.entity.address.Address;
import com.scube.licensing.infrastructure.db.entity.business.Business;
import com.scube.licensing.infrastructure.db.entity.custom_entity.CustomEntityInstance;
import com.scube.licensing.infrastructure.db.entity.document.Document;
import com.scube.licensing.infrastructure.db.entity.entity_fee.EntityFee;
import com.scube.licensing.infrastructure.db.entity.entity_group.EntityGroup;
import com.scube.licensing.infrastructure.db.entity.entity_note.EntityNote;
import com.scube.licensing.infrastructure.db.entity.license.License;
import com.scube.licensing.infrastructure.db.entity.participant.Participant;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.JoinColumnOrFormula;
import org.hibernate.envers.Audited;
import org.springframework.lang.NonNull;
import org.springframework.util.Assert;

@Entity
@Table(
        name = Association.TABLE_NAME,
        uniqueConstraints = {
                @UniqueConstraint(
                        name = "association_unique",
                        columnNames = {
                                Association.PARENT_ASSOCIATION_TYPE,
                                Association.PARENT_ID,
                                Association.CHILD_ASSOCIATION_TYPE,
                                Association.CHILD_ID
                        }
                )
        },
        indexes = {
                @Index(name = "idx_association_parent_types", columnList = Association.PARENT_ASSOCIATION_TYPE + ", " + Association.PARENT_ID),
                @Index(name = "idx_association_child_types", columnList = Association.CHILD_ASSOCIATION_TYPE + ", " + Association.CHILD_ID)
        }
)
@Getter
@NoArgsConstructor
@Accessors(chain = true)
@Audited
public class Association extends BaseEntity {
    public static final String TABLE_NAME = "association";
    public static final String ASSOCIATION_ID = "association_id";
    public static final String PARENT_ASSOCIATION_TYPE = "parent_association_type";
    public static final String PARENT_ID = "parent_id";
    public static final String CHILD_ASSOCIATION_TYPE = "child_association_type";
    public static final String CHILD_ID = "child_id";

    //parent AssociationType
    @Enumerated(EnumType.STRING)
    @Column(name = PARENT_ASSOCIATION_TYPE, nullable = false)
    private AssociationType parentAssociationType;

    @Column(name = PARENT_ID, nullable = false)
    private Long parentId;

    //child AssociationType
    @Enumerated(EnumType.STRING)
    @Column(name = CHILD_ASSOCIATION_TYPE, nullable = false)
    private AssociationType childAssociationType;

    @Column(name = CHILD_ID, nullable = false)
    private Long childId;

    public Association setParent(@NonNull Associable parent) {
        Assert.notNull(parent.getId(), "Associable id must not be null");
        this.setParentAssociable(parent);
        this.parentId = parent.getId();
        this.parentAssociationType = parent.getAssociationType();
        return this;
    }

    public Association setChild(@NonNull Associable child) {
        Assert.notNull(child.getId(), "Associable id must not be null");
        this.setChildAssociable(child);
        this.childId = child.getId();
        this.childAssociationType = child.getAssociationType();
        return this;
    }

    @NonNull
    public Associable getParentAssociable() {
        return switch (parentAssociationType) {
            case PARTICIPANT -> parentParticipant;
            case ADDRESS -> parentAddress;
            case BUSINESS -> parentBusiness;
            case CUSTOM_ENTITY -> parentCustomEntityInstance;
            case DOCUMENT -> parentDocument;
            case ENTITY_FEE -> parentEntityFee;
            case ENTITY_GROUP -> parentEntityGroup;
            case ENTITY_NOTE -> parentEntityNote;
            case LICENSE -> parentLicense;
        };
    }

    @NonNull
    public Associable getChildAssociable() {
        return switch (childAssociationType) {
            case PARTICIPANT -> childParticipant;
            case ADDRESS -> childAddress;
            case BUSINESS -> childBusiness;
            case CUSTOM_ENTITY -> childCustomEntityInstance;
            case DOCUMENT -> childDocument;
            case ENTITY_FEE -> childEntityFee;
            case ENTITY_GROUP -> childEntityGroup;
            case ENTITY_NOTE -> childEntityNote;
            case LICENSE -> childLicense;
        };
    }

    public void setParentAssociable(@NonNull Associable parent) {
        switch (parent.getAssociationType()) {
            case PARTICIPANT -> parentParticipant = (Participant) parent;
            case ADDRESS -> parentAddress = (Address) parent;
            case BUSINESS -> parentBusiness = (Business) parent;
            case CUSTOM_ENTITY -> parentCustomEntityInstance = (CustomEntityInstance) parent;
            case DOCUMENT -> parentDocument = (Document) parent;
            case ENTITY_FEE -> parentEntityFee = (EntityFee) parent;
            case ENTITY_GROUP -> parentEntityGroup = (EntityGroup) parent;
            case ENTITY_NOTE -> parentEntityNote = (EntityNote) parent;
            case LICENSE -> parentLicense = (License) parent;
        }
    }

    public void setChildAssociable(@NonNull Associable child) {
        switch (child.getAssociationType()) {
            case PARTICIPANT -> childParticipant = (Participant) child;
            case ADDRESS -> childAddress = (Address) child;
            case BUSINESS -> childBusiness = (Business) child;
            case CUSTOM_ENTITY -> childCustomEntityInstance = (CustomEntityInstance) child;
            case DOCUMENT -> childDocument = (Document) child;
            case ENTITY_FEE -> childEntityFee = (EntityFee) child;
            case ENTITY_GROUP -> childEntityGroup = (EntityGroup) child;
            case ENTITY_NOTE -> childEntityNote = (EntityNote) child;
            case LICENSE -> childLicense = (License) child;
        }
    }

    /*
     * Participant
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Participant parentParticipant;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Participant childParticipant;

    /*
     * Address
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Address parentAddress;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Address childAddress;

    /*
     * Business
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Business parentBusiness;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Business childBusiness;

    /*
     * Custom Entity Instance
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private CustomEntityInstance parentCustomEntityInstance;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private CustomEntityInstance childCustomEntityInstance;

    /*
     * Document
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Document parentDocument;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private Document childDocument;

    /*
     * Entity Fee
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private EntityFee parentEntityFee;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private EntityFee childEntityFee;

    /*
     * Entity Group
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private EntityGroup parentEntityGroup;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private EntityGroup childEntityGroup;

    /*
     * Entity Note
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private EntityNote parentEntityNote;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private EntityNote childEntityNote;

    /*
     * License
     */
    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = PARENT_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private License parentLicense;

    @ManyToOne
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ID, referencedColumnName = "id", insertable = false, updatable = false))
    @JoinColumnOrFormula(column = @JoinColumn(name = CHILD_ASSOCIATION_TYPE, referencedColumnName = "dummy_column", insertable = false, updatable = false))
    private License childLicense;


    public Association(@NonNull Associable parent, @NonNull Associable child) {
        this.setParent(parent);
        this.setChild(child);
    }

    @Override
    public String getTableName() {
        return TABLE_NAME;
    }
}
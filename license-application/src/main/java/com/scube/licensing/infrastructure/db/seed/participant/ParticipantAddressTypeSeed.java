package com.scube.licensing.infrastructure.db.seed.participant;

import com.scube.licensing.infrastructure.db.entity.participant.address.ParticipantAddressType;
import com.scube.licensing.infrastructure.db.repository.participant.ParticipantAddressTypeRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class ParticipantAddressTypeSeed {

    public static final String MAILING = "Mailing";
    public static final String PRIMARY = "Primary";
    @Autowired
    private ParticipantAddressTypeRepository participantAddressTypeRepository;

    @Transactional
    public void seed() {
        log.info("Start ParticipantAddressTypeSeed.seed()...");

        if (participantAddressTypeRepository.count() > 0) {
            log.info("ParticipantAddressTypeSeed.seed() already seeded...");
            return;
        }

        participantAddressTypeRepository.save(
                new ParticipantAddressType(MAILING, "Mailing participant group")
        );

        participantAddressTypeRepository.save(
                new ParticipantAddressType(PRIMARY, "Home participant group")
        );

        participantAddressTypeRepository.save(
                new ParticipantAddressType("Physical", "")
        );
        participantAddressTypeRepository.save(
                new ParticipantAddressType("placeholder", "")
        );
        participantAddressTypeRepository.save(
                new ParticipantAddressType("Home", "")
        );
        participantAddressTypeRepository.save(
                new ParticipantAddressType("Work", "")
        );
        participantAddressTypeRepository.save(
                new ParticipantAddressType("Other", "")
        );

        log.info("End ParticipantAddressTypeSeed.seed()...");
    }
}

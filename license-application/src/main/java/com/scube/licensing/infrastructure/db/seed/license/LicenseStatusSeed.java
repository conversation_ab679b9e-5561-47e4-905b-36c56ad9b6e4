package com.scube.licensing.infrastructure.db.seed.license;

import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatus;
import com.scube.licensing.infrastructure.db.entity.license.status.LicenseStatusCodeEnum;
import com.scube.licensing.infrastructure.db.repository.license.LicenseStatusRepository;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class LicenseStatusSeed {
    private final LicenseStatusRepository licenseStatusRepository;

    @Autowired
    public LicenseStatusSeed(LicenseStatusRepository licenseStatusRepository) {
        this.licenseStatusRepository = licenseStatusRepository;
    }

    @Transactional
    public void seed() {
        log.info("Start LicenseStatusSeed.seed()...");

        if (!licenseStatusRepository.existsByNameIgnoreCase("Pending"))
            licenseStatusRepository.save(new LicenseStatus("Pending", LicenseStatusCodeEnum.PENDING));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Pending Payment"))
            licenseStatusRepository.save(new LicenseStatus("Pending Payment", LicenseStatusCodeEnum.PENDING_PAYMENT));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Pending Approval"))
            licenseStatusRepository.save(new LicenseStatus("Pending Approval", LicenseStatusCodeEnum.PENDING_APPROVAL));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Draft"))
            licenseStatusRepository.save(new LicenseStatus("Draft", LicenseStatusCodeEnum.DRAFT));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Submitted"))
            licenseStatusRepository.save(new LicenseStatus("Submitted", LicenseStatusCodeEnum.SUBMITTED));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Payment Completed"))
            licenseStatusRepository.save(new LicenseStatus("Payment Completed", LicenseStatusCodeEnum.SUBMITTED));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Rejected"))
            licenseStatusRepository.save(new LicenseStatus("Rejected", LicenseStatusCodeEnum.REJECTED));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Approved"))
            licenseStatusRepository.save(new LicenseStatus("Approved", LicenseStatusCodeEnum.APPROVED));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Active"))
            licenseStatusRepository.save(new LicenseStatus("Active", LicenseStatusCodeEnum.ACTIVE));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Expired"))
            licenseStatusRepository.save(new LicenseStatus("Expired", LicenseStatusCodeEnum.EXPIRED));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Canceled"))
            licenseStatusRepository.save(new LicenseStatus("Canceled", LicenseStatusCodeEnum.CANCELED));

        if (!licenseStatusRepository.existsByNameIgnoreCase("Closed"))
            licenseStatusRepository.save(new LicenseStatus("Closed", LicenseStatusCodeEnum.CLOSED));

        log.info("End LicenseStatusSeed.seed()...");
    }
}
package com.scube.licensing.infrastructure.db.repository.license;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.licensing.infrastructure.db.entity.license.LicenseActivity;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public interface LicenseActivityRepository extends AuditableEntityRepository<LicenseActivity, Long> {

    @Query(nativeQuery = true, value = """
            select la.activity_type from license_activity la 
            where la.license_activity_id in
            (
                select laf.license_activity_id from license_activity_fee laf
            	where order_id = :orderId
            	order by laf.license_activity_id
            )
            """)
    List<String> getActivitiesByOrderId(UUID orderId);

    @Query(nativeQuery = true, value = "select * from determineLicenseActivityDate(:licenseEntityId) d")
    Map<String, Object> determineLicenseActivityDates(UUID licenseEntityId);
}
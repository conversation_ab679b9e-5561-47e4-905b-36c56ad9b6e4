package com.scube.licensing.infrastructure.db.entity.code_lookup;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.lang.Nullable;
import org.springframework.web.server.ResponseStatusException;

import static org.springframework.http.HttpStatus.BAD_REQUEST;

@Getter
@AllArgsConstructor
public enum CodeLookupActionEnum {
    LOOKUP("lookup"),
    CREATE("create"),
    REGISTER("register"),
    R<PERSON>E<PERSON>("renew"),
    LANDING_PAGE("landing_page"),
    LOGIN_PAGE("login_page"),
    SIGN_UP_PAGE("sign_up_page"),
    DEMO_PAGE("demo_page");

    private final String value;

    public static CodeLookupActionEnum fromValue(@Nullable String value) {
        for (CodeLookupActionEnum type : values()) {
            if (type.value.equalsIgnoreCase(value)) {
                return type;
            }
        }
        throw new ResponseStatusException(BAD_REQUEST, "Invalid action type: " + value);
    }


    @Override
    public String toString() {
        return value;
    }
}
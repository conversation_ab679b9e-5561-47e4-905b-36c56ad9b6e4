<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1694643161066-2">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="template_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694643161066-3">
        <addColumn tableName="template">
            <column name="created_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694643161066-4">
        <addColumn tableName="template">
            <column name="created_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694643161066-5">
        <addColumn tableName="template">
            <column name="last_modified_by" type="varchar(250 BYTE)" defaultValue="liquibase">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694643161066-6">
        <addColumn tableName="template">
            <column name="last_modified_date" type="timestamptz" defaultValue="now()">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694643161066-7">
        <renameColumn tableName="template" oldColumnName="document_uuid" newColumnName="documentuuid"
                      columnDataType="uuid"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694643161066-8">
        <renameColumn tableName="template" oldColumnName="sub_category" newColumnName="subcategory"
                      columnDataType="varchar(255 BYTE)"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1694643161066-1">
        <modifyDataType columnName="template_id" newDataType="bigint" tableName="template"/>
    </changeSet>
</databaseChangeLog>
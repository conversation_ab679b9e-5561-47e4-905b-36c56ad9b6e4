<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="updateDefaultAuditRecord_update3" author="david">
        <sqlFile path="update_table_with_createdBy_and_modified_by.sql"
                 relativeToChangelogFile="true" splitStatements="false"/>
    </changeSet>

    <changeSet author="davidr (generated)" id="1695362402947-1">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="audit_log_revision_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-2">
        <createSequence cacheSize="1" cycle="false" dataType="bigint" incrementBy="50" maxValue="9223372036854775807"
                        minValue="1" sequenceName="prompt_seq" startValue="1"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-3">
        <createTable tableName="audit_log_prompt">
            <column name="prompt_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_prompt_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_prompt_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="prompt_key" type="VARCHAR(255)"/>
            <column name="prompt_text" type="TEXT"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-4">
        <createTable tableName="audit_log_revision">
            <column name="audit_log_revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_revision_pkey"/>
            </column>
            <column name="timestamp" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="auditor" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-5">
        <addColumn tableName="prompt">
            <column name="conversion_reference" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-6">
        <addColumn tableName="prompt">
            <column name="created_by" type="varchar(1000 BYTE)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-7">
        <addColumn tableName="prompt">
            <column name="created_date" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-8">
        <addColumn tableName="prompt">
            <column name="last_modified_by" type="varchar(1000 BYTE)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-9">
        <addColumn tableName="prompt">
            <column name="last_modified_date" type="timestamptz">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1695362402947-10">
        <addForeignKeyConstraint baseColumnNames="revision_id" baseTableName="audit_log_prompt"
                                 constraintName="fk9651ouqpr779ungpc14jmfw31" deferrable="false"
                                 initiallyDeferred="false" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="audit_log_revision_id" referencedTableName="audit_log_revision"
                                 validate="true"/>
    </changeSet>
</databaseChangeLog>
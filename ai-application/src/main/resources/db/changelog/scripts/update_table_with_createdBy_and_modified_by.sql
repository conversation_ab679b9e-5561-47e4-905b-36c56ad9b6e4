DO $$
DECLARE
    tableName text;
BEGIN
	--last_modified_by
    FOR tableName IN (SELECT table_name
						FROM information_schema.tables
						WHERE table_type = 'BASE TABLE'
						and table_name in (
							SELECT table_name
							 FROM information_schema.columns
							 WHERE column_name IN ('last_modified_by')
							 and table_schema = 'ai'
						))
    LOOP
        EXECUTE 'UPDATE ' || tableName || '
                 SET last_modified_by = COALESCE(last_modified_by, ''system'')
                 WHERE last_modified_by IS NULL;';
    END LOOP;
	--created_by
	FOR tableName IN (SELECT table_name
						FROM information_schema.tables
						WHERE table_type = 'BASE TABLE'
						and table_name in (
							SELECT table_name
							 FROM information_schema.columns
							 WHERE column_name IN ('created_by')
							 and table_schema = 'ai'
						))
    LOOP
        EXECUTE 'UPDATE ' || tableName || '
                 SET created_by = COALESCE(created_by, ''system'')
                 WHERE created_by IS NULL;';
    END LOOP;
END $$;
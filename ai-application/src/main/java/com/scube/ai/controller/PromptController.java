package com.scube.ai.controller;


import com.scube.ai.dto.CreatePromptRequest;
import com.scube.ai.dto.UpdatePromptRequest;
import com.scube.ai.entity.Prompt;
import com.scube.ai.permission.Permissions;
import com.scube.ai.service.PromptService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/prompts")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.AI_SERVICE)
@Validated
public class PromptController {
    private final PromptService promptService;

    @GetMapping
    @RolesAllowed(Permissions.Prompt.GET_ALL_PROMPTS)
    @ResponseStatus(HttpStatus.OK)
    public List<Prompt> getAllPrompts() {
        return promptService.findAll();
    }

    @GetMapping("/{uuid}")
    @RolesAllowed(Permissions.Prompt.GET_PROMPT_BY_ID)
    @ResponseStatus(HttpStatus.OK)
    public Prompt getPromptById(@PathVariable UUID uuid) {
        return promptService.findByUuidOrThrow(uuid);
    }

    @GetMapping("/key/{promptKey}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.Prompt.GET_PROMPT_BY_KEY)
    public Prompt getPromptByKey(@PathVariable @NotEmpty String promptKey) {
        return promptService.findByPromptKey(promptKey);
    }

    @PostMapping
    @RolesAllowed(Permissions.Prompt.CREATE_PROMPT)
    @ResponseStatus(HttpStatus.CREATED)
    public Prompt createPrompt(@RequestBody CreatePromptRequest request) {
        return promptService.save(request);
    }

    @PutMapping
    @RolesAllowed(Permissions.Prompt.UPDATE_PROMPT)
    @ResponseStatus(HttpStatus.OK)
    public Prompt updatePrompt(@RequestBody UpdatePromptRequest request) {
        return promptService.update(request);
    }

    @DeleteMapping("/{uuid}")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @RolesAllowed(Permissions.Prompt.DELETE_PROMPT)
    public void deletePrompt(@PathVariable UUID uuid) {
        promptService.deleteByUuid(uuid);
    }
}

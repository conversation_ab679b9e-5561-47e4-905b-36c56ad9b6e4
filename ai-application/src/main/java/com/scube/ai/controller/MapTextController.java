package com.scube.ai.controller;

import com.scube.ai.dto.MapImageToTextRequest;
import com.scube.ai.dto.MapTextRequest;
import com.scube.ai.dto.MapTextResponse;
import com.scube.ai.dto.MapTextToSchemaRequest;
import com.scube.ai.permission.Permissions;
import com.scube.ai.service.MapTextService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.lib.misc.annotations.validation.NoValidation;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotEmpty;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@GenerateHttpExchange(value = ServiceUrlConstant.AI_SERVICE)
@RequiredArgsConstructor
@Validated
public class MapTextController {
    private final MapTextService mapTextService;

    @PostMapping("map-text")
    @RolesAllowed(Permissions.MapText.PARSE_OCR_TEXT)
    public MapTextResponse parseOcrText(@RequestBody MapTextRequest request) {
        return mapTextService.mapText(request);
    }

    @PostMapping("map-text-to-schema")
    @RolesAllowed(Permissions.MapText.MAP_TEXT_TO_SCHEMA)
    public MapTextResponse mapTextToSchema(@RequestBody MapTextToSchemaRequest request) {
        return mapTextService.mapTextToSchema(request);
    }

    @PostMapping(value = "map-image-to-schema", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @RolesAllowed(Permissions.MapText.MAP_IMAGE_TO_SCHEMA)
    public MapTextResponse mapImageToSchema(
            @RequestPart("file") MultipartFile file,
            @RequestPart("schema") @NotEmpty String schema,
            @RequestParam("promptKey") @NoValidation String promptKey,
            @RequestParam("prompt") @NoValidation String prompt) {
        return mapTextService.mapImageToSchema(new MapImageToTextRequest(file, schema, prompt, promptKey));
    }
}
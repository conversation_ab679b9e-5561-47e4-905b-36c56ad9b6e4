package com.scube.payment.features.payment.processing.service;

import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentResponseDto;
import com.scube.payment.features.payment.processing.mapper.PaymentMapper;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.payment.util.Mocks;
import com.scube.payment.util.TestPaymentMapperImpl;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("test")
public class PaymentProcessingServiceUnitTest {
    @InjectMocks
    private PaymentProcessingService paymentProcessingService;
    @Mock
    private AmqpGateway amqpGateway;
    @Mock
    private PaymentStorageService paymentStorageService;

    PaymentMapper paymentMapper = new TestPaymentMapperImpl();

    @Test
    public void testSubmitPaymentFullPayment() {
        UUID orderId = UUID.randomUUID();
        BigDecimal orderAmount = new BigDecimal("100.00");
        BigDecimal paymentAmount = new BigDecimal("100.00");

        SubmitPaymentRequestDto paymentRequest = Mocks.createMockSubmitPaymentRequestDto();
        paymentRequest.setOrderId(orderId);
        paymentRequest.setOrderAmount(orderAmount);
        paymentRequest.setPaymentAmount(paymentAmount);

        Payment expectedPayment = paymentMapper.toEntity(paymentRequest);
        expectedPayment.setUuid(UUID.randomUUID());
        expectedPayment.setStatus(PaymentStatus.SUCCESS);
        expectedPayment.setTransactionId(UUID.randomUUID().toString());

        when(paymentStorageService.getPayments(orderId)).thenReturn(new ArrayList<>());
        when(paymentStorageService.storePayment(paymentRequest)).thenReturn(expectedPayment);

        ArgumentCaptor<PaymentProcessingService.OrderPaymentCompletedEvent> eventCaptor = ArgumentCaptor.forClass(PaymentProcessingService.OrderPaymentCompletedEvent.class);

        doNothing().when(amqpGateway).publish(eventCaptor.capture());

        SubmitPaymentResponseDto actualResp = paymentProcessingService.submitPayment(paymentRequest);

        verify(amqpGateway, times(1)).publish(eventCaptor.capture());

        assertEquals(eventCaptor.getValue(), new PaymentProcessingService.OrderPaymentCompletedEvent(orderId.toString()));

        assertEquals(actualResp.getOrderId(), orderId);
        assertEquals(actualResp.getBalance(), new BigDecimal("0.00"));
        assertEquals(actualResp.getOrderId().toString(), eventCaptor.getValue().orderId());
        assertEquals(actualResp.getPaymentId(), expectedPayment.getUuid());
    }

    @Test
    public void testSubmitPaymentPartialPayment() {
        UUID orderId = UUID.randomUUID();
        BigDecimal orderAmount = new BigDecimal("100.00");
        BigDecimal paymentAmount = new BigDecimal("50.00");

        SubmitPaymentRequestDto paymentRequest = Mocks.createMockSubmitPaymentRequestDto();
        paymentRequest.setOrderId(orderId);
        paymentRequest.setOrderAmount(orderAmount);
        paymentRequest.setPaymentAmount(paymentAmount);

        Payment expectedPayment = paymentMapper.toEntity(paymentRequest);
        expectedPayment.setUuid(UUID.randomUUID());
        expectedPayment.setStatus(PaymentStatus.SUCCESS);
        expectedPayment.setTransactionId(UUID.randomUUID().toString());

        when(paymentStorageService.getPayments(orderId)).thenReturn(new ArrayList<>());
        when(paymentStorageService.storePayment(paymentRequest)).thenReturn(expectedPayment);

        ArgumentCaptor<PaymentProcessingService.OrderSubmitPaymentEvent> eventCaptor = ArgumentCaptor.forClass(PaymentProcessingService.OrderSubmitPaymentEvent.class);

        doNothing().when(amqpGateway).publish(eventCaptor.capture());

        SubmitPaymentResponseDto actualResp = paymentProcessingService.submitPayment(paymentRequest);

        verify(amqpGateway, times(1)).publish(eventCaptor.capture());

        assertEquals(eventCaptor.getValue(), new PaymentProcessingService.OrderSubmitPaymentEvent(orderId.toString()));

        assertEquals(actualResp.getOrderId(), orderId);
        assertEquals(actualResp.getBalance(), new BigDecimal("50.00"));
        assertEquals(actualResp.getOrderId().toString(), eventCaptor.getValue().orderId());
        assertEquals(actualResp.getPaymentId(), expectedPayment.getUuid());
    }

    @Test
    public void testSubmitPaymentOverPayment() {
        UUID orderId = UUID.randomUUID();
        BigDecimal orderAmount = new BigDecimal("100.00");
        BigDecimal paymentAmount = new BigDecimal("150.00");

        SubmitPaymentRequestDto paymentRequest = Mocks.createMockSubmitPaymentRequestDto();
        paymentRequest.setOrderId(orderId);
        paymentRequest.setOrderAmount(orderAmount);
        paymentRequest.setPaymentAmount(paymentAmount);

        Payment expectedPayment = paymentMapper.toEntity(paymentRequest);
        expectedPayment.setUuid(UUID.randomUUID());
        expectedPayment.setStatus(PaymentStatus.SUCCESS);
        expectedPayment.setTransactionId(UUID.randomUUID().toString());

        when(paymentStorageService.getPayments(orderId)).thenReturn(new ArrayList<>());

        verify(paymentStorageService, never()).storePayment(paymentRequest);
        verify(amqpGateway, never()).publish(any(IRabbitFanoutPublisher.class));
        verify(amqpGateway, never()).publish(any(IRabbitFanoutPublisherRpc.class));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> {
            paymentProcessingService.submitPayment(paymentRequest);
        });
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("Payment amount exceeds balance of 100.00", exception.getReason());
    }

    @Test
    public void testSubmitPaymentOverPayment_existingPayments() {
        UUID orderId = UUID.randomUUID();
        BigDecimal orderAmount = new BigDecimal("100.00");
        BigDecimal paymentAmount = new BigDecimal("50.00");

        SubmitPaymentRequestDto paymentRequest = Mocks.createMockSubmitPaymentRequestDto();
        paymentRequest.setOrderId(orderId);
        paymentRequest.setOrderAmount(orderAmount);
        paymentRequest.setPaymentAmount(paymentAmount);

        Payment expectedPayment = paymentMapper.toEntity(paymentRequest);
        expectedPayment.setUuid(UUID.randomUUID());
        expectedPayment.setStatus(PaymentStatus.SUCCESS);
        expectedPayment.setTransactionId(UUID.randomUUID().toString());

        Payment existingPayment1 = Mocks.createMockPayment();
        existingPayment1.setAmount(new BigDecimal("30.00"));

        Payment existingPayment2 = Mocks.createMockPayment();
        existingPayment2.setAmount(new BigDecimal("30.00"));

        List<Payment> existingPayments = List.of(existingPayment1, existingPayment2);

        when(paymentStorageService.getPayments(orderId)).thenReturn(existingPayments);

        verify(paymentStorageService, never()).storePayment(paymentRequest);
        verify(amqpGateway, never()).publish(any(IRabbitFanoutPublisher.class));
        verify(amqpGateway, never()).publish(any(IRabbitFanoutPublisherRpc.class));

        ResponseStatusException exception = assertThrows(ResponseStatusException.class, () -> {
            paymentProcessingService.submitPayment(paymentRequest);
        });
        assertEquals(HttpStatus.BAD_REQUEST.value(), exception.getStatusCode().value());
        assertEquals("Payment amount exceeds balance of 40.00", exception.getReason());
    }
}
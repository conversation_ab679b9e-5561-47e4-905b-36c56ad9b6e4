package com.scube.payment.features.webhook.inbox.service;

import com.scube.audit.auditable.properties.type.PropertyTypeService;
import com.scube.lib.misc.BeanUtils;
import com.scube.payment.features.providers.mock.MockPaymentProviderWebhookService;
import com.scube.payment.features.webhook.IPaymentProviderWebhookService;
import com.scube.payment.features.webhook.inbox.model.InboxStatus;
import com.scube.payment.features.webhook.inbox.model.WebhookInbox;
import com.scube.payment.features.webhook.inbox.repo.WebhookInboxRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class WebhookInboxProcessingServiceTest {

    @Mock
    private Map<String, IPaymentProviderWebhookService> webhookServices = new HashMap<>();

    @Mock
    private WebhookInboxRepository webhookInboxRepository;

    @Mock
    private ApplicationContext applicationContext;

    @Mock
    private PropertyTypeService propertyTypeService;

    @InjectMocks
    private WebhookInboxProcessingService webhookInboxProcessingService;

    @BeforeEach
    void setUp() {
        new BeanUtils().setApplicationContext(applicationContext);
        lenient().when(applicationContext.getBean(PropertyTypeService.class)).thenReturn(propertyTypeService);
    }

    public WebhookInbox getWebhookInbox() {
        WebhookInbox webhookInbox = new WebhookInbox();
        webhookInbox.setUuid(UUID.randomUUID());
        webhookInbox.setStatus(InboxStatus.PENDING);
        webhookInbox.setPayload(Map.of("key", "value"));
        webhookInbox.setPaymentProvider("TestProvider");

        return webhookInbox;
    }

    @Test
    public void testProcessWebhook() {
        WebhookInboxProcessingService webhookInboxProcessingService = spy(this.webhookInboxProcessingService);
        UUID webhookInboxUuid = UUID.randomUUID();

        WebhookInbox expectedWebhookInbox = getWebhookInbox();

        when(webhookInboxRepository.findByUuid(webhookInboxUuid)).thenReturn(Optional.of(expectedWebhookInbox));

        webhookInboxProcessingService.processWebhook(webhookInboxUuid);

        verify(webhookInboxProcessingService).processWebhook(expectedWebhookInbox);
    }

    @Test
    public void testProcessWebhookNotFound() {
        UUID webhookInboxUuid = UUID.randomUUID();

        when(webhookInboxRepository.findByUuid(webhookInboxUuid)).thenReturn(Optional.empty());

        assertThrows(ResponseStatusException.class, () -> webhookInboxProcessingService.processWebhook(webhookInboxUuid));
    }

    @Test
    public void testProcessWebhookAlreadyProcessed() {
        WebhookInbox expectedWebhookInbox = getWebhookInbox();
        expectedWebhookInbox.setStatus(InboxStatus.COMPLETED);

        when(webhookInboxRepository.findByUuid(expectedWebhookInbox.getUuid())).thenReturn(Optional.of(expectedWebhookInbox));

        assertThrows(ResponseStatusException.class, () -> webhookInboxProcessingService.processWebhook(expectedWebhookInbox.getUuid()));
    }

    @Test
    public void testProcessWebhookInbox() {
        WebhookInbox expectedWebhookInbox = getWebhookInbox();

        MockPaymentProviderWebhookService mockWebhookService = mock(MockPaymentProviderWebhookService.class);

        when(webhookServices.get(expectedWebhookInbox.getPaymentProvider())).thenReturn(mockWebhookService);

        doNothing().when(mockWebhookService).process(expectedWebhookInbox);

        webhookInboxProcessingService.processWebhook(expectedWebhookInbox);

        verify(mockWebhookService).process(expectedWebhookInbox);
    }

    @Test
    public void testProcessWebhookInboxFailed() {
        WebhookInbox expectedWebhookInbox = getWebhookInbox();

        MockPaymentProviderWebhookService mockWebhookService = mock(MockPaymentProviderWebhookService.class);

        when(webhookServices.get(expectedWebhookInbox.getPaymentProvider())).thenReturn(mockWebhookService);

        doThrow(new RuntimeException()).when(mockWebhookService).process(expectedWebhookInbox);

        webhookInboxProcessingService.processWebhook(expectedWebhookInbox);

        ArgumentCaptor<WebhookInbox> captor = ArgumentCaptor.forClass(WebhookInbox.class);
        verify(webhookInboxRepository).save(captor.capture());
        assertEquals(InboxStatus.FAILED, captor.getValue().getStatus());
    }

    @Test
    public void testProcessPending() {
        webhookInboxProcessingService = spy(webhookInboxProcessingService);

        WebhookInbox webhookInbox = getWebhookInbox();
        when(webhookInboxRepository.findByStatus(InboxStatus.PENDING)).thenReturn(List.of(webhookInbox));

        doNothing().when(webhookInboxProcessingService).processWebhook(any(WebhookInbox.class));

        webhookInboxProcessingService.processPending();

        verify(webhookInboxProcessingService).processWebhook(webhookInbox);
    }
}

package com.scube.payment.util;

import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.processing.dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.PayeeDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.storage.model.Payee;
import com.scube.payment.features.payment.storage.model.Payment;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

public class Mocks {
    static UUID mockOrderId = UUID.randomUUID();

    public static Payment createMockPayment() {
        Payment mockPayment = new Payment();

        Payee mockPayee = createMockPayee();

        mockPayment.setOrderId(mockOrderId);
        mockPayment.setTransactionId(UUID.randomUUID().toString());
        mockPayment.setTransactionDate(Instant.now());
        mockPayment.setStatus(PaymentStatus.COMPLETED);
        mockPayment.setPaymentType("mock-type");
        mockPayment.setAmount(new BigDecimal("50.00"));
        mockPayment.setPaymentProvider("mock-provider");
        mockPayment.setReceiptId(UUID.randomUUID());
        mockPayment.setPayee(mockPayee);
        mockPayment.setPaymentNumber("mock-number");
        mockPayment.setCreatedBy("mock-created");
        mockPayment.setPaymentReference("mock-ref");

        return mockPayment;
    }

    @NotNull
    private static Payee createMockPayee() {
        Payee mockPayee = new Payee();

        mockPayee.setFirstName("mock-first");
        mockPayee.setLastName("mock-last");
        mockPayee.setBusinessName("mock-business");
        mockPayee.setEmail("mock-email");
        mockPayee.setPhone("mock-phone");
        mockPayee.setMailingAddress("mock-address");
        mockPayee.setMailingAddress2("mock-address2");
        mockPayee.setMailingCity("mock-city");
        mockPayee.setMailingState("mock-state");
        mockPayee.setMailingZipCode("mock-zip");
        mockPayee.setBillingSameAsMailing(true);
        mockPayee.setBillingAddress("mock-address");
        mockPayee.setBillingAddress2("mock-address2");
        mockPayee.setBillingCity("mock-city");
        mockPayee.setBillingState("mock-state");
        mockPayee.setBillingZipCode("mock-zip");
        return mockPayee;
    }

    public static GetPaymentResponseDto createMockGetPaymentResponseDto() {
        GetPaymentResponseDto mockGetPaymentResponseDto = new GetPaymentResponseDto();

        GetPaymentResponseDto.PayeeDto mockPayeeDto = new GetPaymentResponseDto.PayeeDto();

        mockPayeeDto.setFirstName("mock-first");
        mockPayeeDto.setLastName("mock-last");
        mockPayeeDto.setBusinessName("mock-business");
        mockPayeeDto.setEmail("mock-email");
        mockPayeeDto.setPhone("mock-phone");
        mockPayeeDto.setMailingAddress("mock-address");
        mockPayeeDto.setMailingAddress2("mock-address2");
        mockPayeeDto.setMailingCity("mock-city");
        mockPayeeDto.setMailingState("mock-state");
        mockPayeeDto.setMailingZipCode("mock-zip");
        mockPayeeDto.setBillingSameAsMailing(true);
        mockPayeeDto.setBillingAddress("mock-address");
        mockPayeeDto.setBillingAddress2("mock-address2");
        mockPayeeDto.setBillingCity("mock-city");
        mockPayeeDto.setBillingState("mock-state");
        mockPayeeDto.setBillingZipCode("mock-zip");

        mockGetPaymentResponseDto.setOrderId(mockOrderId);
        mockGetPaymentResponseDto.setTransactionId(UUID.randomUUID().toString());
        mockGetPaymentResponseDto.setTransactionDate(Instant.now());
        mockGetPaymentResponseDto.setStatus("mock-status");
        mockGetPaymentResponseDto.setPaymentType("mock-type");
        mockGetPaymentResponseDto.setAmount(new BigDecimal("50.00"));
        mockGetPaymentResponseDto.setPaymentProvider("mock-provider");
        mockGetPaymentResponseDto.setReceiptId(UUID.randomUUID());
        mockGetPaymentResponseDto.setPayee(mockPayeeDto);
        mockGetPaymentResponseDto.setPaymentNumber("mock-number");
        mockGetPaymentResponseDto.setCreatedBy("mock-created");
        mockGetPaymentResponseDto.setPaymentReference("mock-ref");

        return mockGetPaymentResponseDto;
    }

    public static SubmitPaymentRequestDto createMockSubmitPaymentRequestDto() {
        SubmitPaymentRequestDto mockSubmitPaymentRequestDto = new SubmitPaymentRequestDto();

        mockSubmitPaymentRequestDto.setOrderId(mockOrderId);
        mockSubmitPaymentRequestDto.setOrderAmount(new BigDecimal("100.00"));
        mockSubmitPaymentRequestDto.setPaymentAmount(new BigDecimal("50.00"));
        mockSubmitPaymentRequestDto.setStatus(null);
        mockSubmitPaymentRequestDto.setPaymentProvider("mock-provider");
        mockSubmitPaymentRequestDto.setPaymentType("mock-type");
        mockSubmitPaymentRequestDto.setPaymentReference("mock-ref");
        mockSubmitPaymentRequestDto.setTransactionDate(Instant.now());
        mockSubmitPaymentRequestDto.setPayee(createMockPayeeDto());

        return mockSubmitPaymentRequestDto;
    }

    public static PayeeDto createMockPayeeDto() {
        PayeeDto mockPayeeDto = new PayeeDto();

        mockPayeeDto.setFirstName("mock-first");
        mockPayeeDto.setLastName("mock-last");
        mockPayeeDto.setBusinessName("mock-business");
        mockPayeeDto.setEmail("mock-email");
        mockPayeeDto.setPhone("mock-phone");
        mockPayeeDto.setMailingAddress("mock-address");
        mockPayeeDto.setMailingAddress2("mock-address2");
        mockPayeeDto.setMailingCity("mock-city");
        mockPayeeDto.setMailingState("mock-state");
        mockPayeeDto.setMailingZipCode("mock-zip");
        mockPayeeDto.setBillingSameAsMailing(true);
        mockPayeeDto.setBillingAddress("mock-address");
        mockPayeeDto.setBillingAddress2("mock-address2");
        mockPayeeDto.setBillingCity("mock-city");
        mockPayeeDto.setBillingState("mock-state");
        mockPayeeDto.setBillingZipCode("mock-zip");

        return mockPayeeDto;
    }
}

package com.scube.payment.util;

import com.scube.payment.features.payment.processing.dto.AuditDto;
import com.scube.payment.features.payment.processing.dto.PayeeDto;
import com.scube.payment.features.payment.processing.dto.PaymentDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.mapper.PaymentMapper;
import com.scube.payment.features.payment.storage.model.Payee;
import com.scube.payment.features.payment.storage.model.Payment;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

public class TestPaymentMapperImpl extends PaymentMapper {
    @Override
    public PaymentDto toDto(Payment payment) {
        if (payment == null) {
            return null;
        }

        PaymentDto paymentDto = new PaymentDto();

        paymentDto.setAuditDto(paymentToAuditDto(payment));
        paymentDto.setPaymentUuid(payment.getUuid());
        paymentDto.setId(payment.getId());
        paymentDto.setOrderId(payment.getOrderId());
        paymentDto.setAmount(payment.getAmount());
        paymentDto.setTransactionId(payment.getTransactionId());
        paymentDto.setPaymentNumber(payment.getPaymentNumber());
        paymentDto.setStatus(payment.getStatus());
        paymentDto.setPaymentProvider(payment.getPaymentProvider());
        paymentDto.setPaymentType(payment.getPaymentType());
        paymentDto.setTransactionDate(payment.getTransactionDate());
        paymentDto.setReceiptId(payment.getReceiptId());
        paymentDto.setPaymentReference(payment.getPaymentReference());
        paymentDto.setPayee(payment.getPayee());

        return paymentDto;
    }

    @Override
    public Payment toEntity(PaymentDto paymentDto) {
        if (paymentDto == null) {
            return null;
        }

        Payment payment = new Payment();

        payment.setUuid(paymentDto.getPaymentUuid());
        payment.setCreatedBy(paymentDtoAuditDtoCreatedBy(paymentDto));
        payment.setCreatedDate(paymentDtoAuditDtoCreatedDate(paymentDto));
        payment.setLastModifiedBy(paymentDtoAuditDtoLastModifiedBy(paymentDto));
        payment.setLastModifiedDate(paymentDtoAuditDtoLastModifiedDate(paymentDto));
        payment.setId(paymentDto.getId());
        payment.setOrderId(paymentDto.getOrderId());
        payment.setAmount(paymentDto.getAmount());
        payment.setTransactionId(paymentDto.getTransactionId());
        payment.setPaymentNumber(paymentDto.getPaymentNumber());
        payment.setStatus(paymentDto.getStatus());
        payment.setPaymentProvider(paymentDto.getPaymentProvider());
        payment.setPaymentType(paymentDto.getPaymentType());
        payment.setTransactionDate(paymentDto.getTransactionDate());
        payment.setReceiptId(paymentDto.getReceiptId());
        payment.setPaymentReference(paymentDto.getPaymentReference());
        payment.setPayee(paymentDto.getPayee());

        return payment;
    }

    @Override
    public List<PaymentDto> toDto(List<Payment> payments) {
        if (payments == null) {
            return null;
        }

        List<PaymentDto> list = new ArrayList<PaymentDto>(payments.size());
        for (Payment payment : payments) {
            list.add(toDto(payment));
        }

        return list;
    }

    @Override
    public List<Payment> toEntity(List<PaymentDto> paymentDtos) {
        if (paymentDtos == null) {
            return null;
        }

        List<Payment> list = new ArrayList<Payment>(paymentDtos.size());
        for (PaymentDto paymentDto : paymentDtos) {
            list.add(toEntity(paymentDto));
        }

        return list;
    }

    @Override
    public Payment toEntity(SubmitPaymentRequestDto submitPaymentRequestDto) {
        if (submitPaymentRequestDto == null) {
            return null;
        }

        Payment payment = new Payment();

        payment.setAmount(submitPaymentRequestDto.getPaymentAmount());
        payment.setOrderId(submitPaymentRequestDto.getOrderId());
        payment.setStatus(submitPaymentRequestDto.getStatus());
        payment.setPaymentProvider(submitPaymentRequestDto.getPaymentProvider());
        payment.setPaymentType(submitPaymentRequestDto.getPaymentType());
        payment.setTransactionDate(submitPaymentRequestDto.getTransactionDate());
        payment.setPaymentReference(submitPaymentRequestDto.getPaymentReference());
        payment.setAuthorizedTs(submitPaymentRequestDto.getAuthorizedTs());
        payment.setCapturedTs(submitPaymentRequestDto.getCapturedTs());
        payment.setIsOnlineTransaction(submitPaymentRequestDto.getIsOnlineTransaction());
        payment.setPayee(payeeDtoToPayee(submitPaymentRequestDto.getPayee()));
        payment.setPaymentProvider("");

        return payment;
    }

    protected AuditDto paymentToAuditDto(Payment payment) {
        if (payment == null) {
            return null;
        }

        AuditDto auditDto = new AuditDto();

        auditDto.setCreatedBy(payment.getCreatedBy());
        auditDto.setCreatedDate(payment.getCreatedDate());
        auditDto.setLastModifiedBy(payment.getLastModifiedBy());
        auditDto.setLastModifiedDate(payment.getLastModifiedDate());

        return auditDto;
    }

    private String paymentDtoAuditDtoCreatedBy(PaymentDto paymentDto) {
        AuditDto auditDto = paymentDto.getAuditDto();
        if (auditDto == null) {
            return null;
        }
        return auditDto.getCreatedBy();
    }

    private Instant paymentDtoAuditDtoCreatedDate(PaymentDto paymentDto) {
        AuditDto auditDto = paymentDto.getAuditDto();
        if (auditDto == null) {
            return null;
        }
        return auditDto.getCreatedDate();
    }

    private String paymentDtoAuditDtoLastModifiedBy(PaymentDto paymentDto) {
        AuditDto auditDto = paymentDto.getAuditDto();
        if (auditDto == null) {
            return null;
        }
        return auditDto.getLastModifiedBy();
    }

    private Instant paymentDtoAuditDtoLastModifiedDate(PaymentDto paymentDto) {
        AuditDto auditDto = paymentDto.getAuditDto();
        if (auditDto == null) {
            return null;
        }
        return auditDto.getLastModifiedDate();
    }

    protected Payee payeeDtoToPayee(PayeeDto payeeDto) {
        if (payeeDto == null) {
            return null;
        }

        Payee payee = new Payee();

        payee.setId(payeeDto.getId());
        payee.setFirstName(payeeDto.getFirstName());
        payee.setLastName(payeeDto.getLastName());
        payee.setBusinessName(payeeDto.getBusinessName());
        payee.setEmail(payeeDto.getEmail());
        payee.setPhone(payeeDto.getPhone());
        payee.setMailingAddress(payeeDto.getMailingAddress());
        payee.setMailingAddress2(payeeDto.getMailingAddress2());
        payee.setMailingCity(payeeDto.getMailingCity());
        payee.setMailingState(payeeDto.getMailingState());
        payee.setMailingZipCode(payeeDto.getMailingZipCode());
        payee.setBillingSameAsMailing(payeeDto.isBillingSameAsMailing());
        payee.setBillingAddress(payeeDto.getBillingAddress());
        payee.setBillingAddress2(payeeDto.getBillingAddress2());
        payee.setBillingCity(payeeDto.getBillingCity());
        payee.setBillingState(payeeDto.getBillingState());
        payee.setBillingZipCode(payeeDto.getBillingZipCode());

        return payee;
    }
}

package com.scube.payment.features.providers.authorizedotnet.dto.hosted_payment;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.scube.payment.features.providers.authorizedotnet.dto.config.IAuthorizeDotNetApiRequest;
import com.scube.payment.features.providers.authorizedotnet.dto.shared.MerchantAuthentication;
import com.scube.payment.features.providers.authorizedotnet.dto.transaction.TransactionRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetHostedPaymentPageTokenRequestDto implements IAuthorizeDotNetApiRequest {

    @JsonProperty("getHostedPaymentPageRequest")
    private GetHostedPaymentPageRequest getHostedPaymentPageRequest;

    @JsonPropertyOrder({"merchantAuthentication", "refId", "transactionRequest", "hostedPaymentSettings"})
    public record GetHostedPaymentPageRequest(
            MerchantAuthentication merchantAuthentication,
            String refId,
            TransactionRequest transactionRequest,
            HostedPaymentSettings hostedPaymentSettings
    ) {}
}


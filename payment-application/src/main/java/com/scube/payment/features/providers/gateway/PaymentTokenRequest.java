package com.scube.payment.features.providers.gateway;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.payment.features.payment.processing.dto.IOrderPayment;
import com.scube.payment.features.payment.processing.serialization.BigDecimalTwoDecimalPlacesDeserializer;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentTokenRequest implements IOrderPayment {
    @NotNull
    private UUID orderId;
    @Positive
    @JsonDeserialize(using = BigDecimalTwoDecimalPlacesDeserializer.class)
    private BigDecimal paymentAmount;
    @Positive
    @JsonDeserialize(using = BigDecimalTwoDecimalPlacesDeserializer.class)
    private BigDecimal orderAmount;
    @NotNull
    private OrderInvoiceResponse order;

    public List<OrderInvoiceItem> getItems() {
        return Optional.ofNullable(order)
                .map(OrderInvoiceResponse::getItems)
                .orElseThrow(() -> new IllegalArgumentException("Order items cannot be null"));
    }
}

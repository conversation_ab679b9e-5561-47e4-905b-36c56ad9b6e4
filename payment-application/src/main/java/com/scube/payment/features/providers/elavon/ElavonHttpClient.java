package com.scube.payment.features.providers.elavon;

import com.scube.config_utils.app_property.AppPropertyValue;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;

import java.math.BigDecimal;

@Slf4j
@Service
@RequiredArgsConstructor
@Profile("!test")
public class ElavonHttpClient {
    @AppPropertyValue
    private ElavonProperties elavonProperties;

    private WebClient getWebClient() {
        return WebClient.builder()
                .baseUrl(elavonProperties.getUrl())
                .build();
    }

    public String getTransactionToken(String orderId, BigDecimal amount) {
        var webClient = getWebClient();
        var body = new LinkedMultiValueMap<String, String>();
        body.add("ssl_transaction_type", elavonProperties.getTransactionType());
        body.add("ssl_account_id", elavonProperties.getAccountId());
        body.add("ssl_user_id", elavonProperties.getUserId());
        body.add("ssl_pin", elavonProperties.getPin());
        body.add("ssl_amount", amount.toString());
        body.add("ssl_transaction_currency", elavonProperties.getTransactionCurrency());
        body.add("orderId", orderId);
        return webClient.post()
                .uri("/hosted-payments/transaction_token")
                .header("Content-Type", "application/x-www-form-urlencoded")
                .bodyValue(body)
                .retrieve()
                .bodyToMono(String.class)
                .block();
    }
}
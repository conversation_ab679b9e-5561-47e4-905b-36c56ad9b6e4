package com.scube.payment.features.payment.processing.mapper;

import com.scube.payment.features.payment.processing.dto.PaymentDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.storage.model.Payment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class PaymentMapper {
    @Mapping(target = "paymentUuid", source = "uuid")
    @Mapping(target = "auditDto.createdBy", source = "createdBy")
    @Mapping(target = "auditDto.createdDate", source = "createdDate")
    @Mapping(target = "auditDto.lastModifiedBy", source = "lastModifiedBy")
    @Mapping(target = "auditDto.lastModifiedDate", source = "lastModifiedDate")
    public abstract PaymentDto toDto(Payment payment);

    @Mapping(target = "uuid", source = "paymentUuid")
    @Mapping(target = "createdBy", source = "auditDto.createdBy")
    @Mapping(target = "createdDate", source = "auditDto.createdDate")
    @Mapping(target = "lastModifiedBy", source = "auditDto.lastModifiedBy")
    @Mapping(target = "lastModifiedDate", source = "auditDto.lastModifiedDate")
    public abstract Payment toEntity(PaymentDto paymentDto);

    public abstract List<PaymentDto> toDto(List<Payment> payments);

    public abstract List<Payment> toEntity(List<PaymentDto> paymentDtos);

    @Mapping(target = "amount", source = "paymentAmount")
    public abstract Payment toEntity(SubmitPaymentRequestDto submitPaymentRequestDto);
}

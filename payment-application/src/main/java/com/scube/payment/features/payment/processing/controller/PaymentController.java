package com.scube.payment.features.payment.processing.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.payment.features.payment.processing.dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentResponseDto;
import com.scube.payment.features.payment.processing.service.PaymentProcessingService;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.payment.features.payment.validator.ValidPaymentAmount;
import com.scube.payment.features.permission.Permissions;
import com.scube.payment.features.providers.gateway.PaymentProviderGateway;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.PaymentTokenResponse;
import jakarta.annotation.security.RolesAllowed;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MarkerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.PAYMENT_SERVICE)
@RestController
@Slf4j
@Validated
public class PaymentController {
    private final PaymentStorageService paymentService;
    private final PaymentProviderGateway paymentProviderGateway;
    private final PaymentProcessingService paymentProcessingService;

    @GetMapping("/payments")
    @RolesAllowed(Permissions.Payment.GET_ALL_PAYMENTS)
    public List<Payment> getAllPayments() {
        return paymentService.getPaymentResponseDtos();
    }

    @GetMapping("/order/{orderId}")
    @RolesAllowed(Permissions.Payment.GET_PAYMENTS_BY_ORDER_ID)
    public List<GetPaymentResponseDto> getPaymentsByOrderId(@PathVariable UUID orderId) {
        return paymentService.getPaymentResponseDtos(orderId);
    }

    @PostMapping("/process")
    @RolesAllowed(Permissions.Payment.SUBMIT_PAYMENT)
    public SubmitPaymentResponseDto submitPayment(@ValidPaymentAmount @RequestBody SubmitPaymentRequestDto paymentRequest) {
        return paymentProcessingService.submitPayment(paymentRequest);
    }

    @PostMapping("/payments/token")
    @RolesAllowed(Permissions.Payment.GET_PAYMENT_TOKEN)
    public PaymentTokenResponse getPaymentToken(@ValidPaymentAmount @RequestBody PaymentTokenRequest req) {
        return paymentProviderGateway.getToken(req);
    }

    @DeleteMapping("/payments/{id}")
    @RolesAllowed(Permissions.Payment.DELETE_PAYMENT)
    public void deletePayment(@PathVariable("id") int id) {
        paymentService.deletePayment(id);
    }

    @PostMapping("/test/log")
    @RolesAllowed(Permissions.Payment.TEST_LOG)
    public void testLog() {
        log.error(MarkerFactory.getMarker("CRITICAL"),
                "Failed to process webhook " + UUID.randomUUID(), new Exception("Test exception"));
    }
}

package com.scube.payment.features.providers.authorizedotnet.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.scube.payment.features.providers.authorizedotnet.dto.AuthorizeDotNetWebhookSignature;
import com.scube.payment.features.providers.authorizedotnet.dto.shared.MerchantAuthentication;
import com.scube.payment.features.providers.gateway.IPaymentProviderProperties;
import com.scube.payment.features.providers.gateway.IWebhookSignature;
import lombok.*;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ConfigurationProperties(prefix = "payment-providers.authorize-dot-net")
public class AuthorizeDotNetProperties implements IPaymentProviderProperties {
    public static final String PAYMENT_PROVIDER_NAME = "Authorize.Net";
    private String apiLoginId;
    private String transactionKey;
    private String merchantSignatureKey;

    @NestedConfigurationProperty
    private AuthorizeDotNetPaymentProviderSettings settings;

    public MerchantAuthentication merchantAuthentication() {
        return new MerchantAuthentication(apiLoginId, transactionKey);
    }

    @Override
    public IWebhookSignature webhookSignature() {
        return new AuthorizeDotNetWebhookSignature(merchantSignatureKey);
    }

    public String apiAuthHeader() {
        return "Basic " + Base64.getEncoder().encodeToString((apiLoginId + ":" + transactionKey).getBytes());
    }

    @SneakyThrows
    public Map<String, String> getSettingsAsMap() {
        var objectMapper = Jackson2ObjectMapperBuilder.json().build();
        HashMap<String, Object> properties = objectMapper.convertValue(this.getSettings(), new TypeReference<>() {
        });
        var result = new HashMap<String, String>();
        // convert each property value to json string
        for (Map.Entry<String, Object> entry : properties.entrySet()) {
            result.put(entry.getKey(), objectMapper.writeValueAsString(entry.getValue()));
        }

        return result;
    }
}

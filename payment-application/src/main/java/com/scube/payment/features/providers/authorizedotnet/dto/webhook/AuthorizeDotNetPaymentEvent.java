package com.scube.payment.features.providers.authorizedotnet.dto.webhook;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum AuthorizeDotNetPaymentEvent {
    AUTH_CAPTURE_CREATED("net.authorize.payment.authcapture.created"),
    AUTHORIZATION_CREATED("net.authorize.payment.authorization.created"),
    CAPTURE_CREATED("net.authorize.payment.capture.created"),
    FRAUD_APPROVED("net.authorize.payment.fraud.approved"),
    FRAUD_DECLINED("net.authorize.payment.fraud.declined"),
    FRAUD_HELD("net.authorize.payment.fraud.held"),
    PRIOR_AUTH_CAPTURE_CREATED("net.authorize.payment.priorAuthCapture.created"),
    REFUND_CREATED("net.authorize.payment.refund.created"),
    VOID_CREATED("net.authorize.payment.void.created");

    private final String eventType;

    AuthorizeDotNetPaymentEvent(String eventType) {
        this.eventType = eventType;
    }

    @JsonValue
    public String getEventType() {
        return eventType;
    }

    @Json<PERSON>reator
    public static AuthorizeDotNetPaymentEvent fromString(String eventType) {
        for (AuthorizeDotNetPaymentEvent type : AuthorizeDotNetPaymentEvent.values()) {
            if (type.getEventType().equalsIgnoreCase(eventType)) {
                return type;
            }
        }

        throw new IllegalArgumentException("Unknown event type: " + eventType);
    }
}
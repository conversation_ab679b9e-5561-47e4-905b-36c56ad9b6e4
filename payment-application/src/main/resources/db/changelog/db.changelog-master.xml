<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.4.xsd">

    <!-- @formatter:off -->
    <include file="classpath:db/changelog/scripts/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000001_add_entity_id_and_payee_table.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000002_audit_logging.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000003_change_payment_entity_to_use_transactionid.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000004_audit_tables.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000005_history_tables.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000006_remove_sequence_for_identity.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000007_add_receipt_id_column.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000008_add_payment_number_column.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000009_add_payment_reference_column.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000010_add_scheduler_table_tbl.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000011_update_audit_columns_to_clerk.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000012_payment_providers.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/property_type.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000013_custom_fields_as_json.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000014_add_onlineTransaction_field.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000015_add_providerWebhookId_column.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000016_shedlock.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/revision_entity.xml" relativeToChangelogFile="false"/>
    <include file="classpath:db/changelog/scripts/00000000000017_payment_provider_settings_migrate.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
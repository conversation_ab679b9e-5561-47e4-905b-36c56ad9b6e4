<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1711565300604-62">
        <addColumn tableName="webhook_inbox">
            <column name="provider_webhook_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <addColumn tableName="audit_log_webhook_inbox">
            <column name="provider_webhook_id" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        <createIndex indexName="idx_webhook_inbox_provider_webhook_id" tableName="webhook_inbox">
            <column name="provider_webhook_id"/>
        </createIndex>
        <createIndex indexName="idx_webhook_payment_payment_reference" tableName="payment">
            <column name="payment_reference"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>

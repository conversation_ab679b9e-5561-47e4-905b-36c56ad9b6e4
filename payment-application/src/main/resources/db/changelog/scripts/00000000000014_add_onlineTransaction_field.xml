<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="postgres (generated)" id="1711565300604-59">
        <addColumn tableName="payment">
                <column name="is_online_transaction" type="BOOLEAN"></column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-60">
        <addColumn tableName="audit_log_payment">
            <column name="is_online_transaction" type="BOOLEAN"></column>
        </addColumn>
    </changeSet>
    <changeSet author="postgres (generated)" id="1711565300604-61">
        <sql>
            update payment set payment_provider = 'Authorize.Net',
            payment_provider_id =
            (select payment_provider_id from payment.payment_provider where name = 'Authorize.Net'),
            is_online_transaction = true
            where authorized_ts is not null
        </sql>
    </changeSet>
</databaseChangeLog>

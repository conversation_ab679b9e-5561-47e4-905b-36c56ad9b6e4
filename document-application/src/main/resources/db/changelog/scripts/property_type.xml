<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="property_type_createTable" author="David">
        <createTable tableName="property_type">
            <column autoIncrement="true" name="property_type_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false" unique="true"/>
            </column>
            <column name="property_type_uuid" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="created_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="properties" type="JSONB"/>
            <column name="table_name" type="VARCHAR(100)" defaultOnNull="true" defaultValue="">
                <constraints nullable="false"/>
            </column>
            <column name="property_name" type="VARCHAR(255)" defaultOnNull="true" defaultValue="">
                <constraints nullable="false"/>
            </column>
            <column name="property_type" type="VARCHAR(255)" defaultOnNull="true" defaultValue="OBJECT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="audit_log_property_type_createTable" author="David">
        <createTable tableName="audit_log_property_type">
            <column name="property_type_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revision_id" type="BIGINT">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="revision_type" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="property_type_uuid" type="UUID">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="properties" type="JSONB"/>
            <column name="table_name" type="VARCHAR(100)" defaultOnNull="true" defaultValue="">
                <constraints nullable="false"/>
            </column>
            <column name="property_name" type="VARCHAR(255)" defaultOnNull="true" defaultValue="">
                <constraints nullable="false"/>
            </column>
            <column name="property_type" type="VARCHAR(255)" defaultOnNull="true" defaultValue="OBJECT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
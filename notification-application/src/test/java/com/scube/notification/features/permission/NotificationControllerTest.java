package com.scube.notification.features.permission;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.notification.SharedTestConfig;
import org.junit.jupiter.api.Test;

import static com.scube.notification.permissions.MockMvcHelper.*;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

class NotificationControllerTest extends SharedTestConfig {
    @Test
    @WithJwt(json = START_JSON + Permissions.Notification.GET_ALL + END_JSON)
    void testGetAll_Success() throws Exception {
        var result = performFormDataPost(mockMvc, "/notification");
        assertNotEquals(403, result.getResponse().getStatus());
    }

    @Test
    @WithJwt(json = START_JSON + Permissions.Notification.GET_ALL + END_JSON)
    void testGet_Success() throws Exception {
        var result = performFormDataPost(mockMvc, "/notification");
        assertNotEquals(403, result.getResponse().getStatus());
    }
}
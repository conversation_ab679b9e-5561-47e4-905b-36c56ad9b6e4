package com.scube.notification.features.processing.sms.service;

import com.scube.notification.features.processing.sms.model.SMS;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static com.scube.notification.features.processing.sms.constants.ServiceNames.MOCK_SMS;

@Service(MOCK_SMS)
@Slf4j
@AllArgsConstructor
public class MockSMSService implements ISmsService {
    @Override
    public void send(SMS sms) {
        log.debug("MockSMSService.send()");
        log.info("SUCCESS: {}", sms);
    }
}

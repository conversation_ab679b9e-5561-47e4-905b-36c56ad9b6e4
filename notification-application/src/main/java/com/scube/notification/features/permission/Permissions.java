package com.scube.notification.features.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2024-04-18T01:22:41.730672Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-18T01:22:41.731671600Z
     */
    public static class NotificationStatus {
        public static final String GET_ALL = "notification-service-status-get-all";

        public static final String GET = "notification-service-status-get";

        public static final String CREATE = "notification-service-status-create";

        public static final String UPDATE = "notification-service-status-update";

        public static final String DELETE = "notification-service-status-delete";

        private NotificationStatus() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-18T01:22:41.739151300Z
     */
    public static class AppProperties {
        public static final String GET_ALL_PROPERTIES = "notification-service-app-properties-get-all-properties";

        public static final String GET_CACHE = "notification-service-app-properties-get-cache";

        public static final String GET_PROPERTY_BY_UUID = "notification-service-app-properties-get-property-by-uuid";

        public static final String GET_PROPERTY_BY_NAME = "notification-service-app-properties-get-property-by-name";

        public static final String CREATE_PROPERTY = "notification-service-app-properties-create-property";

        public static final String UPDATE_PROPERTY = "notification-service-app-properties-update-property";

        public static final String DELETE_PROPERTY_BY_UUID = "notification-service-app-properties-delete-property-by-uuid";

        private AppProperties() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-18T01:22:41.740159100Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "notification-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "notification-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-18T01:22:41.740159100Z
     */
    public static class Notification {
        public static final String GET_ALL = "notification-service-notification-get-all";

        public static final String GET = "notification-service-notification-get";

        public static final String CREATE = "notification-service-notification-create";

        public static final String DELETE = "notification-service-notification-delete";

        public static final String CANCEL = "notification-service-notification-cancel";

        private Notification() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-18T01:22:41.740159100Z
     */
    public static class NotificationProcessing {
        public static final String PROCESS_ALL = "notification-service-processing-process-all";

        public static final String PROCESS = "notification-service-processing-process";

        private NotificationProcessing() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-18T01:22:41.741158900Z
     */
    public static class ErrorNotification {
        public static final String SEND_EMAIL = "notification-service-error-send-email";

        private ErrorNotification() {
        }
    }
}

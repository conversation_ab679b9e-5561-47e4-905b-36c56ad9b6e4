package com.scube.config.sql_storage.rabbit.delete_by_uuid;

import com.scube.config.sql_storage.SqlStorageService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class DeleteSqlStorageByUUIDCommandHandler extends FanoutListener<DeleteSqlStorageByUUIDCommand> {
    private final SqlStorageService sqlStorageService;

    @Override
    public void consume(DeleteSqlStorageByUUIDCommand event) {
        sqlStorageService.deleteByUuid(event.uuid());
    }
}
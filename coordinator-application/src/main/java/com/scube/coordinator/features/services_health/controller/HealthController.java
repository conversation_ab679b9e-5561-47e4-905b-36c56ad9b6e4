package com.scube.coordinator.features.services_health.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.permission.Permissions;
import com.scube.coordinator.features.services_health.dto.ServiceHealthDto;
import com.scube.coordinator.features.services_health.service.HealthService;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

@RestController
@RequestMapping("health")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class HealthController {
    private final HealthService healthService;

    @GetMapping
    @RolesAllowed(Permissions.Health.GET_SERVICE_HEALTH)
    public Flux<ServiceHealthDto> getServiceHealth() {
        return healthService.getHealth();
    }
}
package com.scube.coordinator.features.templates.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.documenttemplates.dto.gen_dto.TemplateDto;
import com.scube.report.features.base.dto.gen_dto.ReportTypeDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Template {
    private UUID templateUUID;
    private String name;
    private String nameKey;
    private String filetype;

    private String description;
    private UUID documentUUID;

    @JsonProperty("isReport")
    private boolean isReport;
    private List<Query> queries;
    private JsonNode formData;

    @JsonIgnore
    private Map<String, Object> properties;


    @JsonAnyGetter
    public Map<String, Object> getCustomFields() {
        return properties;
    }

    public Template(@NonNull TemplateDto template, @Nullable ReportTypeDto reportType) {
        this.templateUUID = template.getTemplateUuid();
        this.name = Optional.ofNullable(template.getName())
                .filter(x -> !ObjectUtils.isEmpty(x))
                .filter(x -> Stream.of("null", "undefined", "string").noneMatch(x::equalsIgnoreCase))
                .orElse(template.getNameKey());
        this.nameKey = Optional.ofNullable(template.getNameKey()).orElse("");
        this.filetype = Optional.ofNullable(template.getFiletype()).orElse("none");
        if (!ObjectUtils.isEmpty(reportType)) {
            this.isReport = true;
        }
        this.description = Optional.ofNullable(template.getDescription())
                .filter(x -> !ObjectUtils.isEmpty(x))
                .filter(x -> Stream.of("null", "undefined", "string").noneMatch(x::equalsIgnoreCase))
                .orElse("none");
        this.documentUUID = template.getDocumentUuid();
        this.properties = template.getCustomFields();
        this.queries = Optional.ofNullable(reportType)
                .map(x -> Query.createQueryList(x.getQueries()))
                .orElse(List.of());
        this.formData = Optional.ofNullable(reportType)
                .map(ReportTypeDto::getFormData)
                .orElse(new ObjectMapper().createObjectNode());
    }
}
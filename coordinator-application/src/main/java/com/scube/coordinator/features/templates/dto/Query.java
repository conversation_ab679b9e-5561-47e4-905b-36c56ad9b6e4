package com.scube.coordinator.features.templates.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.config_utils.sql_storage.utils.SqlStorageUtils;
import com.scube.report.features.base.dto.gen_dto.QueryType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Query {
    private String queryKey;
    private String referenceKey;
    private String type;
    private String[] parameters;
    private String sql;

    @JsonIgnore
    public static List<Query> createQueryList(@Nullable Map<String, com.scube.report.features.base.dto.gen_dto.Query> queries) {
        if (ObjectUtils.isEmpty(queries)) return List.of();
        var result = new ArrayList<Query>();
        for (var query : queries.entrySet()) {
            var queryKey = query.getKey();
            var queryValue = query.getValue();
            if (ObjectUtils.isEmpty(queryValue)) continue;
            var newQuery = new Query();
            newQuery.setQueryKey(queryValue.getQueryKey());
            newQuery.setReferenceKey(queryKey);
            newQuery.setType(String.valueOf(queryValue.getQueryType()));
            if (Objects.nonNull(queryValue.getParameters())) {
                newQuery.setParameters(queryValue.getParameters().keySet().toArray(new String[0]));
            }
            if (Objects.nonNull(queryValue.getQueryType()) && queryValue.getQueryType().equals(QueryType.SQL)) {
                var sql = SqlStorageUtils.getByName(queryValue.getQueryKey());
                if (Objects.nonNull(sql)) {
                    newQuery.setSql(sql.getSql());
                }
            }
            result.add(newQuery);
        }
        return result;
    }

    @JsonIgnore
    public static Map<String, com.scube.report.features.base.dto.gen_dto.Query> createQueryMap(@Nullable List<Query> queries) {
        if (ObjectUtils.isEmpty(queries)) return Map.of();
        var result = new HashMap<String, com.scube.report.features.base.dto.gen_dto.Query>();
        for (Query query : queries) {
            if (ObjectUtils.isEmpty(query)) continue;
            var queryValue = new com.scube.report.features.base.dto.gen_dto.Query();
            var referenceKey = query.getReferenceKey();
            if (ObjectUtils.isEmpty(referenceKey))
                throw new IllegalArgumentException("Query key cannot be null or empty");
            queryValue.setQueryKey(query.getQueryKey());
            queryValue.setQueryType(QueryType.valueOf(query.getType()));
            var parameters = new LinkedHashMap<String, Object>();
            if (Objects.nonNull(query.getParameters())) {
                for (String parameter : query.getParameters()) {
                    parameters.put(parameter, null);
                }
            }
            queryValue.setParameters(parameters);
            queryValue.setQueryText(query.getSql());

            result.put(referenceKey, queryValue);
        }
        return result;
    }
}

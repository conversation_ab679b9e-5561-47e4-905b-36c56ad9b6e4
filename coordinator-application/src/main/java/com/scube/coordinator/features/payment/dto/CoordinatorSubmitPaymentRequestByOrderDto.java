package com.scube.coordinator.features.payment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoordinatorSubmitPaymentRequestByOrderDto extends CoordinatorSubmitPaymentRequestDto<CoordinatorSubmitPaymentRequestByOrderDto.PaymentRequestOrderItemDto> {
    @NotNull(message = "Order id is required")
    private UUID orderId;

    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaymentRequestOrderItemDto extends CoordinatorSubmitPaymentRequestDto.PaymentRequestItemDto {
        @NotNull(message = "Order item ID is required")
        @Min(value = 1, message = "Order item ID must be greater than 0")
        private long orderItemId;
    }
}
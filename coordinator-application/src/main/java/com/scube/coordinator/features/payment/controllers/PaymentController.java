package com.scube.coordinator.features.payment.controllers;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.payment.dto.*;
import com.scube.coordinator.features.payment.services.PaymentService;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("payment")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class PaymentController {
    private final PaymentService paymentService;

    @PostMapping("items")
    @RolesAllowed(Permissions.Payment.SUBMIT_PAYMENT_BY_ITEMS)
    public CoordinatorPaymentResponseDto submitPaymentByItems(@Valid @RequestBody CoordinatorSubmitPaymentRequestDto<?> paymentRequest) {
        return paymentService.submitPaymentByItems(paymentRequest);
    }

    @PostMapping("cart")
    @RolesAllowed(Permissions.Payment.SUBMIT_PAYMENT_BY_CART)
    public CoordinatorPaymentResponseDto submitPaymentByCart(@Valid @RequestBody CoordinatorSubmitPaymentRequestByCartDto paymentRequest) {
        return paymentService.submitPaymentByCart(paymentRequest, false);
    }

    @PostMapping("order")
    @RolesAllowed(Permissions.Payment.SUBMIT_PAYMENT_BY_ORDER)
    public CoordinatorPaymentResponseDto submitPaymentByOrder(@Valid @RequestBody CoordinatorSubmitPaymentRequestByOrderDto paymentRequest) {
        return paymentService.submitPaymentByOrder(paymentRequest);
    }

    @PostMapping("processPaymentAndDeleteCartItems")
    @RolesAllowed(Permissions.Payment.SUBMIT_PAYMENT_AND_DELETE_CART_ITEMS)
    public CoordinatorPaymentResponseDto submitPaymentAndDeleteCartItems(@Valid @RequestBody CoordinatorSubmitPaymentRequestByCartDto paymentRequest) {
        return paymentService.submitPaymentByCart(paymentRequest, false);
    }

    @GetMapping("/{orderId}/receipts")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Payment.GET_RECEIPTS)
    public GetOrderReceiptsResponseDto getReceipts(@PathVariable UUID orderId) {
        return paymentService.getReceipts(orderId, false);
    }

    @PostMapping("/{orderId}/generate-receipt")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Payment.GET_RECEIPTS)
    public void generateReceipt(@PathVariable UUID orderId) {
        paymentService.generateReceipt(orderId, false);
    }
}
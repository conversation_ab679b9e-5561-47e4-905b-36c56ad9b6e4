package com.scube.coordinator.features.text_extractor.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "application.text-extractor.enabled", havingValue = "false")
public class MockTextExtractorService implements ITextExtractorService {
    private final ObjectMapper objectMapper;

    @Override
    public JsonNode processFile(final MultipartFile file,
                                final String fileIdentifier,
                                final String schema,
                                final boolean isMe) {
        return objectMapper.createObjectNode();
    }

    @Override
    public String merge(final MultiValueMap<String, MultipartFile> pairs,
                        final String schema,
                        final String promptKey,
                        final boolean isMe) {
        return objectMapper.createObjectNode().toString();
    }
}

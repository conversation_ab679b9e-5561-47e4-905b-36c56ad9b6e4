package com.scube.coordinator.features.cart;

import com.scube.calculation.dto.gen_dto.AddItemRequest;
import com.scube.calculation.dto.gen_dto.CartInvoiceResponse;
import com.scube.calculation.dto.gen_dto.CartItemSummary;
import com.scube.calculation.dto.gen_dto.FeeRequest;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.calculation.generated.RemoveCartItemBy_Cart_QueryParams;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.cart.dto.AddItemToCartRequest;
import com.scube.coordinator.features.cart.dto.AddItemToCartResponse;
import com.scube.coordinator.features.cart.dto.InferPayeeResponseDto;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestByCartDto;
import com.scube.coordinator.rabbit.license.service.LicenseRabbitService;
import com.scube.licensing.features.entity_fee.dtos.gen_dto.EntityFeeDto;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
@AllArgsConstructor
public class CartService {
    private final CalculationServiceConnection calculationServiceConnection;
    private final LicenseServiceConnection licenseServiceConnection;
    private final AmqpGateway amqpGateway;
    private final LicenseRabbitService licenseRabbitService;

    public AddItemToCartResponse addToCart(UUID cartId, AddItemToCartRequest addItemToCartRequest, boolean isMe) {
        return addToCart(cartId, addItemToCartRequest, false, isMe);
    }

    public AddItemToCartResponse addToCart(UUID cartId, AddItemToCartRequest addItemToCartRequest, boolean isOrder, boolean isMe) {
        AddItemRequest addItemRequest;
        List<GetUnPaidLicenseFeesQueryResponseDetail> licenseFees = null;
        List<EntityFeeDto> entityFees = null;
        addItemRequest = switch (addItemToCartRequest.getItemType().toLowerCase()) {
            case "license" -> {
                licenseFees = amqpGateway.queryResult(new GetUnPaidLicenseFeesQuery(addItemToCartRequest.getItemId()))
                        .orElseThrow().items;
                entityFees = amqpGateway.queryResult(new GetUnPaidEntityFeesQuery("license", addItemToCartRequest.getItemId()))
                        .orElseThrow().fees;
                yield addDogLicense(addItemToCartRequest, licenseFees, entityFees);
            }
            case "participant", "individual", "dog", "business", "address", "document" -> {
                entityFees = amqpGateway.queryResult(new GetUnPaidEntityFeesQuery(addItemToCartRequest.getItemType(), addItemToCartRequest.getItemId()))
                        .orElseThrow().fees;
                yield addFee(addItemToCartRequest, entityFees);
            }
            case "tag" -> addTag(addItemToCartRequest);
            case "fee", "entityfee" -> {
                if (isMe) {
                    entityFees = List.of(licenseServiceConnection.loggedInUserEntityFee().getFee(addItemToCartRequest.getItemId()));
                } else {
                    entityFees = List.of(licenseServiceConnection.entityFee().getFee(addItemToCartRequest.getItemId()));
                }
                yield addFee(addItemToCartRequest, entityFees);
            }
            case "entitygroup" -> {
                entityFees = amqpGateway.queryResult(new GetUnPaidEntityFeesQuery("entityGroup", addItemToCartRequest.getItemId()))
                        .orElseThrow().fees;
                yield addFee(addItemToCartRequest, entityFees);
            }
            default ->
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid item type " + addItemToCartRequest.getItemType());
        };

        CartItemSummary addItemResponse = null;
        if (isMe) {
            addItemResponse = calculationServiceConnection.loggedInUserCart().addItem(cartId, addItemRequest);
        } else {
            addItemResponse = calculationServiceConnection.cart().addItem(cartId, addItemRequest);
        }

        if (isOrder && !ObjectUtils.isEmpty(licenseFees)) {
            updateLicenseFeesWithOrderId(cartId, licenseFees);
        }
        if (isOrder && !ObjectUtils.isEmpty(entityFees)) {
            updateEntityFeesWithOrderId(cartId, entityFees);
        }

        return new AddItemToCartResponse(cartId, String.valueOf(addItemResponse.getId()));
    }

    private void updateLicenseFeesWithOrderId(UUID cartId, List<GetUnPaidLicenseFeesQueryResponseDetail> licenseFees) {
        var feeIds = licenseFees.stream().flatMap(x -> x.fees.stream()).map(x -> x.licenseActivityFeeId).toList();
        var updateResult = amqpGateway.queryResult(new UpdateLicenseFeesWithOrderCommand(feeIds, cartId));
        if (updateResult == null || !updateResult.isSuccess())
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error updating fees with order id " + cartId + "." + (updateResult != null ? " error: " + updateResult.getErrorMessage() : ""));
    }

    private void updateEntityFeesWithOrderId(UUID cartId, List<EntityFeeDto> entityFeeDtos) {
        var updateResult = amqpGateway.queryResult(new UpdateEntityFeesWithOrderCommand(entityFeeDtos, cartId));
        if (updateResult == null || updateResult.isFailure())
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error updating fees with order id " + cartId + "." + (updateResult != null ? " error: " + updateResult.getErrorMessage() : ""));
    }

    private AddItemRequest addTag(AddItemToCartRequest addItemToCartRequest) {
        var air = new AddItemRequest();
        air.setItemId(addItemToCartRequest.getItemId());
        air.setItemType(addItemToCartRequest.getItemType());
        air.setName("Dog Tag");
        air.setFees(new ArrayList<>(List.of(
                new FeeRequest("STORE-M-DOGTAG", null, Map.of(
                        "tagId", addItemToCartRequest.getItemId()
                ))
        )));
        return air;
    }

    private AddItemRequest addFee(AddItemToCartRequest addItemToCartRequest, List<EntityFeeDto> entityFees) {
        var name = "";
        if (entityFees.size() == 1) {
            name = entityFees.getFirst().getFeeName();
        } else {
            name = addItemToCartRequest.getItemType() + " Fees";
        }
        var description = name;

        var feeRequests = new ArrayList<FeeRequest>();
        for (EntityFeeDto code : entityFees) {
            feeRequests.add(new FeeRequest(code.getFeeCode(), code.getFeeAmount(), Map.of(
                    "entityFeeId", code.getEntityId()
            )));
        }

        var air = new AddItemRequest();
        air.setItemId(addItemToCartRequest.getItemId());
        air.setItemType(addItemToCartRequest.getItemType());
        air.setName(name);
        air.setDescription(description);
        air.setFees(feeRequests);
        air.setProperties(Map.of("entityFeeIds",
                entityFees.stream().map(EntityFeeDto::getEntityId).toList()));
        return air;
    }

    private AddItemRequest addDogLicense(AddItemToCartRequest addItemToCartRequest, List<GetUnPaidLicenseFeesQueryResponseDetail> licenseFees, List<EntityFeeDto> entityFees) {
        GetLicenseOrderItemInfoQueryResponse data = amqpGateway.queryResult(new GetLicenseOrderItemInfoQuery(addItemToCartRequest.getItemId())).orElseThrow();
        if (data == null)
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error getting license order item info for license entity id " + addItemToCartRequest.getItemId());

        var feeRequests = new ArrayList<FeeRequest>();
        for (GetUnPaidLicenseFeesQueryResponseDetail code : licenseFees) {
            for (var lfee : code.fees) {
                feeRequests.add(new FeeRequest(lfee.feeCode(), lfee.amount(), Map.of(
                        "licenseActivityFeeId", lfee.licenseActivityFeeId,
                        "licenseActivityFeeUUID", lfee.feeUUID,
                        "licenseActivityId", code.getLicenseActivityId()
                )));
            }
        }
        for (var code : entityFees) {
            feeRequests.add(new FeeRequest(code.getFeeCode(), code.getFeeAmount(), Map.of(
                    "entityFeeId", code.getEntityId()
            )));
        }

        var addItemRequest = new AddItemRequest();
        addItemRequest.setItemId(addItemToCartRequest.getItemId());
        addItemRequest.setItemType(addItemToCartRequest.getItemType());
        addItemRequest.setName(data.name);
        addItemRequest.setDescription(data.description);
        addItemRequest.setFees(feeRequests);
        addItemRequest.setProperties(Map.of(
                "licenseActivityFeeIds", licenseFees.stream().flatMap(x -> x.fees.stream()).map(x -> x.licenseActivityFeeId).toList(),
                "licenseActivities", licenseFees.stream()
                        .map(x -> Map.of(
                                "licenseActivityId", x.licenseActivityId,
                                "validFromDate", x.validFromDate,
                                "validToDate", x.validToDate
                        ))
                        .toList()
        ));
        return addItemRequest;
    }

    /**
     * Infer payee from cart by looking through the cart for a license
     * and then getting the license holder contact info and address from license service
     *
     * @param cartId
     * @return
     */
    public InferPayeeResponseDto inferPayee(UUID cartId, boolean isMe) {
        CartInvoiceResponse cart = null;
        if (isMe) {
            cart = calculationServiceConnection.loggedInUserCart().getCartInvoice(cartId);
        } else {
            cart = calculationServiceConnection.cart().getCartInvoice(cartId);
        }
        var licenseEntityId = cart.getItems()
                .stream()
                .filter(x -> x.getItemType().equals("license"))
                .findFirst()
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.BAD_REQUEST, "No license found in cart " + cartId))
                .getItemId();

        if (licenseEntityId == null)
            return new InferPayeeResponseDto();

        var licenseData = licenseRabbitService.getLicense(licenseEntityId);

        return InferPayeeResponseDto.of(licenseData);
    }

    public void removeItemsFromCart(CoordinatorSubmitPaymentRequestByCartDto paymentRequest) {
        var items = new RemoveCartItemBy_Cart_QueryParams()
                .cartItemId(paymentRequest.getItems().stream()
                        .map(CoordinatorSubmitPaymentRequestByCartDto.PaymentRequestCartItemDto::getCartItemId)
                        .toList());

        calculationServiceConnection.cart().removeCartItemBy(paymentRequest.getCartId(), items);
    }

    public void removeOrderItemsFromCart(UUID cartId, OrderInvoiceResponse order) {
        var items = new RemoveCartItemBy_Cart_QueryParams()
                .cartItemId(order.getItems().stream()
                        .map(OrderInvoiceItem::getOrderItemId)
                        .toList());

        calculationServiceConnection.cart().removeCartItemBy(cartId, items);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GetUnPaidLicenseFeesQueryResponseDetail {
        private UUID licenseActivityId;
        private Instant validFromDate;
        private Instant validToDate;
        private List<GetUnPaidLicenseFeesQueryLicenseFeeResponseDetail> fees = new ArrayList<>();
    }


    // @formatter:off
    public record GetUnPaidLicenseFeesQuery(UUID licenseEntityId) implements IRabbitFanoutPublisherRpc<GetUnPaidLicenseFeesQueryResponse> {}
    public record GetUnPaidLicenseFeesQueryResponse(List<GetUnPaidLicenseFeesQueryResponseDetail> items) {}
    public record GetUnPaidLicenseFeesQueryLicenseFeeResponseDetail(Long licenseActivityFeeId, UUID feeUUID, String feeCode, BigDecimal amount) { }
    public record UpdateLicenseFeesWithOrderCommand(List<Long> feeIds, UUID orderId) implements IRabbitFanoutPublisherRpc<Boolean> {}
    public record GetLicenseOrderItemInfoQuery(UUID entityId) implements IRabbitFanoutPublisherRpc<GetLicenseOrderItemInfoQueryResponse> {}
    public record GetLicenseOrderItemInfoQueryResponse(String name, String description) {}
    // entity fees
    public record UpdateEntityFeesWithOrderCommand(List<EntityFeeDto> entityFeeDtos, UUID orderId) implements IRabbitFanoutPublisherRpc<Boolean> {}
    public record GetUnPaidEntityFeesQuery(String entityType, UUID entityId) implements IRabbitFanoutPublisherRpc<GetUnPaidEntityFeesQueryResponse> { }
    public record GetUnPaidEntityFeesQueryResponse(List<EntityFeeDto> fees) { }
}
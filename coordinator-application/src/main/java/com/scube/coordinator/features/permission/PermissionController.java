package com.scube.coordinator.features.permission;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.ai.generated.AiServiceConnection;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.client.auth.generated.AuthServiceConnection;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.config.generated.ConfigServiceConnection;
import com.scube.client.document.generated.DocumentServiceConnection;
import com.scube.client.documentTemplate.generated.DocumentTemplateServiceConnection;
import com.scube.client.imageProcessing.generated.ImageProcessingServiceConnection;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.client.notification.generated.NotificationServiceConnection;
import com.scube.client.payment.generated.PaymentServiceConnection;
import com.scube.client.report.generated.ReportServiceConnection;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

@RestController
@RequestMapping("/permissions")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
@Validated
public class PermissionController {
    private final PermissionService permissionService;
    private final AiServiceConnection ai;
    private final AuthServiceConnection auth;
    private final CalculationServiceConnection calculation;
    private final ConfigServiceConnection config;
    private final DocumentServiceConnection document;
    private final DocumentTemplateServiceConnection documentTemplate;
    private final ImageProcessingServiceConnection imageProcessing;
    private final LicenseServiceConnection license;
    private final NotificationServiceConnection notification;
    private final PaymentServiceConnection payment;
    private final ReportServiceConnection report;

    @PostMapping("all-services")
    @Operation(summary = "Seed role to all realms")
    @RolesAllowed(Permissions.Permission.SEED_ROLES_TO_ALL_REALMS_FOR_EACH_MICROSERVICE)
    public Mono<List<String>> seedRolesToAllRealmsForEachMicroservice() {
        List<String> failedServices = new ArrayList<>();
        permissionService.addRoleToRealm();

        Map<String, Supplier<Mono<Void>>> services = new HashMap<>();
        services.put("ai", () -> ai.permission().seedRolesToAllRealmsAsync());
        services.put("auth", () -> auth.permission().seedRolesToAllRealmsAsync());
        services.put("calculation", () -> calculation.permission().seedRolesToAllRealmsAsync());
        services.put("config", () -> config.permission().seedRolesToAllRealmsAsync());
        services.put("document", () -> document.permission().seedRolesToAllRealmsAsync());
        services.put("documentTemplate", () -> documentTemplate.permission().seedRolesToAllRealmsAsync());
        services.put("imageProcessing", () -> imageProcessing.permission().seedRolesToAllRealmsAsync());
        services.put("license", () -> license.permission().seedRolesToAllRealmsAsync());
        services.put("notification", () -> notification.permission().seedRolesToAllRealmsAsync());
        services.put("payment", () -> payment.permission().seedRolesToAllRealmsAsync());
        services.put("report", () -> report.permission().seedRolesToAllRealmsAsync());

        return Flux.fromIterable(services.entrySet())
                .flatMap(entry -> entry.getValue().get().onErrorResume(e -> {
                    failedServices.add(entry.getKey());
                    return Mono.empty();
                }))
                .then(Mono.just(failedServices));
    }


    @PostMapping
    @Operation(summary = "Seed role to all realms")
    @RolesAllowed(Permissions.Permission.SEED_ROLES_TO_ALL_REALMS)
    public void seedRolesToAllRealms() {
        permissionService.addRoleToRealm();
    }

    @PostMapping("/{realmName}")
    @Operation(summary = "Seed role to a specific realm")
    @RolesAllowed(Permissions.Permission.SEED_ROLES_BY_REALM)
    public void seedRolesByRealm(@PathVariable @Size(max = 255) String realmName) {
        permissionService.addRoleToRealm(realmName);
    }
}

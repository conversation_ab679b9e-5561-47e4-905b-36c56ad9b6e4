package com.scube.coordinator.features.templates.client;

import com.scube.documenttemplates.dto.gen_dto.TemplateDto;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PatchExchange;
import org.springframework.web.service.annotation.PostExchange;

public interface IDocumentTemplateHttpExchange {
    @PostExchange(
            value = "/templates",
            contentType = "multipart/form-data"
    )
    TemplateDto createTemplate(@RequestBody LinkedMultiValueMap<String, Object> fields);

    @PatchExchange(
            value = "/templates/{templateNameKey}",
            contentType = "multipart/form-data"
    )
    TemplateDto updateTemplate(@PathVariable String templateNameKey, @RequestBody LinkedMultiValueMap<String, Object> fields);
}
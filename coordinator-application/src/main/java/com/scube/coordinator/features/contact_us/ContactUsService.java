package com.scube.coordinator.features.contact_us;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.config_utils.app_property.AppPropertyValue;
import com.scube.coordinator.features.notification.GenerateTextCommand;
import com.scube.coordinator.features.notification.TenantDto;
import com.scube.coordinator.features.notification.TenantService;
import com.scube.coordinator.features.participant.ParticipantService;
import com.scube.notification.client.model.Email;
import com.scube.notification.client.rabbit.ScheduleEmailFanoutEvent;
import com.scube.rabbit.core.AmqpGateway;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.owasp.encoder.Encode;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ContactUsService {
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;
    private final TenantService tenantService;
    private final ParticipantService participantService;

    @AppPropertyValue
    private ContactUsProperties contactUsProperties;

    public void contactUs(MyOpenIdClaimSet claimSet, ContactUsRequest request) {
        var tenantDto = tenantService.getTenant();
        JsonNode unFormattedDataString = objectMapper.valueToTree(new ContactUsEmailBodyData(request, claimSet, tenantDto, participantService));
        if (ObjectUtils.isEmpty(unFormattedDataString))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to convert request to json");

        var emailTemplateName = "contactUsEmailBody";
        var emailSubjectTemplateName = "contactUsEmailSubject";

        var emailBody = amqpGateway.queryResult(new GenerateTextCommand(emailTemplateName, unFormattedDataString)).orElseThrow();
        var emailSubject = amqpGateway.queryResult(new GenerateTextCommand(emailSubjectTemplateName, unFormattedDataString)).orElseThrow();

        amqpGateway.publish(
                ScheduleEmailFanoutEvent.builder()
                        .tag("ContactUs")
                        .topic("email")
                        .createdBy("CoordinatorService")
                        .email(
                                Email.builder()
                                        .to(contactUsProperties.getTo())
                                        .from("<EMAIL>")
                                        .cc(contactUsProperties.isCcResident() ? claimSet.getEmail() : null)
                                        .bcc(contactUsProperties.getBcc())
                                        .subject(emailSubject)
                                        .body(emailBody)
                                        .contentType("text/html")
                                        .build()
                        ).build()
        );
    }

    @Data
    public static class ContactUsEmailBodyData {
        private final String issue;
        private final String description;
        private final String residentEntityId;
        private final String fullName;
        private final String firstName;
        private final String lastName;
        private final String email;
        private final String clerkXpressUrl;
        private final String residentCode;

        public ContactUsEmailBodyData(ContactUsRequest request, MyOpenIdClaimSet claimSet, TenantDto tenantDto, ParticipantService participantService) {
            this.issue = Encode.forHtml(request.getIssue());
            this.description = Encode.forHtml(request.getDescription());
            this.residentEntityId = claimSet.getSubject();
            this.email = claimSet.getEmail();
            this.fullName = claimSet.getFullName();
            this.firstName = claimSet.getGivenName();
            this.lastName = claimSet.getFamilyName();
            this.clerkXpressUrl = tenantDto.getClerkXpressUrl();
            residentCode = participantService.getResidentCode(UUID.fromString(claimSet.getSubject()));
        }
    }
}
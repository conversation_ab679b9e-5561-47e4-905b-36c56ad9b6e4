package com.scube.coordinator.features.templates.dto;

import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class UpsertTemplateRequest {
    private UUID templateUUID;

    private String nameKey;

    @NotBlank
    private String name;

    private String description;
    private UUID documentUUID;

    @JsonProperty("isReport")
    private boolean isReport;
    private JsonNode formData;
    private List<Query> queries;
    private String sectionName;

    private MultipartFile file;

    @JsonAnySetter
    private Map<String, String> properties = new HashMap<>();

    @JsonIgnore
    private static List<String> excludeFields = List.of(
            Fields.templateUUID, Fields.name, Fields.isReport, Fields.description,
            Fields.documentUUID, Fields.queries, Fields.file, Fields.formData
    );

    public LinkedMultiValueMap<String, Object> toCreateTemplateRequest() {
        LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        if (this.file != null)
            map.add("file", file.getResource());
        map.add("name", name);
        map.add("nameKey", Optional.ofNullable(nameKey).orElse(name.replaceAll("\\s+", "_").toLowerCase()));
        //add all from properties
        for (Map.Entry<String, String> entry : properties.entrySet()) {
            if (excludeFields.contains(entry.getKey())) continue;
            map.add(entry.getKey(), entry.getValue());
        }
        return map;
    }

    public LinkedMultiValueMap<String, Object> toUpdateTemplateRequest() {
        LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        if (this.file != null)
            map.add("file", file.getResource());
        map.add("name", name);
        //add all from properties
        for (Map.Entry<String, String> entry : properties.entrySet()) {
            if (excludeFields.contains(entry.getKey())) continue;
            map.add(entry.getKey(), entry.getValue());
        }
        return map;
    }
}
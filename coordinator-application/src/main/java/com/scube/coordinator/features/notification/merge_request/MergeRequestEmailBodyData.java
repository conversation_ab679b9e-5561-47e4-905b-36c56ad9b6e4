package com.scube.coordinator.features.notification.merge_request;

import com.fasterxml.jackson.databind.JsonNode;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

@Data
@NoArgsConstructor
public class MergeRequestEmailBodyData {
    private String residentEntityId;
    private String residentEmail;
    private String residentCode;
    private String firstName;
    private String lastName;
    private String searchBy;
    private String searchValue;
    private String reason;
    private String deniedComment;
    private String clerkXpressUrl;
    private String clerkEmail;
    private String clerkPhoneNumber;
    private String clerkName;
    private String clerkTitle;
    private String clerkOfficeName;

    public MergeRequestEmailBodyData(JsonNode data, MergeRequestRejectedEvent event) {
        this.searchBy = event.getSearchBy();
        this.searchValue = event.getSearchValue();
        this.reason = event.getReason();
        this.deniedComment = event.getDeniedComment();
        buildData(data);
    }

    public MergeRequestEmailBodyData(JsonNode data, MergeRequestApprovedEvent event) {
        this.searchBy = event.getSearchBy();
        this.searchValue = event.getSearchValue();
        buildData(data);
    }

    private void buildData(JsonNode data) {
        var individual = data.get("individual");
        if (!ObjectUtils.isEmpty(individual)) {
            this.residentEntityId = individual.get("entityId").asText();
            this.firstName = individual.get("firstName").asText();
            this.lastName = individual.get("lastName").asText();
            this.residentEmail = individual.get("primaryEmail").asText();
        }
        this.residentCode = data.get("residentCode").asText();
        var tenant = data.get("tenant");
        if (!ObjectUtils.isEmpty(tenant)) {
            this.clerkXpressUrl = tenant.get("clerkXpressUrl").asText();
            this.clerkEmail = tenant.get("clerkEmail").asText();
            this.clerkPhoneNumber = tenant.get("clerkPhoneNumber").asText();
            this.clerkName = tenant.get("clerkName").asText();
            this.clerkTitle = tenant.get("clerkTitle").asText();
            this.clerkOfficeName = tenant.get("cityClerkOfficeName").asText();
        }
    }
}
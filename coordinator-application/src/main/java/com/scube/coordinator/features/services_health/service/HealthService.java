package com.scube.coordinator.features.services_health.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.client.ai.generated.AiServiceConnection;
import com.scube.client.auth.generated.AuthServiceConnection;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.config.generated.ConfigServiceConnection;
import com.scube.client.document.generated.DocumentServiceConnection;
import com.scube.client.documentTemplate.generated.DocumentTemplateServiceConnection;
import com.scube.client.documentTemplateHelper.generated.DocumentTemplateHelperServiceConnection;
import com.scube.client.imageProcessing.generated.ImageProcessingServiceConnection;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.client.notification.generated.NotificationServiceConnection;
import com.scube.client.ocr.generated.OcrServiceConnection;
import com.scube.client.payment.generated.PaymentServiceConnection;
import com.scube.client.report.generated.ReportServiceConnection;
import com.scube.coordinator.features.services_health.dto.ServiceHealthDto;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Supplier;

@Service
@Slf4j(topic = "HealthService")
@RequiredArgsConstructor
public class HealthService {
    private final AiServiceConnection ai;
    private final AuthServiceConnection auth;
    private final CalculationServiceConnection calculation;
    private final ConfigServiceConnection config;
    private final DocumentServiceConnection document;
    private final DocumentTemplateHelperServiceConnection documentTemplateHelper;
    private final DocumentTemplateServiceConnection documentTemplate;
    private final ImageProcessingServiceConnection imageProcessing;
    private final LicenseServiceConnection license;
    private final NotificationServiceConnection notification;
    private final OcrServiceConnection ocr;
    private final PaymentServiceConnection payment;
    private final ReportServiceConnection report;

    private final ObjectMapper objectMapper;

    public Flux<ServiceHealthDto> getHealth() {
        return Flux.fromIterable(List.of(
                tc(ai.actuator()::healthAsync, "AI Service", "AI Service description"),
                tc(auth.actuator()::healthAsync, "Auth Service", "Auth Service description"),
                tc(calculation.actuator()::healthAsync, "Calculation Service", "Calculation Service description"),
                tc(config.actuator()::healthAsync, "Config Service", "Config Service description"),
                tc(document.actuator()::healthAsync, "Document Storage Service", "Document Service description"),
                tc(documentTemplateHelper.actuator()::healthAsync, "Document Template Helper Service", "Document Template Helper Service description"),
                tc(documentTemplate.actuator()::healthAsync, "Document Template Service", "Document Template Service description"),
                tc(imageProcessing.actuator()::healthAsync, "Image Processing Service", "Image Processing Service description"),
                tc(license.actuator()::healthAsync, "License Service", "License Service description"),
                tc(notification.actuator()::healthAsync, "Notification Service", "Notification Service description"),
                //tc(ocr.actuator()::healthAsync, "OCR Service", "OCR Service description"),
                tc(payment.actuator()::healthAsync, "Payment Service", "Payment Service description"),
                tc(report.actuator()::healthAsync, "Report Service", "Report Service description")
        )).flatMap(x -> x);
    }

    private Mono<ServiceHealthDto> tc(Supplier<Mono<?>> healthSupplier, String serviceName, String serviceDescription) {
        var startTimestamp = System.currentTimeMillis();
        return healthSupplier.get()
                .map(o -> {
                    long duration = System.currentTimeMillis() - startTimestamp;
                    log.debug("Health of service {} is {} - Time taken: {} ms", serviceName, o, duration);
                    var actuatorHealth = objectMapper.convertValue(o, ActuatorHealth.class);
                    return new ServiceHealthDto(actuatorHealth.getStatus(), serviceName, serviceDescription);
                })
                .doOnError(e -> {
                    long duration = System.currentTimeMillis() - startTimestamp;
                    log.error("Error checking health of service {} - Time taken: {} ms", serviceName, duration, e);
                })
                .onErrorResume(e -> Mono.just(new ServiceHealthDto("DOWN", serviceName, serviceDescription)));
    }

    @Data
    static class ActuatorHealth {
        private String status;
    }
}

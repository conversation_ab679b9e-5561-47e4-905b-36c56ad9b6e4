package com.scube.coordinator.features.entity_fee;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.entity_fee.client.AddFileRequest;
import com.scube.coordinator.features.entity_fee.dtos.AddFeeToCartRequest;
import com.scube.coordinator.features.entity_fee.dtos.CreateFeeRequest;
import com.scube.coordinator.features.entity_fee.dtos.UpdateFeeRequest;
import com.scube.coordinator.features.entity_group.client.IEntityGroupExchange;
import com.scube.coordinator.features.permission.Permissions;
import com.scube.licensing.features.document.dto.gen_dto.DocumentDto;
import com.scube.licensing.features.document.dto.gen_dto.DocumentTypeDto;
import com.scube.licensing.features.entity_fee.dtos.gen_dto.EntityFeeDto;
import com.scube.licensing.features.entity_group.dtos.gen_dto.EntityGroupDto;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

@RestController
@RequestMapping("entity-fee")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
@Validated
public class EntityFeeController {
    private final LicenseServiceConnection licenseServiceConnection;
    private final IEntityGroupExchange entityGroupExchange;
    private final CalculationServiceConnection calculationServiceConnection;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.EntityFee.CREATE_FEE)
    public Map<String, Object> createFee(@RequestParam("json") CreateFeeRequest request, @RequestParam Map<String, MultipartFile> files) {
        Map<String, Object> result = new HashMap<>();
        EntityGroupDto entityGroupResult = null;
        CreateFeeRequest.Association groupAssociation = null;
        var feesResult = new ArrayList<EntityFeeDto>();
        var documentResult = new ArrayList<DocumentDto>();
        result.put("fees", feesResult);
        result.put("documents", documentResult);

        if (request.hasGroupName()) {
            var entityGroup = request.getEntityGroupMap();
            entityGroupResult = licenseServiceConnection.entityGroup().createEntityGroupWithBodyAsObject(entityGroup);
            if (!ObjectUtils.isEmpty(entityGroupResult)) {
                groupAssociation = CreateFeeRequest.Association.createEntityGroupAssociation(entityGroupResult.getEntityId());
                result.put("entityGroup", entityGroupResult);
            }
            if (!ObjectUtils.isEmpty(files)) {
                // create the file keys as document types if not exists (api will handle it)
                var documentRequest = files.keySet().stream()
                        .map(x -> new DocumentTypeDto(x, x,null))
                        .toList();
                licenseServiceConnection.document().saveDocumentType(documentRequest);


                var addFileRequest = new AddFileRequest();
                addFileRequest.putFiles(files);
                List<DocumentDto> documents = entityGroupExchange.addFile(entityGroupResult.getEntityId(), addFileRequest);
                documentResult.addAll(documents);
            }
        }

        for (CreateFeeRequest.FeeRequest fee : Optional.ofNullable(request.getFees()).orElse(Set.of())) {
            if (!ObjectUtils.isEmpty(groupAssociation)) {
                fee.addAssociation(groupAssociation);
            }
            EntityFeeDto entityFee = licenseServiceConnection.entityFee().addFeeWithBodyAsObject(fee);
            feesResult.add(entityFee);
        }

        return result;
    }


    @DeleteMapping("{entityId}/{entityType}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.EntityFee.DELETE_FEE)
    public void deleteFee(@PathVariable @NotNull  UUID entityId, @PathVariable @NotBlank String entityType) {
        switch (entityType) {
            case "entityGroup" -> {
                calculationServiceConnection.cart().removeCartItemByItemId(entityId);
                licenseServiceConnection.entityGroup().deleteGroup(entityId);
            }
            case "entityFee" -> {
                calculationServiceConnection.cart().removeCartItemFeeByEntityFeeId(entityId);
                licenseServiceConnection.entityFee().deleteFee(entityId);
            }
            default -> throw new IllegalArgumentException("Invalid entity type: " + entityType);
        }
    }

    @PutMapping(value = "{entityId}/{entityType}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.EntityFee.UPDATE_FEE)
    public EntityFeeDto updateFee(
            @PathVariable @NotNull  UUID entityId,
            @PathVariable @NotBlank String entityType,
            @RequestBody UpdateFeeRequest request) {
        switch (entityType) {
            case "entityFee" -> {
                EntityFeeDto updatedFee = licenseServiceConnection.entityFee().updateFeeWithBodyAsObject(entityId,request);
                calculationServiceConnection.cart().updateFeeOnCartItemByItemIdWithBodyAsObject(entityId, new AddFeeToCartRequest(request));
                return updatedFee;
            }
            default -> throw new IllegalArgumentException("Invalid entity type: " + entityType);
        }
    }
}

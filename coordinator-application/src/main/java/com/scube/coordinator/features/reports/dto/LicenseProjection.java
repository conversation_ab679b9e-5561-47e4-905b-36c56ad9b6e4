package com.scube.coordinator.features.reports.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LicenseProjection {
    private Integer licenseId;
    private UUID entityId;
    private String licenseNumber;
    private LocalDate validFromDate;
    private LocalDate validToDate;
    private String activityType;
    private LocalDate licenseIssuedDate;
    private String licenseStatus;
    private String licenseType;
    private String licenseTypeCode;
    private String licenseTypeGroup;
    private String licenseFormUuid;
    private LocalDate vaccineDueDate;
    private String rabiesTagNumber;
    private String veterinarianName;
    private String veterinaryName;
    private String vaccineProducer;
    private LocalDate vaccineAdministeredDate;
    private String vaccineLotNumber;
    private String vaccineName;
    private String vaccinePeriod;
    private String dogName;
    private String microchipNumber;
    private String tagNumber;
    private String dogSpayedOrNeutered;
    private String dogSex;
    private String dogSecondaryColor;
    private String dogBreed;
    private String dogPrimaryColor;
    private LocalDate dogBirthDate;
    private String dogBirthYear;
    private String dogMarkings;
    private String dogBio;
    private String dogFriendly;
    private String catFriendly;
    private String childFriendly;
    private String ownerFirstName;
    private String ownerLastName;
    private LocalDate ownerDateOfBirth;
    private String ownerCity;
    private BigDecimal ownerLatitude;
    private BigDecimal ownerLongitude;
    private String ownerState;
    private String ownerAddress;
    private String ownerAddress2;
    private String ownerZip;
    private String ownerFullAddress;
    private String ownerHouseNo;
    private String ownerRoad;
    private String ownerTown;
    private String ownerAddressType;
    private String ownerPhone;
    private String ownerEmail;
    private String clerkName;
    private String clerkTitle;
    private String clerkPhoneNumber;
    private String clerkSignature;
    private String clerkOfficeName;
    private String clerkEmail;
    private String municipalityBuilding;
    private String municipalityCity;
    private String municipalityState;
    private String municipalityAddress;
    private String municipalityRoomNumber;
    private String municipalityZipCode;
    private String municipalityPhoneNumber;
    private LocalDate paidDate;
    private BigDecimal cityFee;
    private BigDecimal stateFee;
    private BigDecimal totalFees;
    private BigDecimal penaltyFee;
    private BigDecimal licProFee;
    private BigDecimal pmtProFee;
    private String seniorDiscountStatusText;
    private String spayNeuterStatusText;
    private String ownerEntityId;
    private String dogEntityId;
    private String vaccineDatesExempt;
}

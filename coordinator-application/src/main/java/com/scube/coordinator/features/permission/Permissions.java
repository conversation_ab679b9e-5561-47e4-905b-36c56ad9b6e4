package com.scube.coordinator.features.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2025-05-02T22:04:31.336468200Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.341284400Z
     */
    public static class LoggedInUserParticipant {
        public static final String DELETE_PARTICIPANT = "coordinator-service-me-participant-delete-participant";

        private LoggedInUserParticipant() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.362685700Z
     */
    public static class PublicDemoEmail {
        public static final String SEND_DEMO_EMAIL = "coordinator-service-public-demo-email-send-demo-email";

        private PublicDemoEmail() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.363686200Z
     */
    public static class LoggedInUserCheckout {
        public static final String CHECKOUT = "coordinator-service-me-checkout-checkout";

        public static final String CHECKOUT_NO_PAYMENT_DUE = "coordinator-service-me-checkout-checkout-no-payment-due";

        private LoggedInUserCheckout() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.363686200Z
     */
    public static class LoggedInUserTextExtractor {
        public static final String PROCESS_FILE = "coordinator-service-me-textextractor-process-file";

        public static final String MERGE = "coordinator-service-me-textextractor-merge";

        private LoggedInUserTextExtractor() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.363686200Z
     */
    public static class DemoData {
        public static final String GENERATE_DEMO_DATA = "coordinator-service-demo-data-generate-demo-data";

        private DemoData() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.363686200Z
     */
    public static class TextExtractor {
        public static final String PROCESS_FILE = "coordinator-service-textextractor-process-file";

        public static final String MERGE = "coordinator-service-textextractor-merge";

        private TextExtractor() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.364685200Z
     */
    public static class History {
        public static final String GET_TRANSACTIONS = "coordinator-service-history-get-transactions";

        private History() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.364685200Z
     */
    public static class LoggedInUserLicense {
        public static final String GENERATE_FORM = "coordinator-service-me-license-generate-form";

        private LoggedInUserLicense() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.364685200Z
     */
    public static class Participant {
        public static final String DELETE_PARTICIPANT = "coordinator-service-participant-delete-participant";

        private Participant() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.364685200Z
     */
    public static class Cart {
        public static final String ADD_TO_CART = "coordinator-service-cart-add-to-cart";

        public static final String INFER_PAYEE = "coordinator-service-cart-infer-payee";

        private Cart() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.364685200Z
     */
    public static class Payment {
        public static final String SUBMIT_PAYMENT_BY_ITEMS = "coordinator-service-payment-submit-payment-by-items";

        public static final String SUBMIT_PAYMENT_BY_CART = "coordinator-service-payment-submit-payment-by-cart";

        public static final String SUBMIT_PAYMENT_BY_ORDER = "coordinator-service-payment-submit-payment-by-order";

        public static final String SUBMIT_PAYMENT_AND_DELETE_CART_ITEMS = "coordinator-service-payment-submit-payment-and-delete-cart-items";

        public static final String GET_RECEIPTS = "coordinator-service-payment-get-receipts";

        public static final String GENERATE_RECEIPT = "coordinator-service-payment-generate-receipt";

        private Payment() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.365685800Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS_FOR_EACH_MICROSERVICE = "coordinator-service-permissions-seed-roles-to-all-realms-for-each-microservice";

        public static final String SEED_ROLES_TO_ALL_REALMS = "coordinator-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "coordinator-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.365685800Z
     */
    public static class LoggedInUserPayment {
        public static final String GET_RECEIPTS = "coordinator-service-me-payment-get-receipts";

        public static final String GENERATE_RECEIPT = "coordinator-service-me-payment-generate-receipt";

        private LoggedInUserPayment() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.365685800Z
     */
    public static class LoggedInUserHistory {
        public static final String GET_TRANSACTIONS = "coordinator-service-me-history-get-transactions";

        private LoggedInUserHistory() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.365685800Z
     */
    public static class Checkout {
        public static final String CHECKOUT = "coordinator-service-checkout-checkout";

        private Checkout() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.365685800Z
     */
    public static class License {
        public static final String GET_LICENSE = "coordinator-service-license-get-license";

        public static final String GET_LICENSES = "coordinator-service-license-get-licenses";

        public static final String GENERATE_FORM_ASYNC = "coordinator-service-license-generate-form-async";

        public static final String GENERATE_FORM = "coordinator-service-license-generate-form";

        private License() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.366686900Z
     */
    public static class Health {
        public static final String GET_SERVICE_HEALTH = "coordinator-service-health-get-service-health";

        private Health() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.366686900Z
     */
    public static class LoggedInUserCart {
        public static final String ADD_TO_CART = "coordinator-service-me-cart-add-to-cart";

        public static final String INFER_PAYEE = "coordinator-service-me-cart-infer-payee";

        private LoggedInUserCart() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.366686900Z
     */
    public static class LoggedInUserContactUs {
        public static final String CONTACT_US = "coordinator-service-me-contact-us-contact-us";

        private LoggedInUserContactUs() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.366686900Z
     */
    public static class CartItemFees {
        public static final String UPDATE_FEE_ON_CART_ITEM = "coordinator-service-fees-cartItem-update-fee-on-cart-item";

        public static final String ADD_FEE_ON_CART_ITEM = "coordinator-service-fees-cartItem-add-fee-on-cart-item";

        public static final String REMOVE_FEE_ON_CART_ITEM = "coordinator-service-fees-cartItem-remove-fee-on-cart-item";

        private CartItemFees() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.366686900Z
     */
    public static class Templates {
        public static final String GET_ALL_TEMPLATES = "coordinator-service-templates-get-all-templates";

        public static final String GET_TEMPLATE = "coordinator-service-templates-get-template";

        public static final String UPSERT_TEMPLATE = "coordinator-service-templates-upsert-template";

        private Templates() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2025-05-02T22:04:31.367684100Z
     */
    public static class EntityFee {
        public static final String CREATE_FEE = "coordinator-service-entity-fee-create-fee";

        public static final String DELETE_FEE = "coordinator-service-entity-fee-delete-fee";

        public static final String UPDATE_FEE = "coordinator-service-entity-fee-update-fee";

        private EntityFee() {
        }
    }
}

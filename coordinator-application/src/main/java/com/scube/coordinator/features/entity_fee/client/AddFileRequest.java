package com.scube.coordinator.features.entity_fee.client;

import lombok.NoArgsConstructor;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

@NoArgsConstructor
public class AddFileRequest extends LinkedMultiValueMap<String, Object> {
    public void putFiles(Map<String, MultipartFile> files) {
        files.forEach((key, file) -> add(key, file.getResource()));
    }
}
package com.scube.coordinator.features.text_extractor.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.ai.dto.gen_dto.MapTextRequest;
import com.scube.ai.generated.MapImageToSchema_MapText_QueryParams;
import com.scube.ai.generated.MapImageToSchema_Me_QueryParams;
import com.scube.client.ai.generated.AiServiceConnection;
import com.scube.client.imageProcessing.generated.ImageProcessingServiceConnection;
import com.scube.imageprocessing.service.ocr.textract.dto.gen_dto.IdResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import static org.apache.commons.lang3.StringUtils.defaultString;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

@Service
@Slf4j
@RequiredArgsConstructor
@ConditionalOnProperty(name = "application.text-extractor.enabled", havingValue = "true", matchIfMissing = true)
public class TextExtractorService implements ITextExtractorService {
    private final AiServiceConnection aiServiceConnection;
    private final ImageProcessingServiceConnection imageProcessingServiceConnection;
    private final ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
    private static final int THREAD_POOL_SIZE = 10;
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private static final long MAX_FILE_SIZE = 5 * FileUtils.ONE_MB; // 5 MB

    public JsonNode processFile(final MultipartFile file, final String promptKey, final String schema, final boolean isMe) {
        if (isMe) {
            return processFileIsMe(file, promptKey, schema);
        } else {
            return processFile(file, promptKey, schema);
        }
    }

    public JsonNode processFile(final MultipartFile file, final String promptKey, final String schema) {
        if (isId(promptKey)) {
            IdResponse idResponse = imageProcessingServiceConnection.textract().analyzeId(file);

            return objectMapper.valueToTree(idResponse);
        }

        return mapText(file, schema, promptKey);
    }

    public JsonNode mapText(final MultipartFile file, final String schema, final String promptKey) {
        if (isNotBlank(schema)) {
            var request = new MapImageToSchema_MapText_QueryParams()
                    .promptKey(defaultString(promptKey))
                    .prompt("");
            var response = aiServiceConnection.mapText().mapImageToSchema(file, schema, request);
            return response.getMappedText();
        } else {
            String ocrText = imageProcessingServiceConnection.imageProcessing().extractTextFromImage(file).getExtractedText();
            var request = new MapTextRequest(ocrText, promptKey);
            return aiServiceConnection.mapText().parseOcrText(request).getMappedText();
        }
    }

    public JsonNode processFileIsMe(final MultipartFile file, final String promptKey, final String schema) {
        if (isId(promptKey)) {
            IdResponse idResponse = imageProcessingServiceConnection.loggedInUserTextract().analyzeId(file);

            return objectMapper.valueToTree(idResponse);
        }

        return mapTextIsMe(file, schema, promptKey);
    }

    public JsonNode mapTextIsMe(final MultipartFile file, final String schema, final String promptKey) {
        if (isNotBlank(schema)) {
            var request = new MapImageToSchema_Me_QueryParams()
                    .promptKey(defaultString(promptKey))
                    .prompt("");
            return aiServiceConnection.loggedInUserMapText().mapImageToSchema(file, schema, request).getMappedText();
        } else {
            String ocrText = imageProcessingServiceConnection.loggedInUser().extractTextFromImage(file).getExtractedText();
            var request = new MapTextRequest(ocrText, promptKey);
            return aiServiceConnection.loggedInUserMapText().parseOcrText(request).getMappedText();
        }
    }

    @SneakyThrows
    @Override
    public String merge(final MultiValueMap<String, MultipartFile> pairs,
                        final String schema,
                        final String promptKey,
                        final boolean isMe) {
        validateFileSize(pairs);

        Map<Integer, JsonNode> responseMap = generateResponses(pairs, schema, promptKey, isMe);

        ArrayList<JsonNode> responses = toSortedList(responseMap);

        String finalizedJson = coalesceReturns(responses);

        return finalizedJson;
    }

    private void validateFileSize(MultiValueMap<String, MultipartFile> pairs) {
        // check tha the sizes are not more than 5 MB
        for (List<MultipartFile> files : pairs.values()) {
            for (MultipartFile file : files) {
                if (file != null && file.getSize() > MAX_FILE_SIZE) {
                    throw new ResponseStatusException(
                            HttpStatus.BAD_REQUEST,
                            "File size exceeds the maximum limit of %s".formatted(FileUtils.byteCountToDisplaySize(MAX_FILE_SIZE))
                    );
                }
            }
        }
    }

    @SneakyThrows
    private @NotNull Map<Integer, JsonNode> generateResponses(MultiValueMap<String, MultipartFile> pairs,
                                                              String schema,
                                                              String promptKey,
                                                              boolean isMe) {
        Map<Integer, JsonNode> responseMap = new HashMap<>();

        List<Future<Void>> futures = new ArrayList<>();
        int index = 0;
        for (Map.Entry<String, List<MultipartFile>> entry : pairs.entrySet()) {
            for (MultipartFile file : entry.getValue()) {
                if (file == null || file.isEmpty()) {
                    continue;
                }

                int currentIndex = index++;
                var context = SecurityContextHolder.getContext();
                Future<?> future = executorService.submit(() -> {
                    SecurityContextHolder.setContext(context);//necessary due to async nature of the executor service
                    responseMap.put(currentIndex, processFile(file, promptKey, schema, isMe));
                });
                futures.add((Future<Void>) future);
            }
        }

        // Wait for all tasks to complete
        for (Future<Void> future : futures) {
            try {
                future.get(); // This will block until the task is complete
            } catch (InterruptedException | ExecutionException e) {
                log.error("During future wait: {}", e.getMessage());
                throw e;
            }
        }
        return responseMap;
    }

    private static @NotNull ArrayList<JsonNode> toSortedList(Map<Integer, JsonNode> responseMap) {
        ArrayList<JsonNode> responses = new ArrayList<>();
        List<Integer> sortedKeys = new ArrayList<>(responseMap.keySet());
        Collections.sort(sortedKeys);

        for (Integer key : sortedKeys) {
            responses.add(responseMap.get(key));
        }
        return responses;
    }

    public static String coalesceReturns(final ArrayList<JsonNode> jsons) {
        JSONObject merged = new JSONObject();

        try {
            for (JsonNode jsonNode : jsons) {
                JSONObject json = new JSONObject(jsonNode.toString());
                Iterator<String> it = json.keys();

                while (it.hasNext()) {
                    String key = it.next();
                    Object value = json.get(key);

                    if (!merged.has(key)) {
                        merged.put(key, sanitizeAndConvertToString(value));
                    } else {
                        if (Objects.equals(merged.get(key), "") && !Objects.equals(value, "")) {
                            // If current value in merged is empty and new value is not empty, overwrite
                            merged.put(key, sanitizeAndConvertToString(value));
                        }
                    }
                }
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }

        return merged.toString();
    }

    private static boolean isId(String fileIdentifier) {
        return "passport".equalsIgnoreCase(fileIdentifier)
               || "idCardFront".equalsIgnoreCase(fileIdentifier);
    }

    private static String sanitizeAndConvertToString(Object object) {
        String string = String.valueOf(object).trim();

        if (string.equals("null")) {
            return "";
        }

        return string;
    }
}

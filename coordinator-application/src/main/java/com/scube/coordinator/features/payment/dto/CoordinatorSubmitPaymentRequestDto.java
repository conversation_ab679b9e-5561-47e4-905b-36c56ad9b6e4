package com.scube.coordinator.features.payment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.payment.features.payment.enums.gen_dto.PaymentStatus;
import com.scube.payment.features.payment.processing.dto.gen_dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.gen_dto.PayeeDto;
import com.scube.payment.features.payment.processing.dto.gen_dto.SubmitPaymentRequestDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoordinatorSubmitPaymentRequestDto<T extends CoordinatorSubmitPaymentRequestDto.PaymentRequestItemDto> {
    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.0", inclusive = true, message = "Amount must be greater than or equal to 0")
    private BigDecimal amount;

    private String status;

    @Pattern(regexp = "^(card|cash|personalCheck|certifiedCheck|moneyOrder|bankACH|paypal)$", message = "Invalid Payment Method")
    private String paymentType;

    private Instant transactionDate;
    private String paymentReference;

    @Valid
    private CoordinatorPayeeDto payee;
    private String adjustmentReason;
    private String adjustedAmount;

    @Valid
    private List<T> items;

    public static CoordinatorSubmitPaymentRequestDto from(GetPaymentResponseDto payment) {
        var result = new CoordinatorSubmitPaymentRequestDto();
        if (payment.getAmount() != null)
            result.setAmount(payment.getAmount());
        if (payment.getStatus() != null)
            result.setStatus(payment.getStatus());
        if (payment.getPaymentType() != null)
            result.setPaymentType(payment.getPaymentType());
        if (payment.getTransactionDate() != null)
            result.setTransactionDate(payment.getTransactionDate());
        if (payment.getPaymentReference() != null)
            result.setPaymentReference(payment.getPaymentReference());
        if (payment.getPayee() != null) {
            result.setPayee(new CoordinatorPayeeDto());
            BeanUtils.copyProperties(payment.getPayee(), result.getPayee());
        }
        return result;
    }

    public SubmitPaymentRequestDto toPaymentClientRequestDto() {
        var result = new SubmitPaymentRequestDto();
        if (this.getAmount() != null)
            result.setPaymentAmount(this.getAmount());
        if (this.getStatus() != null)
            result.setStatus(PaymentStatus.valueOf(this.getStatus()));
        if (this.getPaymentType() != null)
            result.setPaymentType(this.getPaymentType());
        if (this.getTransactionDate() != null)
            result.setTransactionDate(this.getTransactionDate());
        if (this.getPaymentReference() != null)
            result.setPaymentReference(this.getPaymentReference());
        if (this.getPayee() != null) {
            result.setPayee(new PayeeDto());
            BeanUtils.copyProperties(this.getPayee(), result.getPayee());
        }
        return result;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaymentRequestItemDto {
        @NotBlank(message = "Item ID is required")
        private String itemId;

        @NotBlank(message = "Item type is required")
        private String itemType;
    }
}
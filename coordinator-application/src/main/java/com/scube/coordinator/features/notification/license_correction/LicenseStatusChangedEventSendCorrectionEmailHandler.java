package com.scube.coordinator.features.notification.license_correction;

import com.scube.coordinator.features.notification.LicenseStatusChangedEvent;
import com.scube.coordinator.features.notification.NotificationHelper;
import com.scube.rabbit.core.annotation.ConcurrentSubscriberInstance;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
@AllArgsConstructor
@ConcurrentSubscriberInstance(2)
public class LicenseStatusChangedEventSendCorrectionEmailHandler extends FanoutListener<LicenseStatusChangedEvent> {
    private final NotificationHelper notificationHelper;


    @Override
    public void consume(LicenseStatusChangedEvent event) {
        log.info("LicenseStatusChangedEvent received: {}", event);
        var licenseType = event.getLicenseType();
        if (ObjectUtils.isEmpty(licenseType)) return;

        //TODO: in v2, this will be replaced by a query to the database
        var triggerStatus = "Rejected";
        if (ObjectUtils.isEmpty(event.getLicenseStatusName())) return;
        if (!event.getLicenseStatusName().equals(triggerStatus)) return;

        var emailBodyTemplates = Map.of(
                "dogLicense", "DogLicenseCorrectionEmailTemplate",
                "purebredDogLicense", "PurebredDogLicenseCorrectionEmailTemplate"
        );

        var emailSubjectTemplates = Map.of(
                "dogLicense", "DogLicenseCorrectionEmailSubjectTemplate",
                "purebredDogLicense", "PurebredDogLicenseCorrectionEmailSubjectTemplate"
        );

        if (!emailBodyTemplates.containsKey(licenseType)) return;
        if (!emailSubjectTemplates.containsKey(licenseType)) return;

        var emailTemplateName = emailBodyTemplates.get(licenseType);
        var emailSubjectTemplateName = emailSubjectTemplates.get(licenseType);

        notificationHelper.sendEmail(triggerStatus, event.getLicenseEntityId(), licenseType, emailSubjectTemplateName, emailTemplateName, List.of());

        log.info("LicenseStatusChangedEvent processed: {}", event);
    }
}
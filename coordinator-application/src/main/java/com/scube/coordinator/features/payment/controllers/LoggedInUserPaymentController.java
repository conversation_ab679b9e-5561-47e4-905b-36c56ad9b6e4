package com.scube.coordinator.features.payment.controllers;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.payment.dto.GetOrderReceiptsResponseDto;
import com.scube.coordinator.features.payment.services.PaymentService;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/me/payment")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class LoggedInUserPaymentController {
    private final PaymentService paymentService;


    @GetMapping("/{orderId}/receipts")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserPayment.GET_RECEIPTS)
    public GetOrderReceiptsResponseDto getReceipts(@PathVariable UUID orderId) {
        return paymentService.getReceipts(orderId, true);
    }

    @PostMapping("/{orderId}/generate-receipt")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.Payment.GET_RECEIPTS)
    public void generateReceipt(@PathVariable UUID orderId) {
        paymentService.generateReceipt(orderId, true);
    }
}
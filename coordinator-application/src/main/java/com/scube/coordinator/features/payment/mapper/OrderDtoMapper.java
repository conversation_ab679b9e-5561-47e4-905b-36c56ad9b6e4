package com.scube.coordinator.features.payment.mapper;


import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.gen_dto.OrderInvoiceResponse;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public abstract class OrderDtoMapper {
    public abstract OrderInvoiceItem toOrderInvoiceItem(com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem orderInvoiceItem);

    public abstract OrderInvoiceResponse toOrderInvoiceResponse(com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse order);
}

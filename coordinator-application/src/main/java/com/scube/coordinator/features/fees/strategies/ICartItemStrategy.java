package com.scube.coordinator.features.fees.strategies;

import com.scube.calculation.dto.gen_dto.CartInvoiceItem;
import com.scube.coordinator.features.fees.dto.AddFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.RemoveFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.UpdateFeeOnCartItemRequest;

public interface ICartItemStrategy {
    Object updateFee(CartInvoiceItem cartItem, UpdateFeeOnCartItemRequest request);

    Object addFee(CartInvoiceItem cartItem, AddFeeOnCartItemRequest request);

    Object removeFee(CartInvoiceItem cartItem, RemoveFeeOnCartItemRequest request);
}
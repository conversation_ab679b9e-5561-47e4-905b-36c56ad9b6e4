package com.scube.coordinator.features.contact_us;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/me/contact-us")
@RequiredArgsConstructor
public class LoggedInUserContactUsController {
    private final ContactUsService contactUsService;

    @PostMapping
    @RolesAllowed(Permissions.LoggedInUserContactUs.CONTACT_US)
    public void contactUs(@AuthenticationPrincipal MyOpenIdClaimSet claimSet, @RequestBody ContactUsRequest request) {
        contactUsService.contactUs(claimSet, request);
    }
}
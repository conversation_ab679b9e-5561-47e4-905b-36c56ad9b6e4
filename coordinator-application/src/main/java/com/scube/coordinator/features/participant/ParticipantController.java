package com.scube.coordinator.features.participant;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.auth.library.enabled_true.keycloak.services.KeycloakUserService;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.permission.Permissions;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequestMapping("/participant")
@RequiredArgsConstructor
public class ParticipantController {
    private final KeycloakUserService keycloakUserService;
    private final LicenseServiceConnection license;

    @DeleteMapping("{participantEntityId}")
    @Operation(summary = "Delete Keycloak user and mark participant as offline resident")
    @RolesAllowed(Permissions.Participant.DELETE_PARTICIPANT)
    public void deleteParticipant(@AuthenticationPrincipal MyOpenIdClaimSet jwt, @PathVariable UUID participantEntityId) {
        // can only delete user in the same realm
        keycloakUserService.delete(jwt.getRealm(), String.valueOf(participantEntityId));
        license.participant().markOfflineResident(participantEntityId);
    }
}
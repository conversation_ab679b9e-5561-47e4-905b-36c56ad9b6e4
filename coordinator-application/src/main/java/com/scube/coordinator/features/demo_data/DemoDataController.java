package com.scube.coordinator.features.demo_data;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.multi.tenant.annotations.BypassArchUnitAsyncTest;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/demo-data")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class DemoDataController {
    private final DemoDataService demoDataService;

    @PostMapping("/generate/{count}")
    @RolesAllowed("generate-demo-data")
    @BypassArchUnitAsyncTest
    public List<Map<String, Object>> generateDemoData(@PathVariable int count) {
        List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            futures.add(demoDataService.generateDemoDataAsync());
        }
        // Wait for all tasks to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        log.info("All demo data generation tasks completed.");
        // Collect results
        return futures.stream()
                .map(CompletableFuture::join)
                .toList();
    }
}
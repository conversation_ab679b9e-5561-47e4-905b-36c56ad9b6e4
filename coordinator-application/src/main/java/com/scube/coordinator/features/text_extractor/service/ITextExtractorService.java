package com.scube.coordinator.features.text_extractor.service;

import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

public interface ITextExtractorService {
    JsonNode processFile(final MultipartFile file,
                         final String fileIdentifier,
                         final String schema,
                         final boolean isMe);

    String merge(final MultiValueMap<String, MultipartFile> pairs,
                 final String schema,
                 final String promptKey,
                 final boolean isMe);
}
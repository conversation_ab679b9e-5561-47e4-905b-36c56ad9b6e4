package com.scube.coordinator.features.payment.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.UUID;

@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoordinatorSubmitPaymentRequestByCartDto extends CoordinatorSubmitPaymentRequestDto<CoordinatorSubmitPaymentRequestByCartDto.PaymentRequestCartItemDto> {
    @NotNull(message = "Cart id is required to remove cart items")
    private UUID cartId;

    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PaymentRequestCartItemDto extends CoordinatorSubmitPaymentRequestDto.PaymentRequestItemDto {
        @NotNull(message = "Cart item ID is required")
        @Min(value = 1, message = "Cart item ID must be greater than 0")
        private long cartItemId;
    }
}
package com.scube.coordinator.features.entity_fee.client;

import com.scube.licensing.features.document.dto.gen_dto.DocumentDto;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.PostExchange;

import java.util.List;
import java.util.UUID;

public interface IEntityFeeExchange {
    @PostExchange(
            value = "/entity-fee/{feeEntityId}/add-file",
            contentType = "multipart/form-data"
    )
    List<DocumentDto> addFile(@PathVariable UUID feeEntityId, @RequestBody AddFileRequest request);
}
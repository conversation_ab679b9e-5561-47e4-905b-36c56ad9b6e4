package com.scube.coordinator.features.payment.mapper;

import com.scube.coordinator.features.payment.dto.CheckoutNoPaymentDueDto;
import com.scube.coordinator.features.payment.dto.CoordinatorPayeeDto;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestByCartDto;
import com.scube.licensing.features.profile.dto.gen_dto.ContactDto;
import com.scube.licensing.features.profile.dto.gen_dto.ParticipantAddressDto;
import com.scube.licensing.features.profile.dto.gen_dto.ParticipantDto;
import org.mapstruct.Mapper;

import java.math.BigDecimal;
import java.time.Instant;

@Mapper(componentModel = "spring")
public abstract class PaymentMapper {
    public CoordinatorSubmitPaymentRequestByCartDto toSubmitPaymentRequestByCartDto(CheckoutNoPaymentDueDto checkoutNoPaymentDueDto, ParticipantDto participantDto, String firstName, String lastName) {
        CoordinatorSubmitPaymentRequestByCartDto submitPaymentRequestByCartDto = new CoordinatorSubmitPaymentRequestByCartDto();
        submitPaymentRequestByCartDto.setCartId(checkoutNoPaymentDueDto.getCartId());
        submitPaymentRequestByCartDto.setPaymentType("cash");
        submitPaymentRequestByCartDto.setAmount(new BigDecimal("0.0"));
        submitPaymentRequestByCartDto.setTransactionDate(Instant.now());
        submitPaymentRequestByCartDto.setPayee(toPayeeDto(participantDto, firstName, lastName));
        return submitPaymentRequestByCartDto;
    }

    public CoordinatorPayeeDto toPayeeDto(ParticipantDto participantDto, String firstName, String lastName) {
        CoordinatorPayeeDto payeeDto = new CoordinatorPayeeDto();
        payeeDto.setFirstName(firstName);
        payeeDto.setLastName(lastName);
        payeeDto.setEmail(getEmail(participantDto));
        payeeDto.setPhone(getPhone(participantDto));

        ParticipantAddressDto address = participantDto.getAddresses().stream().findFirst().orElse(null);

        if (address != null) {
            payeeDto.setMailingAddress(address.getStreetAddress());
            payeeDto.setMailingAddress2(address.getStreetAddress2());
            payeeDto.setMailingCity(address.getCity());
            payeeDto.setMailingState(address.getState());
            payeeDto.setMailingZipCode(address.getZip());
            payeeDto.setBillingSameAsMailing(true);
            payeeDto.setBillingAddress(address.getStreetAddress());
            payeeDto.setBillingAddress2(address.getStreetAddress2());
            payeeDto.setBillingCity(address.getCity());
            payeeDto.setBillingState(address.getState());
            payeeDto.setBillingZipCode(address.getZip());
        }

        return payeeDto;
    }

    public static String getEmail(ParticipantDto participantDto) {
        return participantDto.getContacts().stream()
                .filter(contact -> "Email".equals(contact.getType()) && "Primary".equals(contact.getGroup()))
                .findFirst()
                .map(ContactDto::getValue)
                .orElse(null);
    }

    public static String getPhone(ParticipantDto participantDto) {
        return participantDto.getContacts().stream()
                .filter(contact -> "Phone".equals(contact.getType()) && "Home".equals(contact.getGroup()))
                .findFirst()
                .map(ContactDto::getValue)
                .orElse(null);
    }
}


package com.scube.coordinator.features.license.dto;

import com.scube.coordinator.features.reports.dto.LicenseProjection;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LicenseData {
    public static LicenseData of(LicenseProjection licenseProjection) {
        if (licenseProjection == null) {
            return null;
        }

        LicenseData licenseDataFormatted = new LicenseData();
        licenseDataFormatted.setLicenseId(licenseProjection.getLicenseId());
        licenseDataFormatted.setEntityId(licenseProjection.getEntityId());
        licenseDataFormatted.setLicenseNumber(licenseProjection.getLicenseNumber());
        licenseDataFormatted.setValidFromDate(licenseProjection.getValidFromDate());
        licenseDataFormatted.setValidToDate(licenseProjection.getValidToDate());
        licenseDataFormatted.setLicenseIssuedDate(licenseProjection.getLicenseIssuedDate());
        licenseDataFormatted.setLicenseStatus(licenseProjection.getLicenseStatus());
        licenseDataFormatted.setLicenseType(licenseProjection.getLicenseType());
        licenseDataFormatted.setLicenseTypeCode(licenseProjection.getLicenseTypeCode());
        licenseDataFormatted.setLicenseTypeGroup(licenseProjection.getLicenseTypeGroup());

        LicenseData.Dog dog = new LicenseData.Dog();

        dog.setEntityId(licenseProjection.getDogEntityId());
        dog.setVaccineDueDate(licenseProjection.getVaccineDueDate());
        dog.setRabiesTagNumber(licenseProjection.getRabiesTagNumber());
        dog.setVeterinaryName(licenseProjection.getVeterinaryName());
        dog.setVeterinarianName(licenseProjection.getVeterinarianName());
        dog.setVaccineProducer(licenseProjection.getVaccineProducer());
        dog.setVaccineAdministeredDate(licenseProjection.getVaccineAdministeredDate());
        dog.setVaccineLotNumber(licenseProjection.getVaccineLotNumber());
        dog.setVaccineName(licenseProjection.getVaccineName());
        dog.setVaccinePeriod(licenseProjection.getVaccinePeriod());
        dog.setDogName(licenseProjection.getDogName());
        dog.setMicrochipNumber(licenseProjection.getMicrochipNumber());
        dog.setTagNumber(licenseProjection.getTagNumber());
        dog.setDogSpayedOrNeutered(licenseProjection.getDogSpayedOrNeutered());
        dog.setDogSex(licenseProjection.getDogSex());
        dog.setDogSecondaryColor(licenseProjection.getDogSecondaryColor());
        dog.setDogBreed(licenseProjection.getDogBreed());
        dog.setDogPrimaryColor(licenseProjection.getDogPrimaryColor());
        dog.setDogBirthDate(licenseProjection.getDogBirthDate());
        dog.setBirthYear(licenseProjection.getDogBirthYear());
        dog.setDogMarkings(licenseProjection.getDogMarkings());
        dog.setDogBio(licenseProjection.getDogBio());
        dog.setDogFriendly(licenseProjection.getDogFriendly());
        dog.setCatFriendly(licenseProjection.getCatFriendly());
        dog.setChildFriendly(licenseProjection.getChildFriendly());
        dog.setVaccineDatesExempt(licenseProjection.getVaccineDatesExempt());

        licenseDataFormatted.setDog(dog);

        LicenseData.Owner owner = new LicenseData.Owner();

        owner.setEntityId(licenseProjection.getOwnerEntityId());
        owner.setFirstName(licenseProjection.getOwnerFirstName());
        owner.setLastName(licenseProjection.getOwnerLastName());
        owner.setDateOfBirth(licenseProjection.getOwnerDateOfBirth());
        owner.setPhone(licenseProjection.getOwnerPhone());
        owner.setEmail(licenseProjection.getOwnerEmail());

        LicenseData.OwnerAddress ownerAddress = new LicenseData.OwnerAddress();

        ownerAddress.setCity(licenseProjection.getOwnerCity());
        ownerAddress.setLatitude(licenseProjection.getOwnerLatitude());
        ownerAddress.setLongitude(licenseProjection.getOwnerLongitude());
        ownerAddress.setState(licenseProjection.getOwnerState());
        ownerAddress.setAddress(licenseProjection.getOwnerAddress());
        ownerAddress.setAddress2(licenseProjection.getOwnerAddress2());
        ownerAddress.setZip(licenseProjection.getOwnerZip());
        ownerAddress.setFullAddress(licenseProjection.getOwnerFullAddress());
        ownerAddress.setHouseNo(licenseProjection.getOwnerHouseNo());
        ownerAddress.setRoad(licenseProjection.getOwnerRoad());
        ownerAddress.setTown(licenseProjection.getOwnerTown());
        ownerAddress.setAddressType(licenseProjection.getOwnerAddressType());

        owner.setAddress(ownerAddress);

        licenseDataFormatted.setOwner(owner);

        return licenseDataFormatted;
    }

    private Integer licenseId;
    private UUID entityId;
    private String licenseNumber;
    private LocalDate validFromDate;
    private LocalDate validToDate;
    private LocalDate licenseIssuedDate;
    private String licenseStatus;
    private String licenseType;
    private String licenseTypeCode;
    private String licenseTypeGroup;
    private String licenseDocumentUrl;
    private Dog dog;
    private Owner owner;
    private List<String> actions;

    @Data
    public static class Dog {
        private String entityId;
        private LocalDate vaccineDueDate;
        private String rabiesTagNumber;
        private String veterinaryName;
        private String veterinarianName;
        private String vaccineProducer;
        private LocalDate vaccineAdministeredDate;
        private String vaccineLotNumber;
        private String vaccineName;
        private String vaccinePeriod;
        private String dogName;
        private String microchipNumber;
        private String tagNumber;
        private String dogSpayedOrNeutered;
        private String dogSex;
        private String dogSecondaryColor;
        private String dogBreed;
        private String dogPrimaryColor;
        private LocalDate dogBirthDate;
        private String birthYear;
        private String dogMarkings;
        private String dogBio;
        private String dogFriendly;
        private String catFriendly;
        private String childFriendly;
        private String vaccineDatesExempt;
    }

    @Data
    public static class Owner {
        private String entityId;
        private String firstName;
        private String lastName;
        private LocalDate dateOfBirth;
        private OwnerAddress address;
        private String phone;
        private String email;
    }

    @Data
    public static class OwnerAddress {
        private String city;
        private BigDecimal latitude;
        private BigDecimal longitude;
        private String state;
        private String address;
        private String address2;
        private String zip;
        private String fullAddress;
        private String houseNo;
        private String road;
        private String town;
        private String addressType;
    }
}

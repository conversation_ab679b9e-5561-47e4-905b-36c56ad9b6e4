package com.scube.coordinator.features.payment.controllers;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.payment.dto.CheckoutDto;
import com.scube.coordinator.features.payment.services.PaymentService;
import com.scube.coordinator.features.permission.Permissions;
import com.scube.payment.features.providers.gateway.gen_dto.PaymentTokenResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("checkout")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class CheckoutController {
    private final PaymentService paymentService;

    @PostMapping
    @RolesAllowed(Permissions.Checkout.CHECKOUT)
    public PaymentTokenResponse checkout(@Valid @RequestBody CheckoutDto checkoutRequest) {
        return paymentService.checkout(checkoutRequest, false);
    }
}

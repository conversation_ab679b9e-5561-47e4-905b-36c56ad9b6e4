package com.scube.coordinator.features.license;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.license.dto.GenerateLicenseFormRequest;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("license")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
@Validated
public class LicenseController {
    private final LicenseService licenseService;

    @GetMapping("/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.GET_LICENSE)
    public ResponseEntity<?> getLicense(@PathVariable final UUID entityId) {
        return ResponseEntity.ok(licenseService.getLicense(entityId));
    }

    @GetMapping("/entity/{entityType}/{entityId}")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.License.GET_LICENSES)
    public ResponseEntity<?> getLicenses(@PathVariable final @Size(max = 255) String entityType,
                                         @PathVariable final UUID entityId) {
        return ResponseEntity.ok(licenseService.getLicenses(entityType, entityId));
    }

    @PostMapping("generate-form-async")
    @RolesAllowed(Permissions.License.GENERATE_FORM_ASYNC)
    public void generateFormAsync(@RequestBody @Valid GenerateLicenseFormRequest request) {
        licenseService.generateFormAsync(request);
    }

    @PostMapping("generate-form")
    @RolesAllowed(Permissions.License.GENERATE_FORM)
    public Map<String, String> generateForm(@RequestBody @Valid GenerateLicenseFormRequest request) {
        var documentId = licenseService.generateForm(request);
        return Map.of("url", documentId);
    }
}
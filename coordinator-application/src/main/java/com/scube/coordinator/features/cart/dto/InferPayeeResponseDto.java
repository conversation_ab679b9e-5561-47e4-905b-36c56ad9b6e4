package com.scube.coordinator.features.cart.dto;

import com.scube.coordinator.features.reports.dto.LicenseProjection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InferPayeeResponseDto {
    private String firstName;
    private String lastName;
    private String businessName;
    private String email;
    private String phone;
    private String mailingAddress;
    private String mailingAddress2;
    private String mailingCity;
    private String mailingState;
    private String mailingZipCode;

    public static InferPayeeResponseDto of(LicenseProjection licenseProjection) {
        return InferPayeeResponseDto.builder()
                .firstName(licenseProjection.getOwnerFirstName())
                .lastName(licenseProjection.getOwnerLastName())
                .businessName("")
                .email(licenseProjection.getOwnerEmail())
                .phone(licenseProjection.getOwnerPhone())
                .mailingAddress(licenseProjection.getOwnerAddress())
                .mailingAddress2(licenseProjection.getOwnerAddress2())
                .mailingCity(licenseProjection.getOwnerCity())
                .mailingState(licenseProjection.getOwnerState())
                .mailingZipCode(licenseProjection.getOwnerZip())
                .build();
    }
}

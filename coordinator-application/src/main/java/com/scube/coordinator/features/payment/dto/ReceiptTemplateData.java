package com.scube.coordinator.features.payment.dto;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.scube.coordinator.features.notification.TenantDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class ReceiptTemplateData {
    private String orderNumber;
    private String orderDate;
    private String orderTime;
    private String clerkName;
    private String clerkTitle;
    private List<ReceiptItem> receiptItems;
    private String subTotal;
    private String discounts;
    private String total;
    private String balance;
    private String paymentType;
    private List<Payee> payee;
    private String notes;
    private String paymentReference;
    private List<Map<String, Object>> additionalFee;

    @JsonUnwrapped
    private Municipality municipality;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Payee {
        private String name;
        private String billingAddress;
        private String billingCityStateZip;

        public static Payee of(CoordinatorPayeeDto payeeDto) {
            Payee payee = Payee.builder()
                    .name(payeeDto.getFirstName() + " " + payeeDto.getLastName())
                    .build();

            if (payeeDto.isBillingSameAsMailing()) {
                payee.setBillingAddress(payeeDto.getMailingAddress());
                payee.setBillingCityStateZip(payeeDto.getMailingCity() + ", " + payeeDto.getMailingState() + " " + payeeDto.getMailingZipCode());
            } else {
                payee.setBillingAddress(payeeDto.getBillingAddress());
                payee.setBillingCityStateZip(payeeDto.getBillingCity() + ", " + payeeDto.getBillingState() + " " + payeeDto.getBillingZipCode());
            }

            return payee;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ReceiptItem {
        private String item;
        private String unitPrice;
        private String note;
        private String userId;
        private String quantity;
        private String amount;

        public void combine(ReceiptItem other) {
            int combinedQuantity = Integer.parseInt(this.quantity) + Integer.parseInt(other.quantity);
            BigDecimal combinedAmount = new BigDecimal(this.amount).add(new BigDecimal(other.amount));
            this.quantity = String.valueOf(combinedQuantity);
            this.amount = combinedAmount.toString();
        }
    }

    /**
     * Loops through a list of ReceiptItems and combines duplicates into a single ReceiptItem
     * with an updated quantity and amount.
     */
    public static List<ReceiptItem> combineDuplicateItems(List<ReceiptItem> receiptItems) {
        Map<String, ReceiptItem> map = new HashMap<>();

        for (ReceiptItem item : receiptItems) {
            ReceiptItem existingItem = map.get(item.getItem());
            if (existingItem == null) {
                map.put(item.getItem(), item);
            } else {
                existingItem.combine(item);
                map.put(item.getItem(), existingItem);
            }
        }

        // Convert the values of the map back to a list
        return new ArrayList<>(map.values());
    }

    @Data
    public static class Municipality {
        private String building;
        private String address;
        private String room;
        private String cityStateZip;
        private String phoneNumber;
        private String clerkName;
        private String clerkTitle;

        @JsonUnwrapped
        private TenantDto tenantDto;

        public Municipality(TenantDto tenantDto) {
            this.building = tenantDto.getAdminOffice();
            this.address = tenantDto.getAdminStreet();
            this.room = tenantDto.getAdminOfficeRoom();
            var city = tenantDto.getAdminCity();
            var state = tenantDto.getAdminState();
            var zip = tenantDto.getAdminZipCode();
            this.cityStateZip = "%s, %s %s".formatted(city, state, zip);
            this.phoneNumber = tenantDto.getClerkPhoneNumber();
            this.tenantDto = tenantDto;
        }
    }
}
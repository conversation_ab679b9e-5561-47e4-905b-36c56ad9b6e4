package com.scube.coordinator.features.fees;

import com.scube.calculation.dto.gen_dto.CartInvoiceItem;
import com.scube.calculation.dto.gen_dto.CartInvoiceItemFee;
import com.scube.calculation.dto.gen_dto.UpdateCartItemFeeRequest;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.coordinator.features.cart.CartService;
import com.scube.coordinator.features.cart.dto.AddItemToCartRequest;
import com.scube.coordinator.features.fees.dto.AddFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.RemoveFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.UpdateFeeOnCartItemRequest;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

@UtilityClass
public class CartItemFeeUtils {
    @Nullable
    public static CartInvoiceItemFee getCartItemFee(UUID cartItemFeeId, CartInvoiceItem cartItem) {
        return cartItem.getFees().stream()
                .filter(fee -> !ObjectUtils.isEmpty(fee.getAdditionalProperties()))
                .filter(fee -> fee.getFeeId().equals(cartItemFeeId))
                .findFirst()
                .orElse(null);
    }

    @Nullable
    public static UUID getLicenseActivityFeeId(UUID cartItemFeeId, CartInvoiceItem cartItem) {
        var cartItemFee = getCartItemFee(cartItemFeeId, cartItem);
        if (ObjectUtils.isEmpty(cartItemFee))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Fee not found in cart item");
        if (ObjectUtils.isEmpty(cartItemFee.getAdditionalProperties())) return null;
        if (!cartItemFee.getAdditionalProperties().containsKey("licenseActivityFeeUUID")) return null;
        return UUID.fromString(cartItemFee.getAdditionalProperties().get("licenseActivityFeeUUID").toString());
    }

    @Nullable
    public UUID getEntityFeeId(UUID cartItemFeeId, CartInvoiceItem cartItem) {
        var cartItemFee = getCartItemFee(cartItemFeeId, cartItem);
        if (ObjectUtils.isEmpty(cartItemFee))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Fee not found in cart item");
        if (ObjectUtils.isEmpty(cartItemFee.getAdditionalProperties())) return null;
        if (!cartItemFee.getAdditionalProperties().containsKey("entityFeeId")) return null;
        return UUID.fromString(cartItemFee.getAdditionalProperties().get("entityFeeId").toString());
    }

    public UUID getCartItemFeeId(UUID cartItemFeeId, CartInvoiceItem cartItem) {
        var cartItemFee = getCartItemFee(cartItemFeeId, cartItem);
        if (cartItemFee == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Fee not found on cart item");
        }
        return cartItemFee.getFeeId();
    }

    public void addFeeOnCartItem(CalculationServiceConnection calculationServiceConnection, Long cartItemId, AddFeeOnCartItemRequest request) {
        calculationServiceConnection.cart().addFeeOnCartItem(cartItemId, new com.scube.calculation.dto.gen_dto.AddFeeOnCartItemRequest(
                request.getFeeCode(),
                request.getFeeAmount(),
                request.getReason()
        ));
    }

    public void updateFeeOnCartItem(CalculationServiceConnection calculationServiceConnection, CartInvoiceItem cartItem, UpdateFeeOnCartItemRequest request) {
        var cartItemFeeId = CartItemFeeUtils.getCartItemFeeId(request.getFeeId(), cartItem);
        var updateRequest = new UpdateCartItemFeeRequest(request.getFeeAmount(), request.getReason());
        calculationServiceConnection.cart().changePriceOnCartItemFee(cartItemFeeId, updateRequest);
    }

    public static void removeFeeOnCartItem(CalculationServiceConnection calculationServiceConnection, CartInvoiceItem cartItem, RemoveFeeOnCartItemRequest request) {
        var cartItemFeeId = CartItemFeeUtils.getCartItemFeeId(request.getFeeId(), cartItem);
        calculationServiceConnection.cart().removeCartItemFee(cartItemFeeId);
    }

    public static void refreshCartItem(CalculationServiceConnection calculationServiceConnection,
                                       CartService cartService,
                                       boolean refreshCartItem,
                                       CartInvoiceItem cartItem) {
        if (!refreshCartItem) return;
        calculationServiceConnection.cart().removeItem(cartItem.getCartId(), cartItem.getCartItemId());
        cartService.addToCart(cartItem.getCartId(), new AddItemToCartRequest(cartItem.getItemType(), cartItem.getItemId()), false);
    }
}

package com.scube.coordinator.features.notification.exceptions;

import com.fasterxml.jackson.core.JsonProcessingException;

public class LicenseNotificationDataStringConversionException extends RuntimeException {
    public LicenseNotificationDataStringConversionException(JsonProcessingException e) {
        super("Cannot convert data returned from RPC-GetLicenseNotificationDataQuery to string: " + e.getMessage(), e);
    }

    public LicenseNotificationDataStringConversionException() {
        super("Data returned from RPC-GetLicenseNotificationDataQuery is null.");
    }
}
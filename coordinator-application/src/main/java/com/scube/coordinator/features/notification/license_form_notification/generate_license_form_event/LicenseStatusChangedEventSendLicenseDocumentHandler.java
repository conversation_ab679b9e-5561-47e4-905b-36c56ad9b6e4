package com.scube.coordinator.features.notification.license_form_notification.generate_license_form_event;

import com.scube.coordinator.features.license.LicenseService;
import com.scube.coordinator.features.license.dto.GenerateLicenseFormRequest;
import com.scube.coordinator.features.notification.LicenseStatusChangedEvent;
import com.scube.coordinator.features.notification.NotificationHelper;
import com.scube.rabbit.core.annotation.ConcurrentSubscriberInstance;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
@AllArgsConstructor
@ConcurrentSubscriberInstance(1)
public class LicenseStatusChangedEventSendLicenseDocumentHandler extends FanoutListener<LicenseStatusChangedEvent> {
    private final NotificationHelper notificationHelper;
    private final LicenseService licenseService;


    @Override
    public void consume(LicenseStatusChangedEvent event) {
        log.info("LicenseStatusChangedEvent received: {}", event);
        //TODO: in v2, this will be replaced by a query to the database
        var triggerStatus = "Active";

        var emailBodyTemplates = Map.of(
                "dogLicense", "DogLicenseEmailTemplate",
                "purebredDogLicense", "PurebredDogLicenseEmailTemplate"
        );

        var emailSubjectTemplates = Map.of(
                "dogLicense", "DogLicenseEmailSubjectTemplate",
                "purebredDogLicense", "PurebredDogLicenseEmailSubjectTemplate"
        );

        if (event.getLicenseStatusName() == null) return;
        if (!event.getLicenseStatusName().equals(triggerStatus)) return;
        if (ObjectUtils.isEmpty(event.getLicenseType())) return;
        if (!emailBodyTemplates.containsKey(event.getLicenseType())) return;
        if (!emailSubjectTemplates.containsKey(event.getLicenseType())) return;

        var emailTemplateName = emailBodyTemplates.get(event.getLicenseType());
        var emailSubjectTemplateName = emailSubjectTemplates.get(event.getLicenseType());

        var documentUUID = licenseService.generateForm(new GenerateLicenseFormRequest(event.getLicenseEntityId()));

        notificationHelper.sendEmail(triggerStatus, event.getLicenseEntityId(), event.getLicenseType(),
                emailSubjectTemplateName, emailTemplateName, List.of(documentUUID)
        );

        log.info("LicenseStatusChangedEvent processed: {}", event);
    }
}
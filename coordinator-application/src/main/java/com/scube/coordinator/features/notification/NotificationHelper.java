package com.scube.coordinator.features.notification;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.coordinator.features.contact_us.GetGeneratedCodeQuery;
import com.scube.coordinator.features.notification.exceptions.GenerateEmailMessageCommandException;
import com.scube.coordinator.features.notification.exceptions.LicenseNotificationDataStringConversionException;
import com.scube.coordinator.features.notification.license_form_notification.GetLicenseDataQuery;
import com.scube.notification.client.model.Email;
import com.scube.notification.client.rabbit.ScheduleEmailFanoutEvent;
import com.scube.rabbit.core.AmqpGateway;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.*;

@Component
@RequiredArgsConstructor
public class NotificationHelper {
    private final AmqpGateway amqpGateway;
    private final ObjectMapper objectMapper;

    public void sendEmail(String triggerStatus, UUID licenseEntityId, String licenseType, String emailSubjectTemplateName, String emailTemplateName, List<String> documentIds) {
        var unFormattedDataResult = amqpGateway.queryResult(new GetLicenseDataQuery(licenseEntityId, false));
        Assert.notNull(unFormattedDataResult, "License data is null");
        Assert.notNull(unFormattedDataResult.getResult(), "Formatted data is null");
        var unFormattedData = unFormattedDataResult.getResult();
        JsonNode unFormattedDataString = objectMapper.valueToTree(unFormattedData);
        if (unFormattedDataString == null) throw new LicenseNotificationDataStringConversionException();

        var licenseCode = getLicenseCode(licenseEntityId, amqpGateway);
        ((ObjectNode) unFormattedDataString).put("licenseCode", licenseCode);

        var emailBodyResult = amqpGateway.queryResult(new GenerateTextCommand(emailTemplateName, unFormattedDataString));
        if (emailBodyResult == null || !emailBodyResult.isSuccess())
            throw new GenerateEmailMessageCommandException();

        var emailSubjectResult = amqpGateway.queryResult(new GenerateTextCommand(emailSubjectTemplateName, unFormattedDataString));
        if (emailSubjectResult == null || !emailSubjectResult.isSuccess())
            throw new GenerateEmailMessageCommandException();

        var to = getEmailTo(unFormattedData);
        if (to == null) return; //no email to send to
        var subject = emailSubjectResult.getResult();
        var body = emailBodyResult.getResult();

        amqpGateway.publish(
                ScheduleEmailFanoutEvent.builder()
                        .correlationId(String.valueOf(licenseEntityId))
                        .tag(licenseType)
                        .topic(triggerStatus)
                        .createdBy("CoordinatorService")
                        .email(
                                Email.builder()
                                        .to(to)
                                        .from("<EMAIL>")
                                        .subject(subject)
                                        .body(body)
                                        .contentType("text/html")
                                        .attachmentUuids(documentIds).build()
                        ).build()
        );
    }

    private static String getEmailTo(Map<String, Object> data) {
        String to = null;
        Object ownerEmailObj = data.getOrDefault("ownerEmail.email", new ArrayList<HashMap<String, Object>>());
        if (ownerEmailObj instanceof String ownerEmailStr) {
            to = ownerEmailStr;
        }
        return to;
    }

    private String getLicenseCode(UUID entityId, AmqpGateway amqpGateway) {
        var code = amqpGateway.queryResult(new GetGeneratedCodeQuery("license", String.valueOf(entityId)));
        if (ObjectUtils.isEmpty(code) || ObjectUtils.isEmpty(code.getResult()) || ObjectUtils.isEmpty(code.getResult().code()))
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Unable to generate license code");
        return code.getResult().code();
    }
}

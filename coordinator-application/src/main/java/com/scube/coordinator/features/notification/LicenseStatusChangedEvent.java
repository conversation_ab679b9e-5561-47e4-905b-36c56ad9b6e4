package com.scube.coordinator.features.notification;

import com.scube.rabbit.core.annotation.ConcurrentSubscriberCount;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.Data;

import java.io.Serial;
import java.util.UUID;

@Data
@ConcurrentSubscriberCount(2)
public final class LicenseStatusChangedEvent implements IRabbitFanoutSubscriber {
    @Serial
    private static final long serialVersionUID = -2051167983581220564L;
    private Long licenseId;
    private UUID licenseEntityId;
    private String licenseType;
    private Long licenseStatusId;
    private String licenseStatusName;
}
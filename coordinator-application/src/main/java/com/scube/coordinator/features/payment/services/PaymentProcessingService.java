package com.scube.coordinator.features.payment.services;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.payment.generated.PaymentServiceConnection;
import com.scube.coordinator.features.payment.dto.CoordinatorSubmitPaymentRequestDto;
import com.scube.coordinator.features.payment.mapper.OrderDtoMapper;
import com.scube.payment.features.payment.processing.dto.gen_dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.gen_dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.gen_dto.SubmitPaymentResponseDto;
import com.scube.payment.features.providers.gateway.gen_dto.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.gen_dto.PaymentTokenResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
@AllArgsConstructor
public class PaymentProcessingService {
    private final PaymentServiceConnection paymentServiceConnection;
    private final OrderDtoMapper orderDtoMapper;

    public SubmitPaymentResponseDto submitPayment(CoordinatorSubmitPaymentRequestDto paymentRequest, OrderInvoiceResponse order, boolean isMe) {
        SubmitPaymentRequestDto paymentServiceRequest = paymentRequest.toPaymentClientRequestDto();
        paymentServiceRequest.setOrderAmount(order.getTotal());
        paymentServiceRequest.setOrderId(order.getOrderId());

        SubmitPaymentResponseDto paymentResponse = null;
        if (isMe) {
            paymentResponse = paymentServiceConnection.loggedInUser().submitPayment(paymentServiceRequest);
        } else {
            paymentResponse = paymentServiceConnection.payment().submitPayment(paymentServiceRequest);
        }

        return paymentResponse;
    }

    private record PaymentResult(SubmitPaymentRequestDto paymentServiceRequest,
                                 SubmitPaymentResponseDto paymentResponse) {
    }

    public List<GetPaymentResponseDto> getPayments(UUID orderId, boolean isMe) {
        if (isMe) {
            return paymentServiceConnection.loggedInUser().getPaymentsByOrderId(orderId);
        }
        return paymentServiceConnection.payment().getPaymentsByOrderId(orderId);
    }

    public PaymentTokenResponse getHostedPaymentPageToken(OrderInvoiceResponse order, BigDecimal paymentAmount, boolean isMe) {
        PaymentTokenRequest paymentTokenRequest = new PaymentTokenRequest(
                order.getOrderId(), paymentAmount, order.getTotal(), orderDtoMapper.toOrderInvoiceResponse(order)
        );
        PaymentTokenResponse resp = null;
        if (isMe) {
            resp = paymentServiceConnection.loggedInUser().getPaymentToken(paymentTokenRequest);
        } else {
            resp = paymentServiceConnection.payment().getPaymentToken(paymentTokenRequest);
        }
        return resp;
    }
}
package com.scube.coordinator.features.templates;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.client.documentTemplate.generated.DocumentTemplateServiceConnection;
import com.scube.client.report.generated.ReportServiceConnection;
import com.scube.coordinator.features.templates.client.IDocumentTemplateHttpExchange;
import com.scube.coordinator.features.templates.dto.GetAllTemplates;
import com.scube.coordinator.features.templates.dto.Query;
import com.scube.coordinator.features.templates.dto.Template;
import com.scube.coordinator.features.templates.dto.UpsertTemplateRequest;
import com.scube.documenttemplates.dto.gen_dto.TemplateDto;
import com.scube.report.features.base.dto.gen_dto.FormSectionDto;
import com.scube.report.features.base.dto.gen_dto.ReportTypeDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class TemplatesService {
    private final DocumentTemplateServiceConnection documentTemplateServiceConnection;
    private final ReportServiceConnection reportServiceConnection;
    private final IDocumentTemplateHttpExchange documentTemplateHttpExchange;
    private final ObjectMapper objectMapper;


    public GetAllTemplates getAllTemplates() {
        List<TemplateDto> templates = documentTemplateServiceConnection.documentTemplate().getAllTemplates();
        List<ReportTypeDto> reportTypes = reportServiceConnection.type().getAllReportTypes();
        return new GetAllTemplates(templates, reportTypes);
    }

    public Template getTemplate(UUID templateUUID) {
        TemplateDto template = documentTemplateServiceConnection.documentTemplate().getTemplate(templateUUID);
        ReportTypeDto reportType = getReportTypeDto(template.getNameKey());
        return new Template(template, reportType);
    }

    private ReportTypeDto getReportTypeDto(String nameKey) {
        List<ReportTypeDto> reportTypes = reportServiceConnection.type().getAllReportTypes();
        Map<String, ReportTypeDto> reportTypeMap = reportTypes.stream()
                .collect(HashMap::new, (map, reportType) -> map.put(reportType.getReportTemplateKey(), reportType), HashMap::putAll);
        // get the nameKey or if not exists get nameKey concatenated with "Excel"
        return Optional.ofNullable(reportTypeMap.get(nameKey))
                .or(() -> Optional.ofNullable(reportTypeMap.get(nameKey + "Excel")))
                .or(() -> Optional.ofNullable(reportTypeMap.get(nameKey.replace("Excel", ""))))
                .orElse(null);
    }

    public void upsertTemplate(UpsertTemplateRequest request) {
        TemplateDto template = null;
        ReportTypeDto reportType = null;
        if (Objects.nonNull(request.getTemplateUUID())) {
            template = updateTemplate(request);
        } else {
            template = createTemplate(request);
        }
        if (request.isReport()) {
            reportType = upsertReportType(request, template);
        }
    }

    private ReportTypeDto upsertReportType(UpsertTemplateRequest request, TemplateDto template) {
        var reportTypeDto = Optional.ofNullable(getReportTypeDto(template.getNameKey()))
                .orElse(new ReportTypeDto());
        reportTypeDto.setReportTemplateKey(template.getNameKey().replace("Excel", ""));
        if (!ObjectUtils.isEmpty(request.getQueries())) {
            var queriesMap = Query.createQueryMap(request.getQueries());
            reportTypeDto.setQueries(queriesMap);
        }
        if (!ObjectUtils.isEmpty(request.getFormData())) {
            reportTypeDto.setFormData(request.getFormData());
        }
        if (!ObjectUtils.isEmpty(request.getSectionName())) {
            reportTypeDto.setFormSection(new FormSectionDto(request.getSectionName(), null));
        }
        return reportServiceConnection.type().updateReportType(reportTypeDto);
    }

    private TemplateDto updateTemplate(UpsertTemplateRequest request) {
        TemplateDto template = documentTemplateServiceConnection.documentTemplate().getTemplate(request.getTemplateUUID());
        var updateReq = request.toUpdateTemplateRequest();
        return documentTemplateHttpExchange.updateTemplate(template.getNameKey(), updateReq);
    }

    private TemplateDto createTemplate(UpsertTemplateRequest request) {
        var newTemplateReq = request.toCreateTemplateRequest();
        return documentTemplateHttpExchange.createTemplate(newTemplateReq);
    }
}

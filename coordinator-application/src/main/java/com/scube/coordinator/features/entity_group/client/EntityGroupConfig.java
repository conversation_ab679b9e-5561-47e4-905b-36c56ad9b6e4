package com.scube.coordinator.features.entity_group.client;

import com.scube.client.config.ProxyBeanUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class EntityGroupConfig {
    private final WebClient webClient;

    public EntityGroupConfig(@Value("${com.scube.client.license}") String serviceUrl) {
        this.webClient = ProxyBeanUtil.createWebClient(serviceUrl);
    }

    @Bean
    public IEntityGroupExchange testExchangeEntityGroup() {
        return ProxyBeanUtil.createHttpProxyClient(this.webClient, IEntityGroupExchange.class);
    }
}

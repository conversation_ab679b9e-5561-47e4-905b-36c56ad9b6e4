package com.scube.coordinator.features.notification.license_form_notification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetLicenseDataQueryAsResult implements IRabbitFanoutPublisherRpc<Map<String, Object>> {
    private String licenseEntityId;

    @JsonProperty("formatData")
    private boolean formatData = true;
}
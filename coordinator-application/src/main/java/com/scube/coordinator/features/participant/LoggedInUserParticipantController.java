package com.scube.coordinator.features.participant;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.auth.library.enabled_true.keycloak.services.KeycloakUserService;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.permission.Permissions;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.security.RolesAllowed;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/me/participant")
@RequiredArgsConstructor
public class LoggedInUserParticipantController {
    private final KeycloakUserService keycloakUserService;
    private final LicenseServiceConnection license;

    @DeleteMapping
    @Operation(summary = "Delete a participant")
    @RolesAllowed(Permissions.LoggedInUserParticipant.DELETE_PARTICIPANT)
    public void deleteParticipant(@AuthenticationPrincipal MyOpenIdClaimSet jwt) {
        keycloakUserService.delete(jwt.getRealm(), jwt.getSubject());
        license.loggedInUserParticipant().markOfflineResident();
    }
}
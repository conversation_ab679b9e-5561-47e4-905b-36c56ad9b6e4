package com.scube.coordinator.features.fees.strategies;

import com.scube.calculation.dto.gen_dto.CartInvoiceItem;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.coordinator.features.fees.CartItemFeeUtils;
import com.scube.coordinator.features.fees.dto.AddFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.RemoveFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.UpdateFeeOnCartItemRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("tagCartItemStrategy")
@RequiredArgsConstructor
public class TagCartItemStrategy implements ICartItemStrategy {
    private final CalculationServiceConnection calculationServiceConnection;

    @Override
    public Object updateFee(CartInvoiceItem cartItem, UpdateFeeOnCartItemRequest request) {
        CartItemFeeUtils.updateFeeOnCartItem(calculationServiceConnection, cartItem, request);
        return null;
    }

    @Override
    public Object addFee(CartInvoiceItem cartItem, AddFeeOnCartItemRequest request) {
        CartItemFeeUtils.addFeeOnCartItem(calculationServiceConnection, cartItem.getCartItemId(), request);
        return null;
    }

    @Override
    public Object removeFee(CartInvoiceItem cartItem, RemoveFeeOnCartItemRequest request) {
        CartItemFeeUtils.removeFeeOnCartItem(calculationServiceConnection, cartItem, request);
        return null;
    }

}
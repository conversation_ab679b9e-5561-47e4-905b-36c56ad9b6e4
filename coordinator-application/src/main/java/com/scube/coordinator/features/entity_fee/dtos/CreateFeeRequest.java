package com.scube.coordinator.features.entity_fee.dtos;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateFeeRequest {
    private String groupName;
    private Set<Association> associations = new HashSet<>();
    private Set<FeeRequest> fees = new HashSet<>();

    @JsonAnySetter
    @JsonAnyGetter
    @JsonIgnore
    private Map<String, Object> properties = new HashMap<>();

    public boolean hasGroupName() {
        return !ObjectUtils.isEmpty(groupName);
    }

    public Map<String, Object> getEntityGroupMap() {
        var entityGroup = new HashMap<String, Object>();
        entityGroup.put("name", groupName);
        entityGroup.put("associations", associations);
        entityGroup.putAll(properties);
        return entityGroup;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class FeeRequest {
        private String feeCode;
        private BigDecimal amount;

        @JsonAnySetter
        @JsonAnyGetter
        @JsonIgnore
        private Map<String, Object> properties = new HashMap<>();

        private Set<Association> associations = new HashSet<>();

        public void addAssociation(Association association) {
            associations.add(association);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static final class Association {
        private String entityType;
        private UUID entityId;

        @JsonAnySetter
        @JsonAnyGetter
        @JsonIgnore
        private Map<String, Object> properties = new HashMap<>();

        public static Association createEntityGroupAssociation(UUID entityId) {
            var association = new Association();
            association.setEntityType("entityGroup");
            association.setEntityId(entityId);
            return association;
        }
    }
}
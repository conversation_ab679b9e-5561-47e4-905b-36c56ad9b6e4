package com.scube.coordinator.features.cart;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.cart.dto.AddItemToCartRequest;
import com.scube.coordinator.features.cart.dto.AddItemToCartResponse;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("me/cart")
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
public class LoggedInUserCartController {
    private final CartService cartService;

    @PostMapping("/{cartId}/add")
    @ResponseStatus(HttpStatus.CREATED)
    @RolesAllowed(Permissions.LoggedInUserCart.ADD_TO_CART)
    public AddItemToCartResponse addToCart(@PathVariable("cartId") UUID cartId, @Valid @RequestBody AddItemToCartRequest addItemToCartRequest) {
        return cartService.addToCart(cartId, addItemToCartRequest, true);
    }

    @GetMapping("/{cartId}/infer-payee")
    @ResponseStatus(HttpStatus.OK)
    @RolesAllowed(Permissions.LoggedInUserCart.INFER_PAYEE)
    public ResponseEntity<?> inferPayee(@PathVariable("cartId") UUID cartId) {
        return ResponseEntity.ok(cartService.inferPayee(cartId, true));
    }
}
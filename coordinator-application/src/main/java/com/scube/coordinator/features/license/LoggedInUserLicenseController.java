package com.scube.coordinator.features.license;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.license.dto.GenerateLicenseFormRequest;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("me/license")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
@Validated
public class LoggedInUserLicenseController {
    private final LicenseService licenseService;

    @PostMapping("generate-form")
    @RolesAllowed(Permissions.LoggedInUserLicense.GENERATE_FORM)
    public Map<String, String> generateForm(@RequestBody @Valid GenerateLicenseFormRequest request) {
        var documentId = licenseService.generateForm(request);
        return Map.of("url", documentId);
    }
}
package com.scube.coordinator.features.entity_fee.dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddFeeToCartRequest {
    private String feeCode;
    private BigDecimal feeAmount;

    public AddFeeToCartRequest(UpdateFeeRequest request) {
        this.feeCode = request.getKey();
        this.feeAmount = request.getAmount();
    }
}

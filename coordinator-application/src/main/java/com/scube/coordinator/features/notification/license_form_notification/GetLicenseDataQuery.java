package com.scube.coordinator.features.notification.license_form_notification;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.UUID;

@NoArgsConstructor
@AllArgsConstructor
@Data
public class GetLicenseDataQuery implements IRabbitFanoutPublisherRpc<Map<String, Object>> {
    private UUID licenseEntityId;

    @JsonProperty("formatData")
    private boolean formatData = true;
}

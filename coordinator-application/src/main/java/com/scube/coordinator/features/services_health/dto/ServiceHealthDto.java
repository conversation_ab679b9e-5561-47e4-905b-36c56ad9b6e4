package com.scube.coordinator.features.services_health.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ServiceHealthDto {
    @JsonProperty(value = "isOnline")
    private boolean isOnline;

    @JsonProperty(value = "name")
    private String serviceName;
    private String description;

    public ServiceHealthDto(String isOnline, String serviceName, String description) {
        //check if status is online or offline
        this.isOnline = isOnline.equals("UP");
        this.serviceName = serviceName;
        this.description = description;
    }
}


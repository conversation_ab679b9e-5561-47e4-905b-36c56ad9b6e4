package com.scube.coordinator.features.demo_data;

import com.scube.licensing.features.license.gen_dto.CreateLicenseResponse;
import com.scube.licensing.features.participant.dto.gen_dto.CreateParticipantResponseDTO;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.PostExchange;

import java.util.UUID;

//@HttpExchangeWebClient("com.scube.client.license")
public interface LicenseServiceHttpExchange {
    @PostExchange(
            value = "/participant",
            contentType = "multipart/form-data")
    CreateParticipantResponseDTO createResident(@RequestBody LinkedMultiValueMap<String, String> fields);

    @PostExchange(
            value = "/license/create",
            contentType = "multipart/form-data"
    )
    CreateLicenseResponse createLicense(@RequestParam UUID participantId,
                                        @RequestParam String licenseType,
                                        @RequestBody LinkedMultiValueMap<String, String> fields,
                                        @RequestParam Integer duration);

}
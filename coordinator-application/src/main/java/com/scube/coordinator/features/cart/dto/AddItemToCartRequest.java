package com.scube.coordinator.features.cart.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AddItemToCartRequest {
    @NotBlank
    private String itemType;
    @NotNull
    private UUID itemId;
}
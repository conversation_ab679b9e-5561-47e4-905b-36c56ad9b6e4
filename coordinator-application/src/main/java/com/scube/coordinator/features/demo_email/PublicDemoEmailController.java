package com.scube.coordinator.features.demo_email;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/public/demo-email")
@RequiredArgsConstructor
@Validated
public class PublicDemoEmailController {
    private final DemoEmailService demoEmailService;

    @PostMapping("send")
    public void sendDemoEmail(@RequestBody DemoEmailRequest request) {
        demoEmailService.sendDemoEmail(request);
    }
}
package com.scube.coordinator.features.history;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.coordinator.features.history.dto.HistoryDto;
import com.scube.coordinator.features.permission.Permissions;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("me/history")
@Slf4j
@RequiredArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.COORDINATOR_SERVICE)
@Validated
public class LoggedInUserHistoryController {
    private final HistoryService service;

    @GetMapping(path = "transaction/license/{entityId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @RolesAllowed(Permissions.LoggedInUserHistory.GET_TRANSACTIONS)
    @ResponseStatus(HttpStatus.OK)
    public HistoryDto getTransactions(@PathVariable UUID entityId,
                                      @RequestParam(required = false) @Size(max = 255) String startDate,
                                      @RequestParam(required = false) @Size(max = 255) String endDate,
                                      @RequestParam(required = false) @Size(max = 255) String status) {
        return new HistoryDto(service.getTransactionsByEntityId(entityId, true));
    }
}
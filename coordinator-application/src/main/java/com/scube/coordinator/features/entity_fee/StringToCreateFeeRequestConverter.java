package com.scube.coordinator.features.entity_fee;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.coordinator.features.entity_fee.dtos.CreateFeeRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.core.convert.converter.Converter;
import org.springframework.http.HttpStatus;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

@Component
@RequiredArgsConstructor
public class StringToCreateFeeRequestConverter implements Converter<String, CreateFeeRequest> {
    private final ObjectMapper objectMapper;

    @Override
    public CreateFeeRequest convert(@Nullable String source) {
        if (ObjectUtils.isEmpty(source))
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "json key is empty");
        try {
            return objectMapper.readValue(source, CreateFeeRequest.class);
        } catch (JsonProcessingException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid request format: " + e.getMessage());
        }
    }
}
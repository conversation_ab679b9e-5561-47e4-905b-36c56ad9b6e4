package com.scube.coordinator.features.demo_data;

import com.scube.client.config.ProxyBeanUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class DemoDataConfig {
    private final WebClient webClient;

    public DemoDataConfig(@Value("${com.scube.client.license}") String serviceUrl) {
        this.webClient = ProxyBeanUtil.createWebClient(serviceUrl);
    }

    @Bean
    public LicenseServiceHttpExchange licenseServiceHttpExchange() {
        return ProxyBeanUtil.createHttpProxyClient(this.webClient, LicenseServiceHttpExchange.class);
    }
}

package com.scube.coordinator.features.notification;

import com.scube.config_utils.json_storage.JsonStorageValue;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class TenantService {
    @JsonStorageValue({"config=tenant"})
    private TenantDto tenantDto;

    public TenantDto getTenant() {
        return tenantDto;
    }
}

package com.scube.coordinator.features.fees.strategies;

import com.scube.calculation.dto.gen_dto.CartInvoiceItem;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.coordinator.features.fees.CartItemFeeUtils;
import com.scube.coordinator.features.fees.dto.AddFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.RemoveFeeOnCartItemRequest;
import com.scube.coordinator.features.fees.dto.UpdateFeeOnCartItemRequest;
import com.scube.licensing.features.entity_fee.dtos.gen_dto.EntityFeeRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service("entityFeeCartItemStrategy")
@RequiredArgsConstructor
public class EntityFeeCartItemStrategy implements ICartItemStrategy {
    private final LicenseServiceConnection licenseServiceConnection;
    private final CalculationServiceConnection calculationServiceConnection;

    @Override
    public Object updateFee(CartInvoiceItem cartItem, UpdateFeeOnCartItemRequest request) {
        var entityFeeId = CartItemFeeUtils.getEntityFeeId(request.getFeeId(), cartItem);
        if (!ObjectUtils.isEmpty(entityFeeId)) {
            var entityFeeRequest = new EntityFeeRequest();
            entityFeeRequest.setAmount(request.getFeeAmount());
            entityFeeRequest.setComment(request.getReason());
            licenseServiceConnection.entityFee().updateFee(entityFeeId, entityFeeRequest);
        }

        CartItemFeeUtils.updateFeeOnCartItem(calculationServiceConnection, cartItem, request);
        return null;
    }

    @Override
    public Object addFee(CartInvoiceItem cartItem, AddFeeOnCartItemRequest request) {
        CartItemFeeUtils.addFeeOnCartItem(calculationServiceConnection, cartItem.getCartItemId(), request);
        return null;
    }

    @Override
    public Object removeFee(CartInvoiceItem cartItem, RemoveFeeOnCartItemRequest request) {
        var entityFeeId = CartItemFeeUtils.getEntityFeeId(request.getFeeId(), cartItem);
        if (!ObjectUtils.isEmpty(entityFeeId)) {
            licenseServiceConnection.entityFee().deleteFee(entityFeeId);
            calculationServiceConnection.cart().removeItem(cartItem.getCartId(), cartItem.getCartItemId());
        } else {
            calculationServiceConnection.cart().removeCartItemFee(request.getFeeId());
        }

        return null;
    }
}
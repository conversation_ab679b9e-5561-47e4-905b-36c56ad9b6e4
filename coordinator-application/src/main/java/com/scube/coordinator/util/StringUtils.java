package com.scube.coordinator.util;

public class StringUtils {
    public static String camelCaseToCapitalizedWords(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        StringBuilder result = new StringBuilder(input.length());
        int inputLength = input.length();

        result.append(Character.toUpperCase(input.charAt(0)));

        for (int i = 1; i < inputLength; i++) {
            char currentChar = input.charAt(i);

            if (Character.isUpperCase(currentChar)) {
                result.append(' ').append(currentChar);
            } else {
                result.append(currentChar);
            }
        }

        return result.toString();
    }
}

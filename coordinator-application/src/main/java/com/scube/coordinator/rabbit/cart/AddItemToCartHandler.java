package com.scube.coordinator.rabbit.cart;


import com.scube.coordinator.features.cart.CartService;
import com.scube.coordinator.features.cart.dto.AddItemToCartRequest;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class AddItemToCartHandler extends FanoutListener<AddItemToCartHandler.AddItemToCartEvent> {
    private final CartService cartService;

    @Override
    public void consume(AddItemToCartEvent event) {
        cartService.addToCart(event.cartId, new AddItemToCartRequest(event.itemType, event.itemId), event.isMe);
    }

    @Data
    @NoArgsConstructor
    public static class AddItemToCartEvent implements IRabbitFanoutSubscriber {
        private String itemType;
        private UUID itemId;
        private UUID cartId;
        private boolean isMe;
    }
}


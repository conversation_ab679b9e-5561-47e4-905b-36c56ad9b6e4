package com.scube.coordinator.rabbit.shared;

import com.scube.coordinator.features.services_health.dto.ActuatorHealthDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

public interface HealthCheckFeign {
    @GetMapping("actuator/health")
    ActuatorHealthDto getActuatorHealth(@RequestHeader("Authorization") String bearerToken);
}
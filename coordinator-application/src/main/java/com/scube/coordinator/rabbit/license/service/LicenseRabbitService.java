package com.scube.coordinator.rabbit.license.service;

import com.scube.coordinator.features.reports.dto.LicenseProjection;
import com.scube.coordinator.rabbit.license.exception.LicenseServiceException;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisherRpc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.UUID;

@Service
@AllArgsConstructor
public class LicenseRabbitService {
    private final AmqpGateway amqpGateway;

    public LicenseProjection getLicense(UUID entityId) {
        var rabbitResult = amqpGateway.queryResult(new GetLicenseProjectionQuery(entityId));

        if (rabbitResult == null) {
            throw new LicenseServiceException(
                    "Failed to retrieve report data: Rabbit result was null");
        } else if (!rabbitResult.isSuccess()) {
            throw new LicenseServiceException(
                    "Failed to retrieve report data with error:" + rabbitResult.getErrorMessage());
        } else if (rabbitResult.getResult() == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        return rabbitResult.getResult();
    }

    public List<LicenseProjection> getLicenses(UUID entityId, String entityType) {
        var rabbitResult = amqpGateway.queryResult(new GetLicenseIdByParticipantQuery(entityId, entityType));

        if (rabbitResult == null) {
            throw new LicenseServiceException(
                    "Failed to retrieve report data: Rabbit result was null");
        } else if (!rabbitResult.isSuccess()) {
            throw new LicenseServiceException(
                    "Failed to retrieve report data with error:" + rabbitResult.getErrorMessage());
        } else if (rabbitResult.getResult() == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "License not found");
        }

        return rabbitResult.getResult().getLicenses();
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetLicenseProjectionQuery implements IRabbitFanoutPublisherRpc<LicenseProjection> {
        private UUID entityId;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetLicenseIdByParticipantQuery implements IRabbitFanoutPublisherRpc<GetLicenseIdByParticipantQueryResult> {
        private UUID participantEntityId;
        private String participantGroupType;
    }

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class GetLicenseIdByParticipantQueryResult {
        private List<LicenseProjection> licenses;
    }
}
package com.scube.coordinator.rabbit.license.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class LicenseServiceException extends RuntimeException {
    public LicenseServiceException(String message) {
        super(message);
    }

    public LicenseServiceException(String message, Throwable cause) {
        super(message, cause);
    }
}

package com.scube.coordinator;

import com.scube.rabbit.core.annotation.EnableRabbitMQLibrary;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.web.config.EnableSpringDataWebSupport;

@SpringBootApplication
@EnableRabbitMQLibrary(additionalPackages = "com.scube")
@OpenAPIDefinition(info = @Info(title = "Coordinator Service API", version = "1.0", description = "Swagger Documentation"))
@EnableSpringDataWebSupport
public class ServiceCoordinatorApplication {

    public static void main(String[] args) {
        SpringApplication.run(ServiceCoordinatorApplication.class, args);
    }
}

server:
  port: 10004
  servlet:
    context-path: /api/coordinator
  compression:
    enabled: true
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: never
    include-exception: false
    include-client-error-message: true
    include-server-error-message: false
    send-client-error-email: true
    send-server-error-email: true
  forward-headers-strategy: framework


logging:
  pattern:
    level: "%5p [tenantId=%X{tenantId:-}], [%X{traceId:-},%X{spanId:-}] [user=%X{userEmail:-}]"
  level:
    org.springframework.web: "info"

spring:
  application:
    name: CoordinatorService
  threads:
    virtual:
      enabled: false
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB




springdoc:
  show-actuator: true
  swagger-ui:
    enabled: false
    filter: true

keycloak:
  host: http://keycloak.keycloak.svc.cluster.local:8080
  public-host: https://auth-staging.clerkxpress.com
  admin:
    url: ${keycloak.host}
    realm: master
    client-id: ${KEYCLOAK_ADMIN_CLIENT_ID}
    client-secret: ${KEYCLOAK_ADMIN_CLIENT_SECRET}
  swagger:
    url: ${keycloak.public-host}
    realm: clerkXpress
    client-id: ${KEYCLOAK_SWAGGER_AUTH_CLIENT_ID}

com.c4-soft.springaddons.oidc:
  ops:
    - iss: ${keycloak.host}
      jwkSetUri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
    - iss: ${keycloak.public-host}
      jwk-set-uri: ${keycloak.host}
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - https://staging.clerkxpress.com

multi-tenancy:
  enabled: true
  async:
    enabled: true
  caching:
    enabled: true
  keycloak:
    enabled: true
  management:
    enabled: true
  rabbitmq:
    enabled: true
    host: "rabbitmq.backend.svc.cluster.local"
    port: 5672
  database:
    enabled: false

document-storage:
  download:
    url: "https://staging.clerkxpress.com/api/document-service/download?documentUUID="

# When enabled = true, the text extraction service will use ai and ocr services to extract text from images
# When enabled = false, the text extraction service will return empty json
application:
  text-extractor:
    enabled: true

com.scube.client:
  ai: "http://scube-ai-service-srv:9011/api/ai"
  auth: "http://scube-auth-service-srv:9001/api/auth"
  config: "http://scube-config-service-srv:10001/api/config"
  calculation: "http://scube-calculation-service-srv:9002/api/calculation"
  document: "http://scube-document-service-srv:9003/api/document-service"
  documentTemplate: "http://scube-document-template-service-srv:9009/api/document-template"
  documentTemplateHelper: "http://scube-document-template-helper-service-srv:9012/api/document-template-helper"
  imageProcessing: "http://scube-imageprocessing-service-srv:9010/api/image-processing"
  license: "http://scube-license-service-srv:9004/api/license"
  notification: "http://scube-notification-service-srv:9005/api/notification"
  ocr: "http://scube-ocr-service-srv:9008/api/ocr"
  payment: "http://scube-payment-service-srv:9006/api/payment"
  report: "http://scube-report-service-srv:9007/api/report"
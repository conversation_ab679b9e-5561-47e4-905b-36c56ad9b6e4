package com.scube.coordinator.features.text_extractor.controller;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.ai.dto.gen_dto.MapTextRequest;
import com.scube.ai.dto.gen_dto.MapTextResponse;
import com.scube.ai.generated.IMapTextControllerHttpExchangeProxy;
import com.scube.coordinator.SharedTestConfig;
import com.scube.coordinator.features.permission.Permissions;

import com.scube.imageprocessing.dto.gen_dto.ExtractTextFromImageResponse;
import com.scube.imageprocessing.generated.IImageProcessingControllerHttpExchangeProxy;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MvcResult;

import static com.scube.coordinator.permissions.MockMvcHelper.END_JSON;
import static com.scube.coordinator.permissions.MockMvcHelper.START_JSON;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.multipart;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

class TextExtractorControllerTest extends SharedTestConfig {

    @MockBean
    private IMapTextControllerHttpExchangeProxy aiServiceConnection;

    @MockBean
    private IImageProcessingControllerHttpExchangeProxy imageProcessing;

    @Test
    @WithJwt(json = START_JSON + Permissions.TextExtractor.PROCESS_FILE + END_JSON)
    void processFile_should_return_response_body() throws Exception {
        // given
        var aiResponse = new MapTextResponse();

        ResponseEntity<MapTextResponse> expectedResponse = new ResponseEntity<>(aiResponse, HttpStatus.OK);

        when(imageProcessing.extractTextFromImage(any(MockMultipartFile.class)))
                .thenReturn(new ExtractTextFromImageResponse("some unparsed text"));

        when(aiServiceConnection.parseOcrText(any(MapTextRequest.class)))
                .thenReturn(aiResponse);

        MvcResult result = mockMvc.perform(multipart("/textextractor")
                        .file("file", new byte[0])
                        .param("fileIdentifier", "dogLicense-front")
                )
                // then
                .andExpect(status().isOk())
                .andReturn();
    }
}
package com.scube.websocket;

import com.scube.auth.library.ITokenService;
import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.multi.tenant.TenantContext;
import com.scube.multi.tenant.tenancy.keycloak.ITenantResolver;
import lombok.Getter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;


import java.util.List;

/**
 * WebSocket Authentication Interceptor
 * <p>
 * This interceptor secures WebSocket connections by handling authentication and tenant context
 * management for multi-tenant applications.
 * <p>
 * Key Responsibilities:
 * - Authenticates users via JWT tokens during WebSocket connection
 * - Resolves and maintains tenant context throughout the session
 * - Validates ongoing requests to ensure security
 * - Manages session cleanup on disconnect
 * <p>
 * Connection Flow:
 * 1. CONNECT: Validates Bearer token, authenticates user, resolves tenant, stores session data
 * 2. MESSAGE/SEND: Validates session state and token, maintains tenant context
 * 3. DISCONNECT: Cleans up session attributes and tenant context
 * <p>
 * Security Features:
 * - Bearer token validation from Authorization header
 * - Session-based authentication state management
 * - Tenant isolation for multi-tenant security
 * - Automatic token expiration handling
 * <p>
 * Error Handling:
 * - Throws WebSocketAuthenticationException for authentication failures
 * - Comprehensive logging for debugging and monitoring
 * - Graceful cleanup on errors and disconnections
 */
@Component
@RequiredArgsConstructor
@Getter
@Slf4j
public class AuthChannelInterceptor implements ChannelInterceptor {
    private static final String TENANT_ID_KEY = "tenantId";
    private static final String CLAIM_SET_KEY = "claimSet";
    private static final String AUTH_TOKEN_KEY = "authToken";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String AUTHORIZATION_HEADER = "Authorization";

    private final ITokenService tokenService;
    private final ITenantResolver tenantResolver;

    @Override
    public Message<?> preSend(@NonNull Message<?> message, @NonNull MessageChannel channel) {
        StompHeaderAccessor accessor =
                MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);

        if (accessor != null) {
            StompCommand command = accessor.getCommand();

            try {
                if (StompCommand.CONNECT.equals(command)) {
                    handleConnect(accessor);
                } else if (StompCommand.SEND.equals(command) || StompCommand.MESSAGE.equals(command)) {
                    handleMessage(accessor);
                } else if (StompCommand.DISCONNECT.equals(command)) {
                    handleDisconnect(accessor);
                }
            } catch (Exception e) {
                log.error("Error processing WebSocket command: {} for session: {}",
                        command, accessor.getSessionId(), e);
                throw e;
            }
        }

        return message;
    }

    private void handleConnect(StompHeaderAccessor accessor) {
        try {
            String authToken = extractAndValidateToken(accessor);

            if (!tokenService.authenticate(authToken)) {
                throw new WebSocketAuthenticationException("Token authentication failed");
            }

            MyOpenIdClaimSet claimSet = tokenService.getLoggedInUserInfo();

            String tenantId = tenantResolver.resolve();
            if (tenantId == null || tenantId.trim().isEmpty()) {
                throw new WebSocketAuthenticationException("Unable to resolve tenant");
            }

            storeSessionAttributes(accessor, tenantId, claimSet, authToken);

            TenantContext.setTenantId(tenantId);

            log.info("WebSocket connected for user: {}, tenant: {}, session: {}",
                    claimSet.getSubject(), tenantId, accessor.getSessionId());

            Authentication user = new UsernamePasswordAuthenticationToken(
                    claimSet.getSubject(), null, List.of());
            accessor.setUser(user);

        } catch (Exception e) {
            log.error("WebSocket connection failed for session: {}", accessor.getSessionId(), e);
            throw e;
        }
    }

    private void handleMessage(StompHeaderAccessor accessor) {
        if (accessor.getSessionAttributes() != null) {
            String tenantId = (String) accessor.getSessionAttributes().get(TENANT_ID_KEY);
            MyOpenIdClaimSet claimSet = (MyOpenIdClaimSet) accessor.getSessionAttributes().get(CLAIM_SET_KEY);

            if (tenantId != null && claimSet != null) {
                TenantContext.setTenantId(tenantId);

                validateSessionToken(accessor);

                log.debug("Message received for user: {}, tenant: {}, session: {}",
                        claimSet.getSubject(), tenantId, accessor.getSessionId());
            } else {
                log.warn("Missing session attributes for session: {}", accessor.getSessionId());
                throw new WebSocketAuthenticationException("Invalid session state");
            }
        } else {
            log.warn("No session attributes found for session: {}", accessor.getSessionId());
            throw new WebSocketAuthenticationException("No session found");
        }
    }

    private void handleDisconnect(StompHeaderAccessor accessor) {
        try {
            if (accessor.getSessionAttributes() != null) {
                String tenantId = (String) accessor.getSessionAttributes().get(TENANT_ID_KEY);
                MyOpenIdClaimSet claimSet = (MyOpenIdClaimSet) accessor.getSessionAttributes().get(CLAIM_SET_KEY);

                if (claimSet != null) {
                    log.info("WebSocket disconnected for user: {}, tenant: {}, session: {}",
                            claimSet.getSubject(), tenantId, accessor.getSessionId());
                }

                accessor.getSessionAttributes().clear();
            }
        } catch (Exception e) {
            log.error("Error during WebSocket disconnect for session: {}", accessor.getSessionId(), e);
        } finally {
            TenantContext.clear();
        }
    }

    private String extractAndValidateToken(StompHeaderAccessor accessor) {
        String authHeader = accessor.getFirstNativeHeader(AUTHORIZATION_HEADER);

        if (authHeader == null || authHeader.trim().isEmpty()) {
            throw new WebSocketAuthenticationException("Missing authorization header");
        }

        if (!authHeader.startsWith(BEARER_PREFIX)) {
            throw new WebSocketAuthenticationException("Invalid authorization header format");
        }

        String token = authHeader.substring(BEARER_PREFIX.length()).trim();
        if (token.isEmpty()) {
            throw new WebSocketAuthenticationException("Empty token");
        }

        return token;
    }

    private void storeSessionAttributes(StompHeaderAccessor accessor, String tenantId,
                                        MyOpenIdClaimSet claimSet, String authToken) {
        accessor.getSessionAttributes().put(TENANT_ID_KEY, tenantId);
        accessor.getSessionAttributes().put(CLAIM_SET_KEY, claimSet);
        accessor.getSessionAttributes().put(AUTH_TOKEN_KEY, authToken);
    }

    private void validateSessionToken(StompHeaderAccessor accessor) {
        String authToken = (String) accessor.getSessionAttributes().get(AUTH_TOKEN_KEY);
        if (authToken != null) {
            if (!tokenService.authenticate(authToken)) {
                throw new WebSocketAuthenticationException("Token expired");
            }
        }
    }


    public static class WebSocketAuthenticationException extends RuntimeException {
        public WebSocketAuthenticationException(String message) {
            super(message);
        }
    }
}
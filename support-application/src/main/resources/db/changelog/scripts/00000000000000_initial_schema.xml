<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="
       http://www.liquibase.org/xml/ns/dbchangelog
       http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.4.xsd">

    <changeSet id="create-chat-and-audit-log-table" author="ajay">
        <!-- Create chat table -->
        <createTable tableName="chat">
            <column name="chat_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_chat_id"/>
            </column>
            <column name="title" type="TEXT"/>
            <column name="description" type="TEXT"/>
            <column name="status" type="TEXT"/>
            <column name="chat_uuid" type="UUID">
                <constraints nullable="false" unique="true" uniqueConstraintName="uk_chat_uuid"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
        </createTable>

        <!-- Create audit log chat table -->
        <createTable tableName="audit_log_chat">
            <column name="chat_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_audit_chat_id"/>
            </column>
            <column name="title" type="TEXT"/>
            <column name="description" type="TEXT"/>
            <column name="status" type="TEXT"/>
            <column name="chat_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_chat_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
        </createTable>
    </changeSet>

    <changeSet id="create-participant-and-audit-log-table" author="ajay">
        <!-- Create participant table -->
        <createTable tableName="participant">
            <column name="participant_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_participant_id"/>
            </column>
            <column name="chat_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="participant_uuid" type="UUID">
                <constraints nullable="false" unique="true" uniqueConstraintName="uk_participant_uuid"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
        </createTable>

        <addForeignKeyConstraint
                baseTableName="participant"
                baseColumnNames="chat_id"
                referencedTableName="chat"
                referencedColumnNames="chat_id"
                constraintName="fk_participant_chat"/>


        <!-- Create audit log participant table -->
        <createTable tableName="audit_log_participant">
            <column name="participant_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_audit_participant_id"/>
            </column>
            <column name="chat_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="participant_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_participant_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
        </createTable>

    </changeSet>

    <changeSet id="create-message-and-audit-log-table" author="ajay">
        <!-- Create message table -->
        <createTable tableName="message">
            <column name="message_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_message_id"/>
            </column>
            <column name="chat_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="sender_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="content" type="TEXT"/>
            <column name="status" type="TEXT"/>
            <column name="reply_to_message_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="message_uuid" type="UUID">
                <constraints nullable="false" unique="true" uniqueConstraintName="uk_message_uuid"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
        </createTable>

        <addForeignKeyConstraint
                baseTableName="message"
                baseColumnNames="chat_id"
                referencedTableName="chat"
                referencedColumnNames="chat_id"
                constraintName="fk_message_chat"/>

        <addForeignKeyConstraint
                baseTableName="message"
                baseColumnNames="reply_to_message_id"
                referencedTableName="message"
                referencedColumnNames="message_id"
                constraintName="fk_message_reply_to"/>


        <!-- Create audit log message table -->
        <createTable tableName="audit_log_message">
            <column name="message_id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false" primaryKeyName="pk_audit_message_id"/>
            </column>
            <column name="chat_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="sender_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="content" type="TEXT"/>
            <column name="status" type="TEXT"/>
            <column name="reply_to_message_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="message_uuid" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_message_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
        </createTable>
    </changeSet>

    <changeSet author="ajay" id="create-audit-log-revision-table">
        <createTable tableName="audit_log_revision">
            <column name="audit_log_revision_id" type="INTEGER" autoIncrement="true">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_revision_pkey"/>
            </column>
            <column name="timestamp" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="auditor" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

</databaseChangeLog>

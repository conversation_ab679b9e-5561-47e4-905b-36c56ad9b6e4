package com.scube.support.service;

import com.scube.support.dto.ChatMessage;
import com.scube.support.mapper.MessageMapper;
import com.scube.support.model.Message;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChatMessageDispatcher {
    private final SimpMessagingTemplate messagingTemplate;
    private final MessageMapper messageMapper;

    public void dispatchToAdmin(Message message) {
        messagingTemplate.convertAndSend(
                "/topic/admin/messages",
                messageMapper.toDto(message, "")
        );
    }

    public void dispatchToClerk(ChatMessage messageReceived, String userId, Message message) {
        messagingTemplate.convertAndSendToUser(
                messageReceived.getReceiverId(),
                "/queue/messages",
                messageMapper.toDto(message, userId)
        );
    }
}

package com.scube.support.service;

import com.scube.support.dto.ChatDto;
import com.scube.support.dto.ChatResponseDto;
import com.scube.support.dto.ChatStatusCountDTO;
import com.scube.support.dto.CreateChatDto;
import com.scube.support.enums.ChatStatus;
import com.scube.support.enums.ChatType;
import com.scube.support.mapper.ChatMapper;
import com.scube.support.model.Chat;
import com.scube.support.model.Participant;
import com.scube.support.repository.ChatRepository;
import com.scube.support.repository.ParticipantRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class ChatManagementService {
    private final ChatRepository chatRepository;
    private final ChatMapper chatMapper;
    private final ParticipantRepository participantRepository;

    public ChatResponseDto getAllChats(UUID userId, ChatStatus status, Pageable pageable) {
        Page<Chat> chats = getFilteredChats(userId, status, pageable);
        Page<ChatDto> chatDto = chats.map(chat -> chatMapper.toDto(chat, userId));
        ChatStatusCountDTO metadata = chatRepository.countChatsByStatus(userId);
        return new ChatResponseDto(chatDto, metadata);
    }

    private Page<Chat> getFilteredChats(UUID userId, ChatStatus status, Pageable pageable) {
        return switch (status) {
            case JOINED -> chatRepository.findByParticipants_UserIdAndStatusNot(userId, ChatType.CLOSED, pageable);
            case PENDING -> chatRepository.findByStatusNotAndNotAssociatedWithUser(userId, ChatType.CLOSED, pageable);
            case CLOSED -> chatRepository.findByStatus(ChatType.CLOSED, pageable);
        };
    }

    @Transactional
    public Participant createChat(UUID userId, String userName) {
        Chat chat = chatMapper.createChatDtoToChat(new CreateChatDto());
        chat.setUuid(userId);
        chat.setTitle(userName);
        chatRepository.save(chat);
        return createParticipantToChat(userId, chat);
    }

    public Participant createParticipantToChat(UUID userId, Chat chat) {
        Participant participant = new Participant(chat, userId);
        participantRepository.save(participant);
        return participant;
    }

    @Transactional
    public void resolveChat(UUID chatId) {
        Chat chat = chatRepository.findByUuid(chatId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Chat not found with UUID: " + chatId));

        chat.setStatus(ChatType.CLOSED);
        chatRepository.save(chat);
    }

    @Transactional
    public void joinConversation(UUID chatId, UUID currentUserId) {
        Chat chat = chatRepository.findByUuid(chatId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Chat not found"));

        boolean alreadyJoined = participantRepository.existsByUserIdAndChatUuid(currentUserId, chatId);
        if (alreadyJoined) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, "User already joined the conversation");
        }

        Participant participant = new Participant();
        participant.setUserId(currentUserId);
        participant.setChat(chat);
        participantRepository.save(participant);
    }

    public Participant createChatIfNotExists(UUID userId, String userName) {
        return participantRepository.findFirstByUserIdOrderByIdDesc(userId)
                .orElseGet(() -> this.createChat(userId, userName));

    }
}

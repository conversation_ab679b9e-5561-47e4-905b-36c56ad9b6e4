package com.scube.support.service;

import com.scube.support.dto.MessageDto;
import com.scube.support.mapper.MessageMapper;
import com.scube.support.model.Message;
import com.scube.support.repository.MessageRepository;
import com.scube.support.repository.ParticipantRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Service
@Slf4j
@RequiredArgsConstructor
public class MessageService {

    private final MessageRepository messageRepository;
    private final MessageMapper messageMapper;
    private final ParticipantRepository participantRepository;

    public Page<MessageDto> getConversation(UUID chatId, String userId, int pageNumber, int pageSize) {
        Pageable pageable = PageRequest.of(
                Math.max(0, pageNumber - 1),
                pageSize,
                Sort.by(Sort.Direction.DESC, "createdDate")
        );

        Page<Message> messages = messageRepository.findByChat_Uuid(chatId, pageable);
        List<MessageDto> dtos = messages.map(msg -> messageMapper.toDto(msg, userId)).getContent();

        return new PageImpl<>(dtos, pageable, messages.getTotalElements());
    }

    public Page<MessageDto> getConversation(String userId, int pageNumber, int pageSize) {
        return participantRepository
                .findFirstByUserIdOrderByIdDesc(UUID.fromString(userId))
                .map(participant -> {
                    Pageable pageable = PageRequest.of(
                            Math.max(0, pageNumber - 1),
                            pageSize,
                            Sort.by(Sort.Direction.DESC, "createdDate")
                    );

                    Page<Message> messages = messageRepository.findByChat_Uuid(participant.getChat().getUuid(), pageable);
                    List<MessageDto> messagesDto= messages.map(msg -> messageMapper.toDto(msg, userId)).getContent();

                    return new PageImpl<>(messagesDto, pageable, messages.getTotalElements());
                })
                .orElseGet(() -> new PageImpl<>(List.of(), PageRequest.of(pageNumber - 1, pageSize), 0));
    }

    public void addMessageToChat(Message message) {
        messageRepository.save(message);
    }
}

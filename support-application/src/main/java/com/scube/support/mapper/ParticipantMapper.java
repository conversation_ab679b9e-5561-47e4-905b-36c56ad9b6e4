package com.scube.support.mapper;

import com.scube.support.dto.ChatDto;
import com.scube.support.dto.ParticipantDto;
import com.scube.support.model.Chat;
import com.scube.support.model.Participant;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public abstract class ParticipantMapper {
    @Mapping(target = "participantUuid", source = "uuid")
    public abstract ParticipantDto toDto(Participant participant);
}

package com.scube.support.mapper;

import com.scube.support.dto.CreateChatDto;
import com.scube.support.dto.ChatDto;
import com.scube.support.model.Chat;
import com.scube.support.model.Message;
import com.scube.support.repository.MessageRepository;
import lombok.extern.slf4j.Slf4j;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Optional;
import java.util.UUID;

@Mapper(componentModel = "spring")
@Slf4j
public abstract class ChatMapper {

    @Autowired
    protected MessageRepository messageRepository;

    @Autowired
    protected MessageMapper messageMapper;

    @Mapping(target = "id", source = "uuid")
    @Mapping(target = "lastMessage", ignore = true)
    @Mapping(target = "lastModified", expression = "java(formatInstant(chat.getLastModifiedDate()))")
    public abstract ChatDto toDto(Chat chat, @Context UUID userId);

    @AfterMapping
    protected void enrichWithLastMessage(Chat chat, @MappingTarget ChatDto dto, @Context UUID userId) {
        Optional<Message> lastMessage = messageRepository.findFirstByChatUuidOrderByCreatedDateDesc(chat.getUuid());
        lastMessage.map(message -> messageMapper.toDto(message, userId.toString())).ifPresent(dto::setLastMessage);
    }

    public abstract Chat createChatDtoToChat(CreateChatDto createChatDto);

    String formatInstant(Instant instant) {
        if (instant == null) return null;

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("hh:mm a")
                .withLocale(Locale.US)
                .withZone(ZoneId.systemDefault());

        return formatter.format(instant);
    }
}

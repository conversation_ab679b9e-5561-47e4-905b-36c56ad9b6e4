package com.scube.support.controller;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.support.dto.ChatResponseDto;
import com.scube.support.dto.MessageDto;
import com.scube.support.enums.ChatStatus;
import com.scube.support.service.ChatManagementService;
import com.scube.support.service.MessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.UUID;

@AllArgsConstructor
@RestController
@Slf4j
@Validated
public class ChatController {
    private final ChatManagementService chatManagementService;
    private final MessageService messageService;

    @GetMapping("/chats")
    public ResponseEntity<ChatResponseDto> getAllChats(
            @AuthenticationPrincipal OpenidClaimSet jwt,
            @RequestParam ChatStatus status,
            @PageableDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) {
        return ResponseEntity.ok(chatManagementService.getAllChats(UUID.fromString(jwt.getSubject()), status, pageable));
    }

    @PutMapping("/resolveChat/{chatId}")
    public ResponseEntity<Void> resolveChat(@PathVariable UUID chatId) {
        chatManagementService.resolveChat(chatId);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/messages/{chatId}")
    public ResponseEntity<Page<MessageDto>> getConversation(
            @PathVariable UUID chatId,
            @RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            @AuthenticationPrincipal OpenidClaimSet jwt) {
        Page<MessageDto> chats = messageService.getConversation(chatId, jwt.getSubject(), pageNumber, pageSize);
        return ResponseEntity.ok(chats);
    }

    @PostMapping("/joinConversation")
    public ResponseEntity<Void> joinConversation(
            @AuthenticationPrincipal OpenidClaimSet jwt,
            @RequestParam UUID chatId
    ) {
        chatManagementService.joinConversation(chatId, UUID.fromString(jwt.getSubject()));
        return ResponseEntity.ok().build();
    }

}

package com.scube.support.controller.websocket;

import com.scube.multi.tenant.TenantContext;
import com.scube.support.dto.ChatMessage;
import com.scube.support.service.ChatCoordinatorService;
import lombok.RequiredArgsConstructor;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;

import java.security.Principal;

@Controller
@RequiredArgsConstructor
@MessageMapping("/chat")
public class ChatControllerWebSocket {
    private final ChatCoordinatorService chatCoordinatorService;

    @MessageMapping("/send-message")
    public void sendPrivateMessage(@Payload ChatMessage message,
                                   SimpMessageHeaderAccessor headerAccessor, Principal principal) {
        String currentTenant = (String) headerAccessor.getSessionAttributes().get("tenantId");
        TenantContext.setTenantId(currentTenant);
        chatCoordinatorService.sendMessage(message, headerAccessor, true);
        TenantContext.clear();
    }
}

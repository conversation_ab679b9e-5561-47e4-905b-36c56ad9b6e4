package com.scube.support.controller;

import com.c4_soft.springaddons.security.oidc.OpenidClaimSet;
import com.scube.support.dto.MessageDto;
import com.scube.support.service.MessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;  
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@AllArgsConstructor
@RestController
@Slf4j
@Validated
public class LoggedInChatController {
    private final MessageService messageService;


    @GetMapping("/me/messages")
    public ResponseEntity<Page<MessageDto>> getConversation(
            @RequestParam(defaultValue = "1") int pageNumber,
            @RequestParam(defaultValue = "10") int pageSize,
            @AuthenticationPrincipal OpenidClaimSet jwt){
        Page<MessageDto> chats = messageService.getConversation(jwt.getSubject() ,pageNumber, pageSize);
        return ResponseEntity.ok(chats);
    }
}

package com.scube.support;

import com.scube.audit.EnableAuditingLibrary;
import com.scube.rabbit.core.annotation.EnableRabbitMQLibrary;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.data.web.config.EnableSpringDataWebSupport;

@SpringBootApplication
@EnableRabbitMQLibrary(additionalPackages = "com.scube")
@EnableAuditingLibrary
@OpenAPIDefinition(info = @Info(title = "Config Service API", version = "1.0", description = "Swagger Documentation"))
@EnableSpringDataWebSupport
public class SupportServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(SupportServiceApplication.class, args);
    }

}

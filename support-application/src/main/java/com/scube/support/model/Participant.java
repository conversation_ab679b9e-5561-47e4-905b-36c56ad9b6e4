package com.scube.support.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.util.UUID;

@Getter
@Setter
@Table(name = "participant")
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Audited
public class Participant extends AuditableEntity {
    @ManyToOne
    @JoinColumn(name = "chat_id", nullable = false)
    private Chat chat;

    private UUID userId;
}

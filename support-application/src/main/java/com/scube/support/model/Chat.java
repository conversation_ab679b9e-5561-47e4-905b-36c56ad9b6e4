package com.scube.support.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.support.enums.ChatStatus;
import com.scube.support.enums.ChatType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.envers.Audited;

import java.util.List;

@Getter
@Setter
@Table(name = "chat")
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Audited
public class Chat  extends AuditableEntity {
    private String title;
    private String description;

    @Enumerated(EnumType.STRING)
    private ChatType status = ChatType.OPEN;

    @OneToMany(mappedBy = "chat", cascade = CascadeType.ALL)
    private List<Participant> participants;

    @OneToMany(mappedBy = "chat", cascade = CascadeType.ALL)
    private List<Message> messages;
}

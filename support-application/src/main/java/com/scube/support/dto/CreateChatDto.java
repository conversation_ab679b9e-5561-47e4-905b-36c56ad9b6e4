package com.scube.support.dto;

import com.scube.support.model.Message;
import com.scube.support.model.Participant;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class CreateChatDto {
    private String title;
    private String description;
    private List<Participant> participantList = new ArrayList<>();
    private List<Message> messages = new ArrayList<>();
}


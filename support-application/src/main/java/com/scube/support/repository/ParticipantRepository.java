package com.scube.support.repository;

import com.scube.support.model.Participant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface ParticipantRepository extends JpaRepository<Participant, Long> {
    boolean existsByUserIdAndChatUuid(UUID userId, UUID chatUuid);
    Optional<Participant> findFirstByUserIdOrderByIdDesc(UUID userId);
}

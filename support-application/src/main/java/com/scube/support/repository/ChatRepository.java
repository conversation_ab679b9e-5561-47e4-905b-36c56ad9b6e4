package com.scube.support.repository;

import com.scube.support.dto.ChatStatusCountDTO;
import com.scube.support.enums.ChatStatus;
import com.scube.support.enums.ChatType;
import com.scube.support.model.Chat;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Repository
public interface ChatRepository extends JpaRepository<Chat, Long> {

    Optional<Chat> findByUuid(UUID chatUuid);

    Page<Chat> findByStatus(ChatType status, Pageable pageable);

    Page<Chat> findByParticipants_UserIdAndStatusNot(UUID userId, ChatType status, Pageable pageable);

    @Query("""
                SELECT c FROM Chat c
                WHERE c.status != :excludedStatus
                AND NOT EXISTS (
                    SELECT p FROM Participant p
                    WHERE p.chat = c AND p.userId = :userId
                )
            """)
    Page<Chat> findByStatusNotAndNotAssociatedWithUser(
            @Param("userId") UUID userId,
            @Param("excludedStatus") ChatType excludedStatus,
            Pageable pageable
    );


    @Query("""
                SELECT
                    COUNT(CASE
                        WHEN EXISTS (
                            SELECT p FROM Participant p
                            WHERE p.chat = c AND p.userId = :userId
                        ) AND c.status != 'CLOSED'
                        THEN 1 ELSE null
                    END),
                    COUNT(CASE
                        WHEN NOT EXISTS (
                            SELECT p FROM Participant p
                            WHERE p.chat = c AND p.userId = :userId
                        ) AND c.status != 'CLOSED'
                        THEN 1 ELSE null
                    END),
                    COUNT(CASE
                        WHEN c.status = 'CLOSED'
                        THEN 1 ELSE null
                    END)
                FROM Chat c
            """)
    ChatStatusCountDTO countChatsByStatus(@Param("userId") UUID userId);
}

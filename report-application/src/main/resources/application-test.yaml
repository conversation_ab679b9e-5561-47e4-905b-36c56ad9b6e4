spring:
  application:
    name: ReportService
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB

  liquibase:
    enabled: true
    change-log: classpath:/db/changelog/db.changelog-master.xml

  jpa:
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    show-sql: false
    properties:
      hibernate:
        format_sql: true

  h2:
    console:
      enabled: true
      path: /h2-console



keycloak:
  authoritiesConverterType: NATIVE
  host: http://localhost:8443/realms/master
  public-host: http://localhost:8443/realms/master
  admin:
    url: http://localhost:8443
    realm: master
    client-id: test
    client-secret: test
  swagger:
    url: http://localhost:8443
    realm: clerkXpress
    client-id: test

com.c4-soft.springaddons.oidc:
  ops:
    - iss: http://localhost:8443/realms/master
      jwkSetUri: http://localhost:8443/realms/master/protocol/openid-connect/certs
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/actuator/**"
      - "/public/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - http://localhost:3030

multi-tenancy:
  enabled: false
  keycloak:
    enabled: false
  database:
    enabled: false

com.scube.client:
  ai: "http://${CLIENT_LIB_HOST:localhost}:9011/api/ai"
  auth: "http://${CLIENT_LIB_HOST:localhost}:9001/api/auth"
  calculation: "http://${CLIENT_LIB_HOST:localhost}:9002/api/calculation"
  document: "http://${CLIENT_LIB_HOST:localhost}:9003/api/document-service"
  documentTemplateHelper: "http://${CLIENT_LIB_HOST:localhost}:9012/api/document-template-helper"
  documentTemplate: "http://${CLIENT_LIB_HOST:localhost}:9009/api/document-template"
  imageProcessing: "http://${CLIENT_LIB_HOST:localhost}:9010/api/image-processing"
  license: "http://${CLIENT_LIB_HOST:localhost}:9004/api/license"
  notification: "http://${CLIENT_LIB_HOST:localhost}:9005/api/notification"
  ocr: "http://${CLIENT_LIB_HOST:localhost}:9008/api/ocr"
  payment: "http://${CLIENT_LIB_HOST:localhost}:9006/api/payment"
  report: "http://${CLIENT_LIB_HOST:localhost}:9007/api/report"


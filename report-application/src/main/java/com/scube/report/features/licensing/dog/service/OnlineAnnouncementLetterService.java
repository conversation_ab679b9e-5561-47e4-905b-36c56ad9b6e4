package com.scube.report.features.licensing.dog.service;

import com.scube.client.license.generated.LicenseServiceConnection;
import com.scube.licensing.features.license.reports.dto.gen_dto.ReportQueryRequest;
import com.scube.report.features.base.service.IReportQueryService;
import com.scube.report.features.licensing.dog.constants.ReportConstants;
import com.scube.report.features.licensing.dog.dto.reports.DogLicenseOnlineAnnouncementLetter;
import com.scube.report.features.licensing.dog.dto.request.ReportRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import static com.scube.report.features.base.util.ValidationUtil.validate;

@Service("DogLicenseOnlineAnnouncementLetter")
@Slf4j
@RequiredArgsConstructor
public class OnlineAnnouncementLetterService implements IReportQueryService {
    public final LicenseServiceConnection licenseService;
    public static final String REPORT_NAME = "DogLicenseOnlineAnnouncementLetter";

    @Override
    public Object execute(Map<String, Object> params) {
        ReportRequest request = new ReportRequest(params);

        validate(request);

        String sql = "SELECT * FROM view_pivoted_license WHERE license_status_code = 'ACTIVE' AND DATE(valid_to_date) BETWEEN DATE(?) AND DATE(?)";

        Object[] sqlParams = new Object[] {params.get("startDate"), params.get("endDate")};

        var result = licenseService.report().getDogLicenseOnlineAnnouncementLetterAsync(new ReportQueryRequest(sql, sqlParams));

        DateTimeFormatter usaDate = DateTimeFormatter.ofPattern("MM/dd/yyyy");

        List<Map<String, Object>> licenses = result.collectList().block();

        for (int i = 0; i < licenses.size() - 1; i++) {
            licenses.get(i).put("pageBreak", ReportConstants.PAGE_BREAK);
        }

       return new DogLicenseOnlineAnnouncementLetter(licenses);
    }
}

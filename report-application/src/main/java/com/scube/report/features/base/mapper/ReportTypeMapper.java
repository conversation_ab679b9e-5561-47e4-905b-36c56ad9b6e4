package com.scube.report.features.base.mapper;

import com.scube.report.features.base.dto.FormSectionDto;
import com.scube.report.features.base.dto.ReportTypeDto;
import com.scube.report.features.base.entity.ReportType;
import com.scube.report.features.base.repository.FormSectionRepository;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Mapper(componentModel = "spring", uses = {FormSectionMapper.class})
@Component
public abstract class ReportTypeMapper {

    @Autowired
    private FormSectionRepository formSectionRepository;

    @Mapping(target = "uuid", source = "reportTypeUuid")
    public abstract ReportType toEntity(ReportTypeDto reportTypeDto);

    @Mapping(target = "reportTypeUuid", source = "uuid")
    @Mapping(target = "queries", expression = "java(reportType.queries())")
    public abstract ReportTypeDto toDto(ReportType reportType);

    public abstract List<ReportType> toEntity(List<ReportTypeDto> reportTypeDtos);

    public abstract List<ReportTypeDto> toDto(List<ReportType> reportTypes);

    public abstract void updateReportTypeFromDto(ReportTypeDto dto, @MappingTarget ReportType entity);

    @AfterMapping
    protected void mapProperties(ReportTypeDto reportTypeDto, @MappingTarget ReportType reportType) {
        reportType.setBatch(reportTypeDto.isBatch());
        reportType.setQueries(reportTypeDto.getQueries());

        var name = Optional.ofNullable(reportTypeDto.getFormSection())
                .map(FormSectionDto::getName)
                .orElse("adhoc");
        var uuid = Optional.ofNullable(reportTypeDto.getFormSection())
                .map(FormSectionDto::getUuid)
                .orElse(null);
        formSectionRepository.findByUuid(uuid)
                .ifPresent(reportType::setFormSection);
        formSectionRepository.findByName(name)
                .ifPresent(reportType::setFormSection);
    }
}
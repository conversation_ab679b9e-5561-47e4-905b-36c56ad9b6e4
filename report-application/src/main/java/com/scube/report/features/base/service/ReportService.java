package com.scube.report.features.base.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.scube.client.document.generated.DocumentServiceConnection;
import com.scube.client.documentTemplate.generated.DocumentTemplateServiceConnection;
import com.scube.rabbit.core.fanout.IRabbitFanoutPubSub;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.report.features.base.dto.GenerateReportRequest;
import com.scube.report.features.base.dto.GetReportFormDataBySectionNameResponse;
import com.scube.report.features.base.dto.ReportDto;
import com.scube.report.features.base.entity.Report;
import com.scube.report.features.base.entity.ReportType;
import com.scube.report.features.base.mapper.ReportMapper;
import com.scube.report.features.base.repository.ReportRepository;
import com.scube.report.features.base.repository.ReportTypeRepository;
import com.scube.report.features.licensing.dog.dto.GenerateReportResponse;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.util.Map;
import java.util.UUID;

import static io.micrometer.common.util.StringUtils.isEmpty;

@AllArgsConstructor
@Service
@Slf4j
public class ReportService {
    private final ReportTypeRepository reportTypeRepository;
    private final ReportRepository reportRepository;
    private final DocumentTemplateServiceConnection documentTemplateService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final DocumentServiceConnection documentServiceConnection;
    private final ReportDataService reportDataService;
    private final ReportMapper reportMapper = ReportMapper.INSTANCE;
    private final ReportAsyncService reportAsyncService;

    @Transactional
    public ReportDto getReport(UUID reportId) {
        log.debug("ReportService.getReport()");

        return reportMapper.toDto(reportRepository.findByUuid(reportId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Report not found.")));
    }

    @Transactional
    public GetReportFormDataBySectionNameResponse getReportFormDataBySectionName(String sectionName) {
        log.debug("ReportService.getReportFormDataBySectionId()");

        return new GetReportFormDataBySectionNameResponse(
                reportTypeRepository.findAllByFormSectionName(sectionName).stream().map(reportType -> reportType.getJson()).toList());
    }

    public Object generateReport(String reportTemplateKey, Map<String, Object> params) {
        ReportType reportType = reportTypeRepository.findByReportTemplateKey(reportTemplateKey).orElseThrow(
                () -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Report not found"));

        return generateReport(params, reportType);
    }

    public Object generateReport(UUID reportTypeId, Map<String, Object> params) {
        ReportType reportType = reportTypeRepository.findByUuid(reportTypeId).orElseThrow(
                () -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Report not found"));

        return generateReport(params, reportType);
    }

    private Object generateReport(Map<String, Object> params, ReportType reportType) {
        log.debug("ReportService.generateReport()");

        String outputType = (String) params.get("outputType");

        if (isEmpty(outputType)) {
            outputType = "async";
        }

        return switch (outputType) {
            case "json" -> generateJsonReport(reportType, params);
            case "sync" -> generatePdfReport(reportType, params);
            case "async" -> generatePdfReportAsync(reportType, params);
            default -> generatePdfReportAsync(reportType, params);
        };
    }

    public Object generateJsonReport(ReportType reportType, Map<String, Object> params) {
        return reportDataService.getReportData(reportType, params);
    }

    @Transactional
    public Report buildReport(ReportType reportType) {
        log.debug("ReportService.initializeReport()");

        Report report = new Report();

        report.initialize(reportType);

        return reportRepository.save(report);
    }

    @Transactional
    public void failReport(UUID reportId, String errorMessage) {
        log.debug("ReportService.failReport()");

        Report report = reportRepository.findByUuid(reportId).orElse(null);

        if (report == null) {
            log.error("ReportService.failReport(): reportId {} not found.", reportId);
            return;
        }

        report.fail(errorMessage);
        reportRepository.save(report);
    }

    @Transactional
    public void completeReport(UUID reportId, UUID documentId) {
        log.debug("ReportService.completeReport()");

        Report report = reportRepository.findByUuid(reportId).orElse(null);

        if (report == null) {
            log.error("ReportService.completeReport(): reportId {} not found.", reportId);
            return;
        }

        if (documentId == null) {
            log.error("ReportService.completeReport(): documentId is null for reportId {}", reportId);
            report.fail("DocumentId is null");
            reportRepository.save(report);
            return;
        }

        report.setDocumentId(documentId);
        report.complete();
        reportRepository.save(report);
    }

    public GenerateReportResponse generatePdfReportAsync(ReportType reportType, Map<String, Object> params) {
        Report report = buildReport(reportType);

        reportAsyncService.generatePdfReportAsync(report, params);

        return new GenerateReportResponse(report.getUuid());
    }

    @SneakyThrows
    public ResponseEntity<Resource> generatePdfReport(ReportType reportType, Map<String, Object> params) {
        var reportData = objectMapper.valueToTree(reportDataService.getReportData(reportType, params));

        log.debug("ReportService.generateReport(): reportData={}", reportData);

        var reportUuid = documentTemplateService.documentTemplate()
                .fillTemplate(reportType.getReportTemplateKey(), reportData)
                .getDocumentId();

        return documentServiceConnection.document().getFileHttp(reportUuid);
    }

    @SneakyThrows
    public ResponseEntity<Resource> generatePdfReport(String templateKey, GenerateReportRequest request) {
        var reportData = objectMapper.valueToTree(reportDataService.getReportData(request));

        log.debug("ReportService.generateReport(): reportData={}", reportData);

        var reportUuid = documentTemplateService.documentTemplate()
                .fillTemplate(templateKey, reportData)
                .getDocumentId();

        return documentServiceConnection.document().getFileHttp(reportUuid);
    }

    @SneakyThrows
    public ResponseEntity<Resource> generatePdfReport(MultipartFile file, GenerateReportRequest request) {
        var reportData = objectMapper.writeValueAsString(reportDataService.getReportData(request));

        log.debug("ReportService.generateReport(): reportData={}", reportData);

        var reportUuid = documentTemplateService.documentTemplate().fill(file, reportData)
                .getDocumentId();

        return documentServiceConnection.document().getFileHttp(reportUuid);
    }

    public record ReportFailedEvent(String reportId, String errorMessage) implements IRabbitFanoutPubSub {
    }


    public record GenerateDocumentCommand(String parentId,
                                          String parentType,
                                          String nameKey,
                                          String format,
                                          JsonNode data
    ) implements IRabbitFanoutPublisher {
    }
}

package com.scube.report.features.base.mapper;

import com.scube.report.features.base.dto.ReportDto;
import com.scube.report.features.base.entity.Report;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ReportMapper {
    ReportMapper INSTANCE = Mappers.getMapper(ReportMapper.class);

    @Mapping(target="reportId", source="uuid")
    @Mapping(target="status", source="reportStatus")
    ReportDto toDto(Report report);

    Iterable<ReportDto> toDto(Iterable<Report> reports);
}

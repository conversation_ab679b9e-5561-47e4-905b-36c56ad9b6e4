package com.scube.report.features.permission;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.report.SharedTestConfig;
import org.junit.jupiter.api.Test;

import static com.scube.report.permissions.MockMvcHelper.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

class ReportControllerTest extends SharedTestConfig {

    @Test
    @WithJwt(json = START_JSON + Permissions.Report.GET_REPORT + END_JSON)
    void testGetReportResident_Success() throws Exception {
        var result = performGet(mockMvc, "/report/a3e58a7d-3795-43bc-9487-7b6ba7b1a68f");
        assertNotEquals(403, result.getResponse().getStatus());
    }

    @Test
    void testGetReportResident_Failure() throws Exception {
        var result = performGet(mockMvc, "/report/a3e58a7d-3795-43bc-9487-7b6ba7b1a68f");
        assertEquals(403, result.getResponse().getStatus());
    }
}
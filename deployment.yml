apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-auth-service-depl
  labels:
    app: scube-auth-service
spec:
  selector:
    matchLabels:
      app: scube-auth-service
  template:
    metadata:
      labels:
        app: scube-auth-service
    spec:
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-auth-service
          image: service_auth
          ports:
            - containerPort: 9001
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-auth-service-srv.backend.svc.cluster.local
              path: /api/auth/actuator/health
              port: 9001
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9001
            initialDelaySeconds: 30
            periodSeconds: 30
          env:
            - name: TZ
              value: "America/New_York"
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: KE<PERSON><PERSON><PERSON><PERSON>_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-id-secret
                  key: swagger-client-id
            - name: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-secret
            - name: ENCRYPTION_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: encryption-secret
                  key: secret-Key
            - name: JAVA_TOOL_OPTIONS
              value: "-javaagent:/app/opentelemetry-javaagent.jar"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: "http://clerkxpress-jaeger-collector.observability.svc.cluster.local:4317"
            - name: OTEL_EXPORTER_OTLP_PROTOCOL
              value: "grpc"
            - name: OTEL_METRICS_EXPORTER
              value: "none"
            - name: OTEL_SERVICE_NAME
              value: "AuthService"
---
apiVersion: v1
kind: Service
metadata:
  name: scube-auth-service-srv
  labels:
    app: scube-auth-service
spec:
  selector:
    app: scube-auth-service
  type: ClusterIP
  ports:
    - name: auth-port
      protocol: TCP
      port: 9001
      targetPort: 9001

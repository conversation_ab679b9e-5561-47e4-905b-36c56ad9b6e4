apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-report-service-depl
  labels:
    app: scube-report-service
spec:
  selector:
    matchLabels:
      app: scube-report-service
  template:
    metadata:
      labels:
        app: scube-report-service
    spec:
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-report-service
          image: service_report
          ports:
            - containerPort: 9007
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-report-service-srv.backend.svc.cluster.local
              path: /api/report/actuator/health
              port: 9007
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9007
            initialDelaySeconds: 60
            periodSeconds: 30
          env:
            - name: TZ
              value: "America/New_York"
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: username
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgres-user-pass-secret
                  key: password
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: username
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: password
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-id-secret
                  key: swagger-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-id
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: keycloak-admin-secret
                  key: admin-client-secret
            - name: ENCRYPTION_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: encryption-secret
                  key: secret-Key
---
apiVersion: v1
kind: Service
metadata:
  name: scube-report-service-srv
  labels:
    app: scube-report-service
spec:
  selector:
    app: scube-report-service
  type: ClusterIP
  ports:
    - name: report-port
      protocol: TCP
      port: 9007
      targetPort: 9007

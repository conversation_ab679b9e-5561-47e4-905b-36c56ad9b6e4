apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/ai-service-sa-role
  labels:
    app.kubernetes.io/managed-by: eksctl
  name: ai-service-sa
  namespace: backend
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-ai-service-depl
  labels:
    app: scube-ai-service
spec:
  selector:
    matchLabels:
      app: scube-ai-service
  template:
    metadata:
      labels:
        app: scube-ai-service
    spec:
      containers:
        - name: scube-ai-service
          image: service_ai
          ports:
            - containerPort: 9011
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-ai-service-srv.backend.svc.cluster.local
              path: /api/ai/actuator/health
              port: 9011
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 9011
            initialDelaySeconds: 60
            periodSeconds: 30
          env:
          - name: TZ
            value: "America/New_York"
          - name: SPRING_PROFILES_ACTIVE
            valueFrom:
              secretKeyRef:
                key: spring-profile
                name: spring-profile
          - name: OPEN_AI_API_TOKEN
            valueFrom:
              secretKeyRef:
                name: open-ai-token
                key: token
          - name: SPRING_DATASOURCE_USERNAME
            valueFrom:
              secretKeyRef:
                name: postgres-user-pass-secret
                key: username
          - name: SPRING_DATASOURCE_PASSWORD
            valueFrom:
              secretKeyRef:
                name: postgres-user-pass-secret
                key: password
          - name: SPRING_RABBITMQ_USERNAME
            valueFrom:
              secretKeyRef:
                name: rabbitmq-user-pass-secret
                key: username
          - name: SPRING_RABBITMQ_PASSWORD
            valueFrom:
              secretKeyRef:
                name: rabbitmq-user-pass-secret
                key: password
          - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
            valueFrom:
              secretKeyRef:
                name: keycloak-id-secret
                key: swagger-client-id
          - name: KEYCLOAK_ADMIN_CLIENT_ID
            valueFrom:
              secretKeyRef:
                name: keycloak-admin-secret
                key: admin-client-id
          - name: KEYCLOAK_ADMIN_CLIENT_SECRET
            valueFrom:
              secretKeyRef:
                name: keycloak-admin-secret
                key: admin-client-secret
          - name: ENCRYPTION_SECRET_KEY
            valueFrom:
              secretKeyRef:
                name: encryption-secret
                key: secret-Key
          - name: JAVA_TOOL_OPTIONS
            value: "-javaagent:/app/opentelemetry-javaagent.jar"
          - name: OTEL_EXPORTER_OTLP_ENDPOINT
            value: "http://clerkxpress-jaeger-collector.observability.svc.cluster.local:4317"
          - name: OTEL_EXPORTER_OTLP_PROTOCOL
            value: "grpc"
          - name: OTEL_METRICS_EXPORTER
            value: "none"
          - name: OTEL_SERVICE_NAME
            value: "AIService"
      imagePullSecrets:
        - name: aws-ecr-secret
      serviceAccountName: ai-service-sa
---
apiVersion: v1
kind: Service
metadata:
  name: scube-ai-service-srv
  labels:
    app: scube-ai-service
spec:
  selector:
    app: scube-ai-service
  type: ClusterIP
  ports:
    - name: ai-port
      protocol: TCP
      port: 9011
      targetPort: 9011

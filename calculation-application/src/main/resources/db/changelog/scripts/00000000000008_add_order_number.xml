<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addOrderNumberColumn" author="Ben">
        <addColumn tableName="cart">
            <column name="order_number" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="addOrderNumberColumnToAuditTable" author="Ben">
        <addColumn tableName="audit_log_cart">
            <column name="order_number" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="createOrderNumberSequence" author="Ben">
        <createSequence sequenceName="order_number_seq" />
    </changeSet>
    <changeSet id="createOrderNumberTriggerFunction" author="Ben">
        <sql>
            CREATE OR REPLACE FUNCTION update_order_number()
            RETURNS TRIGGER
            LANGUAGE plpgsql
            AS '
            BEGIN
                IF NEW.status LIKE ''ORDER_%'' AND NEW.order_number IS NULL THEN
                    NEW.order_number := ''O-'' || nextval(''order_number_seq'');
                END IF;
                RETURN NEW;
            END;'
        </sql>
    </changeSet>
    <changeSet id="updateExistingOrderNumbers" author="Ben">
        <sql>
            UPDATE cart
            SET order_number = 'O-' || nextval('order_number_seq')
            WHERE status LIKE 'ORDER_%'
            AND order_number IS NULL;
        </sql>
    </changeSet>
    <changeSet id="addTriggerToUpdateOrderNumber" author="Ben">
        <sql>
            CREATE TRIGGER order_number_trigger
            BEFORE INSERT OR UPDATE ON cart
            FOR EACH ROW
            EXECUTE PROCEDURE update_order_number();
        </sql>
    </changeSet>
</databaseChangeLog>

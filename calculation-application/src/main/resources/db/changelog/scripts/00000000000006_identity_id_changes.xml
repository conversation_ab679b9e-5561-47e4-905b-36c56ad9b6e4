<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1696226159330-3">
        <dropSequence sequenceName="cart_item_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696226159330-4">
        <dropSequence sequenceName="fee_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696226159330-1">
        <dropForeignKeyConstraint baseTableName="cart_item" constraintName="fk1uobyhgl1wvgt1jpccia8xxs3"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1696226159330-2">
        <addForeignKeyConstraint baseColumnNames="cart_id" baseTableName="cart_item" constraintName="fk1uobyhgl1wvgt1jpccia8xxs3" onDelete="NO ACTION" onUpdate="NO ACTION" referencedColumnNames="cart_id" referencedTableName="cart"/>
    </changeSet>
</databaseChangeLog>

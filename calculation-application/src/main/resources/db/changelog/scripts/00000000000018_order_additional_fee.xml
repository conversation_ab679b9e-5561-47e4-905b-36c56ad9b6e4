<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="createTable_order_additional_fee" author="Ajay">
        <createTable tableName="order_additional_fee">
            <column autoIncrement="true" name="order_additional_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="order_additional_fee_pkey"/>
            </column>
            <column name="order_additional_fee_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column name="properties" type="JSONB"/>
            <column name="fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <column name="order_id" type="UUID">
                <constraints nullable="false"/>
            </column>
            <column name="price" type="numeric(38, 2)"/>
        </createTable>
    </changeSet>
    <changeSet id="addForeignKeyConstraint_order_additional_fee" author="Ajay">
        <addForeignKeyConstraint
                baseTableName="order_additional_fee"
                baseColumnNames="order_id"
                referencedTableName="order_"
                referencedColumnNames="order_id"
                constraintName="fk_order_additional_fee_order"/>
        <addForeignKeyConstraint constraintName="fk_order_additional_fee_fee_id"
                                 baseColumnNames="fee_id" baseTableName="order_additional_fee"
                                 referencedColumnNames="fee_id" referencedTableName="fee"/>
    </changeSet>

    <changeSet id="createTable_audit_log_order_additional_fee" author="Ajay">
        <createTable tableName="audit_log_order_additional_fee">
            <column autoIncrement="true" name="order_additional_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order_additional_fee_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order_additional_fee_pkey"/>
            </column>
            <column name="order_additional_fee_uuid" type="UUID" defaultValueComputed="gen_random_uuid()"/>
            <column name="revision_type" type="SMALLINT"/>
            <column name="entity_fee_uuid" type="UUID"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="properties" type="JSONB"/>
            <column name="fee_id" type="BIGINT"/>
            <column name="price" type="numeric(38, 2)"/>
            <column name="order_id" type="UUID"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="label" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

    <changeSet id="dropColumn_audit_log_order_additional_fee2" author="Ajay">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_order_additional_fee" columnName="entity_fee_uuid"/>
            </not>
        </preConditions>
    </changeSet>

    <changeSet id="add_columns_to_order_additional_fee" author="Ajay">
        <!-- Add to order_additional_fee -->
        <addColumn tableName="order_additional_fee">
            <column name="unique_item_id" type="UUID"/>
            <column name="item_type_id" type="BIGINT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="description" type="VARCHAR(255)"/>
        </addColumn>

        <!-- Add to audit_log_order_additional_fee -->
        <addColumn tableName="audit_log_order_additional_fee">
            <column name="unique_item_id" type="UUID"/>
            <column name="item_type_id" type="BIGINT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="description" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>
    <changeSet id="modify_item_type_id_to_varchar" author="Ajay">
        <modifyDataType tableName="order_additional_fee" columnName="item_type_id" newDataType="VARCHAR(255)"/>
        <modifyDataType tableName="audit_log_order_additional_fee" columnName="item_type_id"
                        newDataType="VARCHAR(255)"/>
    </changeSet>

    <changeSet id="drop_fk_order_additional_fee_fee_id" author="Ajay">
        <dropForeignKeyConstraint
                baseTableName="order_additional_fee"
                constraintName="fk_order_additional_fee_fee_id"/>
    </changeSet>

    <changeSet id="drop_fee_id_column_from_order_additional_fee" author="Ajay">
        <dropColumn columnName="fee_id" tableName="order_additional_fee"/>
    </changeSet>

    <changeSet id="drop_fee_id_column_from_audit_log_order_additional_fee" author="Ajay">
        <dropColumn columnName="fee_id" tableName="audit_log_order_additional_fee"/>
    </changeSet>

</databaseChangeLog>

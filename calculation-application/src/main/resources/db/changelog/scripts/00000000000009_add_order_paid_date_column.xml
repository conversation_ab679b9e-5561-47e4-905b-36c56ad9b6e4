<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="addOrderPaidDateColumn" author="Ben">
        <addColumn tableName="cart">
            <column name="order_paid_date" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
    </changeSet>
    <changeSet id="addOrderPaidDateColumnToAuditTable" author="Ben">
        <addColumn tableName="audit_log_cart">
            <column name="order_paid_date" type="TIMESTAMP WITH TIME ZONE"/>
        </addColumn>
    </changeSet>
    <changeSet id="updateExistingOrderNumbers" author="Ben">
        <sql>
            UPDATE cart
            SET order_paid_date = last_modified_date
            WHERE status = 'ORDER_PAID'
            AND order_paid_date IS NULL;
        </sql>
    </changeSet>
</databaseChangeLog>

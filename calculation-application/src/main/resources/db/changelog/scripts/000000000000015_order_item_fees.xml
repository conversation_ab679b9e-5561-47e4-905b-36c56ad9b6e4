<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="createTable_order_item_fee" author="David">
        <createTable tableName="order_item_fee">
            <column autoIncrement="true" name="order_item_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="order_item_fee_pkey"/>
            </column>
            <column name="order_item_fee_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>

            <column name="order_item_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="price" type="numeric(38, 2)"/>
        </createTable>
    </changeSet>
    <changeSet id="createTable_audit_log_order_item_fee" author="David">
        <createTable tableName="audit_log_order_item_fee">
            <column autoIncrement="true" name="order_item_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order_item_fee_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_order_item_fee_pkey"/>
            </column>
            <column name="revision_type" type="SMALLINT"/>
            <column name="entity_fee_uuid" type="UUID"/>
            <column name="last_modified_by" type="VARCHAR(255)"/>
            <column name="last_modified_date" type="TIMESTAMP WITHOUT TIME ZONE"/>
            <column name="properties" type="JSONB"/>
            <column name="conversion_reference" type="VARCHAR(255)"/>

            <column name="order_item_id" type="BIGINT"/>
            <column name="fee_id" type="BIGINT"/>
            <column name="price" type="numeric(38, 2)"/>
        </createTable>
    </changeSet>
    <changeSet id="populateTable_order_item_fee_from_order_item_fees" author="David">
        <sql>
            INSERT INTO order_item_fee (order_item_id, fee_id)
            SELECT order_item_id, fee_id
            FROM order_item_fees;
        </sql>
    </changeSet>
    <changeSet id="populateTable_audit_log_order_item_fee_from_audit_log_order_item_fees" author="David">
        <sql>
            INSERT INTO audit_log_order_item_fee (revision_id, revision_type, order_item_id, fee_id)
            SELECT revision_id, revision_type, order_item_id, fee_id
            FROM audit_log_order_item_fees;
        </sql>
    </changeSet>
    <changeSet id="dropConstraint_order_item_fees" author="David">
        <dropAllForeignKeyConstraints baseTableName="order_item_fees"/>
        <dropAllForeignKeyConstraints baseTableName="audit_log_order_item_fees"/>
    </changeSet>
    <changeSet id="addForeignKeyConstraint_order_item_fee" author="David">
        <addForeignKeyConstraint constraintName="fk_order_item_fee_order_item_id"
                                 baseTableName="order_item_fee" baseColumnNames="order_item_id"
                                 referencedTableName="order_item" referencedColumnNames="order_item_id"/>
        <addForeignKeyConstraint constraintName="fk_order_item_fee_fee_id"
                                 baseColumnNames="fee_id" baseTableName="order_item_fee"
                                 referencedColumnNames="fee_id" referencedTableName="fee"/>
    </changeSet>
    <changeSet id="updateForeignKey_order_item_fee_order_item_id_cascade" author="David">
        <dropForeignKeyConstraint baseTableName="order_item_fee" constraintName="fk_order_item_fee_order_item_id"/>

        <addForeignKeyConstraint constraintName="fk_order_item_fee_order_item_id"
                                 baseTableName="order_item_fee" baseColumnNames="order_item_id"
                                 referencedTableName="order_item" referencedColumnNames="order_item_id"
                                 onDelete="CASCADE"/>
    </changeSet>

    <changeSet id="dropTables_order_item_fees" author="David">
        <dropTable tableName="order_item_fees"/>
        <dropTable tableName="audit_log_order_item_fees"/>
    </changeSet>
    <changeSet id="dropAddColumn_audit_log_order_item_fee" author="David">
        <dropColumn columnName="entity_fee_uuid" tableName="audit_log_order_item_fee"/>
        <addColumn tableName="audit_log_order_item_fee">
            <column name="order_item_fee_uuid" type="UUID"/>
        </addColumn>
    </changeSet>
    <changeSet id="updateTable_order_item_fee" author="David">
        <sql>
            update order_item_fee as cif
            set created_date = ci.created_date,
            created_by = ci.created_by,
            last_modified_date = ci.last_modified_date,
            last_modified_by = ci.last_modified_by
            from order_item ci
            where ci.order_item_id = cif.order_item_id;
        </sql>
    </changeSet>
</databaseChangeLog>

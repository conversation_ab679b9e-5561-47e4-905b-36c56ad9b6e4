<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="createTable_cart_additional_item_fee" author="Ajay">
        <createTable tableName="cart_additional_item_fee">
            <column autoIncrement="true" name="cart_additional_item_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="cart_additional_item_fee_pkey"/>
            </column>
            <column name="cart_additional_item_fee_uuid" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="cart_additional_fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(1000)" defaultValueComputed="current_user">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="now()">
                <constraints nullable="false"/>
            </column>
            <column name="properties" type="JSONB"/>
            <column name="fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="price" type="numeric(38, 2)"/>
        </createTable>
    </changeSet>

    <changeSet id="createTable_audit_log_cart_additional_item_fee" author="Ajay">
        <createTable tableName="audit_log_cart_additional_item_fee">
            <column name="cart_additional_item_fee_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_cart_additional_item_fee_pkey"/>
            </column>
            <column name="revision_id" type="INTEGER">
                <constraints nullable="false" primaryKey="true" primaryKeyName="audit_log_cart_additional_item_fee_pkey"/>
            </column>

            <column name="cart_additional_item_fee_uuid" type="UUID"/>
            <column name="cart_additional_fee_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="conversion_reference" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(1000)"/>
            <column name="created_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="last_modified_by" type="VARCHAR(1000)"/>
            <column name="last_modified_date" type="TIMESTAMP WITH TIME ZONE"/>
            <column name="properties" type="JSONB"/>
            <column name="fee_id" type="BIGINT"/>
            <column name="price" type="numeric(38, 2)"/>
            <column name="revision_type" type="SMALLINT"/>
        </createTable>
    </changeSet>


    <changeSet id="addForeignKeyConstraint_cart_additional_item_fee" author="Ajay">
        <addForeignKeyConstraint constraintName="fk_cart_additional_item_fee_cart_additional_fee_id"
                                 baseTableName="cart_additional_item_fee" baseColumnNames="cart_additional_fee_id"
                                 referencedTableName="cart_additional_fee" referencedColumnNames="cart_additional_fee_id"/>
        <addForeignKeyConstraint constraintName="fk_cart_additional_item_fee_fee_id"
                                 baseColumnNames="fee_id" baseTableName="cart_additional_item_fee"
                                 referencedColumnNames="fee_id" referencedTableName="fee"/>
    </changeSet>
</databaseChangeLog>
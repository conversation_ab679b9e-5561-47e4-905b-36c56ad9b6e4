<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="davidr (generated)" id="1692828482875-19">
        <addColumn tableName="fee">
            <column name="key" type="varchar(255 BYTE)"/>
        </addColumn>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-20">
        <dropForeignKeyConstraint baseTableName="cart_item" constraintName="cart_item_category_id_fkey"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-21">
        <dropSequence sequenceName="cart_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-22">
        <dropSequence sequenceName="cart_item_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-23">
        <dropSequence sequenceName="fee_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-24">
        <dropSequence sequenceName="itemcategory_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-25">
        <dropSequence sequenceName="payment_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-26">
        <dropSequence sequenceName="subpayment_id_seq"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-2">
        <addForeignKeyConstraint baseColumnNames="cart_id" baseTableName="cart_item"
                                 constraintName="fk1uobyhgl1wvgt1jpccia8xxs3" onDelete="NO ACTION" onUpdate="NO ACTION"
                                 referencedColumnNames="cart_id" referencedTableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-3">
        <modifyDataType columnName="amount" newDataType="number(19,2)" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-4">
        <dropNotNullConstraint columnDataType="uuid" columnName="cart_id" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-5">
        <modifyDataType columnName="category_name" newDataType="varchar(255)" tableName="itemcategory"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-6">
        <addNotNullConstraint columnDataType="boolean" columnName="collapsible" tableName="fee" validate="true"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-7">
        <modifyDataType columnName="description" newDataType="varchar(255)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-8">
        <modifyDataType columnName="fee_name" newDataType="varchar(255)" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-9">
        <dropDefaultValue columnDataType="boolean" columnName="included" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-10">
        <modifyDataType columnName="item_identifier" newDataType="varchar(255)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-11">
        <modifyDataType columnName="name" newDataType="varchar(255)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-12">
        <modifyDataType columnName="operation" newDataType="varchar(255)" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-13">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="operation" tableName="fee"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-14">
        <modifyDataType columnName="price" newDataType="number(19,2)" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-15">
        <dropNotNullConstraint columnDataType="number(19,2)" columnName="price" tableName="cart_item"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-16">
        <modifyDataType columnName="status" newDataType="varchar(255)" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-17">
        <dropNotNullConstraint columnDataType="varchar(255)" columnName="status" tableName="cart"/>
    </changeSet>
    <changeSet author="davidr (generated)" id="1692828482875-18">
        <modifyDataType columnName="user_id" newDataType="varchar(255)" tableName="cart"/>
    </changeSet>
</databaseChangeLog>

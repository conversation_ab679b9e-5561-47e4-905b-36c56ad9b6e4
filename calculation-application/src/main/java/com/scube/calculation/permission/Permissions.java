package com.scube.calculation.permission;

/**
 * Generated DO NOT MODIFY!
 * date: 2024-04-17T16:22:09.909610200Z
 */
public class Permissions {
    private Permissions() {
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-17T16:22:09.911607200Z
     */
    public static class LoggedInUserFee {
        public static final String GET_ALL_FEES = "calculation-service-me-fees-get-all-fees";

        public static final String GET_FEES_BY_KEYS = "calculation-service-me-fees-get-fees-by-keys";

        private LoggedInUserFee() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-17T16:22:09.924123100Z
     */
    public static class LoggedInUserOrder {
        public static final String CREATE_ORDER_FROM_CART = "calculation-service-me-order-create-order-from-cart";

        public static final String CANCEL_ORDER = "calculation-service-me-order-cancel-order";

        public static final String GET_ORDER = "calculation-service-me-order-get-order";

        public static final String LIST_ORDERS = "calculation-service-me-order-list-orders";

        public static final String ROLL_BACK_TO_CART = "calculation-service-me-order-roll-back-to-cart";

        private LoggedInUserOrder() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-17T16:22:09.925123900Z
     */
    public static class Order {
        public static final String CREATE_ORDER_FROM_CART = "calculation-service-order-create-order-from-cart";

        public static final String ROLL_BACK_TO_CART = "calculation-service-order-roll-back-to-cart";

        public static final String CANCEL_ORDER = "calculation-service-order-cancel-order";

        public static final String TRANSFER_ORDER = "calculation-service-order-transfer-order";

        public static final String EDIT_ORDER = "calculation-service-order-edit-order";

        public static final String GET_ORDER = "calculation-service-order-get-order";

        public static final String GET_ORDER_ID = "calculation-service-order-get-order-id";

        public static final String CREATE_ORDER_FROM_ITEMS = "calculation-service-order-create-order-from-items";

        public static final String LIST_ORDERS = "calculation-service-order-list-orders";

        public static final String FIND_ORDERS_BY_FILTERS = "calculation-service-order-find-orders-by-filters";

        private Order() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-17T16:22:09.925123900Z
     */
    public static class LoggedInUserCart {
        public static final String CREATE_CART = "calculation-service-me-cart-create-cart";

        public static final String GET_ACTIVE_CART = "calculation-service-me-cart-get-active-cart";

        public static final String LIST_CART = "calculation-service-me-cart-list-cart";

        public static final String GET_CART_INVOICE = "calculation-service-me-cart-get-cart-invoice";

        public static final String ADD_ITEM = "calculation-service-me-cart-add-item";

        public static final String REMOVE_ITEM = "calculation-service-me-cart-remove-item";

        public static final String REMOVE_CART_ITEM_BY = "calculation-service-me-cart-remove-cart-item-by";

        public static final String CLEAR_CART = "calculation-service-me-cart-clear-cart";

        private LoggedInUserCart() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-17T16:22:09.925123900Z
     */
    public static class Fee {
        public static final String CREATE_FEE = "calculation-service-fees-create-fee";

        public static final String GET_ALL_FEES = "calculation-service-fees-get-all-fees";

        public static final String GET_FEES_BY_KEYS = "calculation-service-fees-get-fees-by-keys";

        public static final String GET_MANUAL_FEES = "calculation-service-fees-get-manual-fees";
        public static final String UPSERT_FEES = "calculation-service-fees-upsert-fees";

        private Fee() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-17T16:22:09.926123400Z
     */
    public static class Cart {
        public static final String CREATE_CART = "calculation-service-cart-create-cart";

        public static final String GET_ACTIVE_CART = "calculation-service-cart-get-active-cart";

        public static final String LIST_CARTS = "calculation-service-cart-list-carts";

        public static final String GET_CART_INVOICE = "calculation-service-cart-get-cart-invoice";

        public static final String ADD_ITEM = "calculation-service-cart-add-item";

        public static final String REMOVE_ITEM = "calculation-service-cart-remove-item";

        public static final String REMOVE_CART_ITEM_BY = "calculation-service-cart-remove-cart-item-by";

        public static final String GET_PREVIEW_INVOICE = "calculation-service-cart-get-preview-invoice";

        public static final String GET_CART_SUMMARY = "calculation-service-cart-get-cart-summary";

        public static final String SWITCH_ACTIVE_CART = "calculation-service-cart-switch-active-cart";

        public static final String CLEAR_CART = "calculation-service-cart-clear-cart";

        public static final String DELETE_CART = "calculation-service-cart-delete-cart";

        public static final String FIND_CARTS_BY_FILTERS = "calculation-service-cart-find-carts-by-filters";
        public static final String GET_CART_ITEM = "calculation-service-cart-get-cart-item";
        public static final String CHANGE_PRICE_ON_CART_ITEM_FEE = "calculation-service-cart-change-price-on-cart-item-fee";
        public static final String REMOVE_CART_ITEM_FEE = "calculation-service-cart-remove-cart-item-fee";
        public static final String ADD_FEE_ON_CART_ITEM = "calculation-service-cart-add-fee-on-cart-item";

        private Cart() {
        }
    }

    /**
     * Generated DO NOT MODIFY!
     * date: 2024-04-17T16:22:09.926123400Z
     */
    public static class Permission {
        public static final String SEED_ROLES_TO_ALL_REALMS = "calculation-service-permissions-seed-roles-to-all-realms";

        public static final String SEED_ROLES_BY_REALM = "calculation-service-permissions-seed-roles-by-realm";

        private Permission() {
        }
    }
}

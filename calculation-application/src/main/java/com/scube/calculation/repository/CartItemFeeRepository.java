package com.scube.calculation.repository;

import com.scube.audit.auditable.repositories.AuditableEntityRepository;
import com.scube.calculation.model.CartItemFee;
import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface CartItemFeeRepository extends AuditableEntityRepository<CartItemFee, Long> {
    @Transactional
    @Modifying
    @Query(value = "DELETE FROM cart_item_fee WHERE properties ->> 'entityFeeId' IS NOT NULL AND properties ->> 'entityFeeId' = :entityFeeId", nativeQuery = true)
    void deleteByEntityFeeIdFromProperties(@Param("entityFeeId") @NotNull String entityFeeId);

    @Transactional
    @Query(value = "SELECT * FROM cart_item_fee WHERE properties ->> 'entityFeeId' IS NOT NULL AND properties ->> 'entityFeeId' = :entityFeeId", nativeQuery = true)
    CartItemFee findByEntityFeeIdFromProperties(@Param("entityFeeId") @NotNull String entityFeeId);
}

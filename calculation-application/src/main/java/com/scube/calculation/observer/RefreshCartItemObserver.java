package com.scube.calculation.observer;

import com.scube.calculation.model.CartItem;
import com.scube.calculation.repository.CartItemRepository;
import com.scube.calculation.service.CartService;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Component("refresh")
@RequiredArgsConstructor
public class RefreshCartItemObserver implements ICalculationObserver {
    private final CartService cartService;
    private final AmqpGateway amqpGateway;
    private final CartItemRepository cartItemRepository;

    @Override
    public void update(List<UUID> entityIds) {
        var cartItems = cartItemRepository.findByUniqueItemIdInAndItemTypeIdIn(entityIds, CartService.NON_UNIQUE_ITEMS_TYPE_ID);
        Map<UUID, List<UUID>> cartIdToItemIds = cartItems.stream()
                .collect(Collectors.groupingBy(
                        ci -> ci.getCart().getId(),
                        Collectors.mapping(CartItem::getUniqueItemId, Collectors.toList())
                ));
        cartIdToItemIds.forEach(cartService::removeItemByItemIds);
        cartItems.forEach(cartItem -> amqpGateway.publish(new RefreshCartItemObserver.AddItemToCartEvent(cartItem.getItemTypeId(), cartItem.getUniqueItemId(), cartItem.getCart().getId(), false)));
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddItemToCartEvent implements IRabbitFanoutPublisher {
        private String itemType;
        private UUID itemId;
        private UUID cartId;
        private boolean isMe;
    }
}

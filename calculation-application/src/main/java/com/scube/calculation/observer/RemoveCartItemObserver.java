package com.scube.calculation.observer;


import com.scube.calculation.service.CartService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.UUID;


@Component("remove")
@RequiredArgsConstructor
public class RemoveCartItemObserver implements ICalculationObserver {
    private final CartService cartService;

    @Override
    public void update(List<UUID> entityIds) {
        cartService.removeEntityFromAllCarts(entityIds);
    }
}

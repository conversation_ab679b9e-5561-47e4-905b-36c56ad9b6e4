package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Getter
@Setter
@Entity
@Table(name = OrderAdditionalFee.TABLE_NAME)
@NoArgsConstructor
@Audited
@AllArgsConstructor
public class  OrderAdditionalFee extends AuditableEntity {
    public static final String TABLE_NAME = "order_additional_fee";
    public static final String ADDITIONAL_FEE_ID = "order_additional_fee_id";

    @ManyToOne
    @JoinColumn(name = Order.ORDER_ID)
    private Order order;

    @OneToMany(mappedBy = "orderAdditionalFee", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<OrderAdditionalItemFee> additionalItemFees = new ArrayList<>();

    private BigDecimal price;
    @Size(max = 255)
    private String label;
    @Size(max = 255)
    private String itemTypeId;

    private UUID uniqueItemId;
    @Size(max = 255)
    private String name;

    @Size(max = 255)
    private String description;

    public OrderAdditionalFee(AdditionalFee additionalFee, Order order) {
        this.order = order;
        this.label = additionalFee.getLabel();
        this.name = additionalFee.getName();
        this.description = additionalFee.getDescription();
        this.uniqueItemId = additionalFee.getUniqueItemId();
        this.itemTypeId = additionalFee.getItemTypeId();
        this.setProperties(additionalFee.getProperties());
        this.additionalItemFees = additionalFee.getAdditionalItemFees().stream().map(x -> new OrderAdditionalItemFee(this, x.getFee(),null)).collect(Collectors.toList());
    }



    public BigDecimal calculatePrice() {
        return  additionalItemFees.stream().map(OrderAdditionalItemFee::calculatePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}

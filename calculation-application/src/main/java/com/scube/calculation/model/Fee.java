package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.calculation.enums.FeeType;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;

@Getter
@Setter
@Entity
@Audited
@Table(name = "fee")
public class Fee extends AuditableEntity {
    public static final String FEE_ID = "fee_id";

    @Column(unique = true)
    @Size(max = 255)
    private String key;

    @Size(max = 255)
    private String feeName;

    private int payableId;

    private BigDecimal amount = BigDecimal.ZERO;

    @Enumerated(EnumType.STRING)
    private FeeType operation;

    // When this is true, multiple instances of this fee on an invoice will be combined.
    private boolean collapsible;

    public BigDecimal getFeePrice(BigDecimal itemPrice, BigDecimal feePrice) {
        return switch (operation) {
            case FLAT -> getAmount();
            case MANUAL -> feePrice;
            case PERCENTAGE -> getAmount().multiply(itemPrice).divide(BigDecimal.valueOf(100));
        };
    }
}

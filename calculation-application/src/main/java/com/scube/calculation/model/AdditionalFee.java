package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = AdditionalFee.TABLE_NAME)
@NoArgsConstructor
@Audited
@AllArgsConstructor
public class AdditionalFee extends AuditableEntity {
    public static final String TABLE_NAME = "cart_additional_fee";
    public static final String ADDITIONAL_FEE_ID = "cart_additional_fee_id";

    @ManyToOne
    @JoinColumn(name = Cart.CART_ID)
    private Cart cart;

    @OneToMany(mappedBy = "additionalFee", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<AdditionalItemFee> additionalItemFees = new ArrayList<>();

    private BigDecimal price = BigDecimal.ZERO;
    @Size(max = 255)
    private String label;

    @Size(max = 255)
    private String itemTypeId;

    private UUID uniqueItemId;
    @Size(max = 255)
    private String name;

    @Size(max = 255)
    private String description;

    public AdditionalFee(Cart cart, List<Fee> fees, String label, String itemTypeId){
        this.cart = cart;
        this.label = label;
        this.price = BigDecimal.ZERO;
        this.uniqueItemId = UUID.randomUUID();
        this.itemTypeId = itemTypeId;
        this.name = label;
        this.setFees(fees);
    }

    public void setFees(List<Fee> fees) {
        this.additionalItemFees.clear();
        fees.forEach(fee -> {
            AdditionalItemFee cartItemFee = new AdditionalItemFee(this, fee);
            this.additionalItemFees.add(cartItemFee);
        });
    }

    public BigDecimal calculatePrice() {
        return additionalItemFees.stream()
                .map(AdditionalItemFee::calculatePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}

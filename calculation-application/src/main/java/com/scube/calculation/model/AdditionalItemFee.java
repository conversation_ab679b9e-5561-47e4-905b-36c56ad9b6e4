package com.scube.calculation.model;

import com.scube.audit.auditable.entity.AuditableEntity;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.*;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;


@Getter
@Setter
@Entity
@Table(name = AdditionalItemFee.TABLE_NAME)
@NoArgsConstructor
@Audited
@AllArgsConstructor
public class AdditionalItemFee  extends AuditableEntity {
    public static final String TABLE_NAME = "cart_additional_item_fee";

    @ManyToOne
    @JoinColumn(name = AdditionalFee.ADDITIONAL_FEE_ID, nullable = false)
    private AdditionalFee additionalFee;

    @ManyToOne
    @JoinColumn(name = Fee.FEE_ID, nullable = false)
    private Fee fee;

    @Getter(AccessLevel.NONE)
    private BigDecimal price;

    public AdditionalItemFee(AdditionalFee additionalFee, Fee fee) {
        this.additionalFee = additionalFee;
        this.fee = fee;
    }


    public BigDecimal calculatePrice() {
        BigDecimal cartTotal = additionalFee.getCart().getCartTotal();
        BigDecimal amount = (this.price == null || this.price.equals(BigDecimal.ZERO)) ? fee.getAmount() : this.price;
        Map<String, Object> properties = this.getProperties();
        BigDecimal minFee = new BigDecimal(String.valueOf(properties.getOrDefault("minFee",0)));
        if (fee.getOperation() == null) return amount;
        var res = switch (fee.getOperation()) {
            case FLAT, MANUAL -> amount;
            case PERCENTAGE -> amount.multiply(cartTotal).divide(BigDecimal.valueOf(100));
        };
        if (res.compareTo(BigDecimal.ZERO) > 0) {
            res = res.setScale(2, RoundingMode.CEILING)
                    .max(minFee.setScale(2, RoundingMode.CEILING));
        }
        return res;
    }
}

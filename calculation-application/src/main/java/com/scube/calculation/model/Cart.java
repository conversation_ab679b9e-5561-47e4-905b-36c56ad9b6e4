package com.scube.calculation.model;


import com.scube.audit.auditable.entity.AuditableBaseWithProperties;
import com.scube.calculation.enums.CartStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Entity
@Audited
@Table(name = Cart.TABLE_NAME)
public class Cart extends AuditableBaseWithProperties<UUID> {
    public static final String TABLE_NAME = "cart";
    public static final String CART_ID = "cart_id";

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Size(max = 255)
    private String userId;

    @OneToMany(mappedBy = "cart", cascade = CascadeType.ALL)
    private List<CartItem> cartItems = new ArrayList<>();

    @OneToMany(mappedBy = "cart", cascade = CascadeType.ALL)
    private List<AdditionalFee> additionalFees = new ArrayList<>();

    @Enumerated(EnumType.STRING)
    private CartStatus status;

    public Cart(String userId) {
        this.status = CartStatus.CART_ACTIVE;
        this.userId = userId;
    }

    public Cart() {
        this.status = CartStatus.CART_ACTIVE;
    }

    public void addItem(CartItem item) {
        item.setCart(this);
        cartItems.add(item);
    }

    public void addAdditionalFee(List<AdditionalFee> additionalFeeList) {
        for (AdditionalFee additionalFee : additionalFeeList) {
            additionalFee.setCart(this);
            additionalFees.add(additionalFee);
        }
    }

    public Cart(Order order) {
        setActive();
        setCartFromOrder(order);
    }

    public void setCartFromOrder(Order order) {
        this.userId = order.getUserId();

        List<CartItem> items = new ArrayList<>();
        for (OrderItem orderItem : order.getOrderItems()) {
            if (this.cartItems.stream()
                    .anyMatch(cartItem -> cartItem.getUniqueItemId().equals(orderItem.getUniqueItemId()))) {
                continue;
            }
            CartItem cartItem = new CartItem(orderItem);
            cartItem.setCart(this);
            items.add(cartItem);
        }
        this.cartItems.addAll(items);
    }

    public void setActive() {
        this.status = CartStatus.CART_ACTIVE;
    }

    public BigDecimal getCartTotal() {
        return this.getCartItems().stream()
                .flatMap(cartItem -> cartItem.getCartItemFees().stream())
                .map(CartItemFee::calculatePrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
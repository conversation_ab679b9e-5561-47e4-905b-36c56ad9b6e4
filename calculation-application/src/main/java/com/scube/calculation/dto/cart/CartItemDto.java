package com.scube.calculation.dto.cart;

import com.scube.calculation.dto.AddItemRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CartItemDto {
    private Long id = null;
    private String createdBy = null;
    private Instant createdDate = null;
    private String lastModifiedBy = null;
    private Instant lastModifiedDate = null;
    private String conversionReference = null;
    private String itemTypeId;
    private UUID uniqueItemId;
    private BigDecimal price;
    private String name;
    private String description;
    private List<CartItemFeeDto> fees = new ArrayList<>();
    private Map<String, Object> properties = new HashMap<>();
    private UUID cartId;

    public CartItemDto(AddItemRequest request) {
        if (request.getBasePrice() == null) request.setBasePrice(BigDecimal.ZERO);
        this.price = request.getBasePrice();
        this.name = request.getName();
        this.description = request.getDescription();
        this.itemTypeId = request.getItemType();
        this.uniqueItemId = request.getItemId();
    }
}

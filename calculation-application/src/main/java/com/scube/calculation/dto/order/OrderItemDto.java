package com.scube.calculation.dto.order;

import com.scube.calculation.dto.AddItemRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class OrderItemDto {
    private Long id = null;
    private String createdBy = null;
    private Instant createdDate = null;
    private String lastModifiedBy = null;
    private Instant lastModifiedDate = null;
    private String conversionReference = null;
    private String itemTypeId;
    private UUID uniqueItemId;
    private UUID orderItemUuid;
    private BigDecimal price;
    private String name;
    private String description;
    private List<OrderItemFeeDto> fees = new ArrayList<>();
    private Map<String, Object> properties = new HashMap<>();

    public OrderItemDto(AddItemRequest request) {
        if (request.getBasePrice() == null) request.setBasePrice(BigDecimal.ZERO);
        this.price = request.getBasePrice();
        this.name = request.getName();
        this.description = request.getDescription();
        this.itemTypeId = request.getItemType();
        this.uniqueItemId = request.getItemId();
        this.properties = request.getProperties();
    }
}

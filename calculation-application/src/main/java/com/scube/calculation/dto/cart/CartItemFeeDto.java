package com.scube.calculation.dto.cart;

import com.scube.calculation.dto.fee.FeeDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CartItemFeeDto {
    private UUID uuid;
    private FeeDto fee;
    private BigDecimal price = BigDecimal.ZERO;
    private Map<String, Object> properties = new HashMap<>();
}
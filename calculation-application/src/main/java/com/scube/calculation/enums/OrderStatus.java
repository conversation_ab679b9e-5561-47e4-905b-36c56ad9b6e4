package com.scube.calculation.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum OrderStatus {
    ORDER_OPEN("orderOpen"),
    ORDER_LOCKED("orderLocked"),
    ORDER_PAID("orderPaid"),
    ORDER_CANCELED("orderCancelled");

    private final String key;

    OrderStatus(String key) {
        this.key = key;
    }

    public static OrderStatus valueOfIgnoreCase(String key) {
        for (OrderStatus status : OrderStatus.values()) {
            if (status.getKey().equalsIgnoreCase(key)) {
                return status;
            }
        }
        return null;
    }

    public static List<OrderStatus> getAllStatuses() {
        List<OrderStatus> statuses = new ArrayList<>();
        for (OrderStatus status : OrderStatus.values()) {
            statuses.add(status);
        }
        return statuses;
    }
}
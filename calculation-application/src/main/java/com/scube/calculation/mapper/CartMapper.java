package com.scube.calculation.mapper;

import com.scube.calculation.dto.cart.*;
import com.scube.calculation.dto.fee.FeeDto;
import com.scube.calculation.model.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class CartMapper {
    public abstract CartDto toDto(Cart cart);

    public abstract List<CartDto> toDto(List<Cart> carts);

    public abstract Cart toEntity(CartDto cartDto);

    public abstract List<Cart> toEntity(List<CartDto> cartDtos);

    @Mapping(target = "fees", source = "cartItemFees")
    @Mapping(target = "cartId", source = "cart.id")
    public abstract CartItemDto toDto(CartItem cartItem);

    public abstract FeeDto toDto(Fee fee);

    @Mapping(target = "price", expression = "java(cartItemFee.calculatePrice())")
    public abstract CartItemFeeDto toDto(CartItemFee cartItemFee);

    @Mapping(target = "fees", source = "additionalItemFees")
    @Mapping(target = "price", expression = "java(additionalFee.calculatePrice())")
    public abstract AdditionalFeeDto toDto(AdditionalFee additionalFee);

    @Mapping(target = "price", expression = "java(additionalItemFee.calculatePrice())")
    public abstract AdditionalItemFeeDto toDto(AdditionalItemFee additionalItemFee);
}
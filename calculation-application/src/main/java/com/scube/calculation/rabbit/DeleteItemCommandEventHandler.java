package com.scube.calculation.rabbit;

import com.scube.calculation.repository.CartRepository;
import com.scube.calculation.service.CartService;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class DeleteItemCommandEventHandler extends FanoutListener<DeleteItemCommandEventHandler.DeleteItemCommandEvent> {
    private final CartService cartService;
    private final CartRepository cartRepository;

    @Override
    public void consume(DeleteItemCommandEvent event) {
        var order = cartRepository.findCartsByUniqueItemId(UUID.fromString(event.getItemEntityId()));
        for (var cart : order) {
            cartService.removeItem(cart.getId(), UUID.fromString(event.getItemEntityId()));
        }
    }

    @Data
    @NoArgsConstructor
    public static class DeleteItemCommandEvent implements IRabbitFanoutSubscriber {
        private String itemEntityId;
    }
}

package com.scube.calculation.rabbit;

import com.scube.calculation.dto.GetOrderIDResponse;
import com.scube.calculation.service.OrderService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriberRpc;
import lombok.*;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class GetOrderIdByItemIdQueryHandler extends FanoutListenerRpc<GetOrderIdByItemIdQueryHandler.GetOrderIdByItemIdQuery, GetOrderIDResponse> {
    private final OrderService orderService;

    public RabbitResult<GetOrderIDResponse> consume(GetOrderIdByItemIdQuery event) {
        return RabbitResult.of(() ->
                orderService.getOrderId(UUID.fromString(event.getItemId()))
        );
    }


    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GetOrderIdByItemIdQuery implements IRabbitFanoutSubscriberRpc<GetOrderIDResponse> {
        private String itemId;
    }
}
name: "Production: Update, Build & Deploy"
run-name: Deploy ${{ github.repository }} ${{ github.ref_name }} to Production by @${{ github.actor }}

on:
    push:
        # Publish semver tags as releases.
        tags: [ 'v*.*.*' ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
jobs:
  build-deploy:
    uses: s-Cube-Enterprise/reusable-workflows/.github/workflows/jib-build-publish.yaml@main
    with:
      AWS_REGION: us-east-1
      ECR_REPOSITORY: service_auth
      EKS_DEPLOYMENT: scube-auth-service-depl
      EKS_CONTAINER: scube-auth-service
      KUBE_NAMESPACE: backend
      IMAGE_TAG: ${{ github.ref_name }}
      JAVA_VERSION: 21
      TARGET_ENVIRONMENT: PROD
    secrets: inherit

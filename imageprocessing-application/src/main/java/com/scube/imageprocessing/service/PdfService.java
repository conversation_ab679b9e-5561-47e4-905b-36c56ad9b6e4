package com.scube.imageprocessing.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.text.PDFTextStripper;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class PdfService {
    /**
     * Extracts text from standard pdfs
     *
     * @param pdf - byte[] of a pdf
     * @return String - extracted text
     */
    public String extractText(byte[] pdf) {
        log.debug("PdfService.extractText()");

        try (PDDocument document = PDDocument.load(pdf)) {
            PDFTextStripper stripper = new PDFTextStripper();

            return stripper.getText(document);
        } catch (IOException e) {
            log.error("Error extracting text from pdf");
            throw new RuntimeException(e);
        }
    }

    /**
     * Converts pdfs to images
     *
     * @param pdf       - MultipartFile of the pdf
     * @param imageType - the filetype of the resulting image
     * @return List<ByteArrayOutputStream> - A list containing 1 ByteArrayOutputStream per pdf page
     */
    public List<ByteArrayOutputStream> convertPdfToImage(byte[] pdf, String imageType) {
        log.debug("PdfService.convertPdfToImage()");

        try (PDDocument document = PDDocument.load(pdf)) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);
            List<ByteArrayOutputStream> imageList = new ArrayList<>();

            for (int page = 0; page < document.getNumberOfPages(); ++page) {
                BufferedImage bim = pdfRenderer.renderImageWithDPI(page, 300, ImageType.GRAY);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                ImageIO.write(bim, imageType, outputStream);
                imageList.add(outputStream);
            }

            return imageList;
        } catch (IOException e) {
            log.error("Error converting pdf to {}", imageType);
            throw new RuntimeException(e);
        }
    }
}

package com.scube.imageprocessing.util;

import lombok.experimental.UtilityClass;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@UtilityClass
public class MultipartFileUtil {
    /**
     * Generates a unique file name for a MultipartFile object.
     *
     * @param file the MultipartFile object
     * @return the unique file name
     */
    public static String generateUniqueFileName(MultipartFile file) {
        return getFileNameWithoutExtension(file)
                + "_" + System.currentTimeMillis()
                + "." + getFileExtension(file);
    }

    /**
     * Extracts the file extension from a MultipartFile object.
     *
     * @param file the MultipartFile object
     * @return the file extension or an empty string if none found
     */
    public static String getFileExtension(MultipartFile file) {
        if (file != null) {
            String fileName = file.getOriginalFilename();
            if (fileName != null && fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
                return fileName.substring(fileName.lastIndexOf(".") + 1);
            }
        }
        return "";
    }

    /**
     * Extracts the file name without its extension from a MultipartFile object.
     *
     * @param file the MultipartFile object
     * @return the file name without extension, or the full file name if no extension is found
     */
    public static String getFileNameWithoutExtension(MultipartFile file) {
        if (file != null) {
            String fileName = file.getOriginalFilename();
            if (fileName != null) {
                int dotIndex = fileName.lastIndexOf(".");
                if (dotIndex > 0) { // Greater than 0 to avoid files starting with a dot
                    return fileName.substring(0, dotIndex);
                }
            }
        }
        return "";
    }

    /**
     * Gets the bytes from a MultipartFile object.
     * @param file the MultipartFile object
     * @return byte[]
     */
    public static byte[] getBytes(MultipartFile file) {
        try {
            return file.getBytes();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}

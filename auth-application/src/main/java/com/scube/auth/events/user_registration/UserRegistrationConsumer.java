
package com.scube.auth.events.user_registration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.auth.services.UserService;
import com.scube.multi.tenant.annotations.AllowRabbitMqListener;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.server.ResponseStatusException;

import java.io.Serial;
import java.io.Serializable;
import java.util.Map;


@Slf4j
@Component
@RequiredArgsConstructor
public class UserRegistrationConsumer {
    private final AmqpGateway amqpGateway;
    private final UserService userService;

    @AllowRabbitMqListener
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "license_service_user_registration_queue", durable = "true"),
                    exchange = @Exchange(value = "keycloak.events", type = "topic"),
                    key = "KK.EVENT.CLIENT.*.SUCCESS.*.REGISTER"
            )
    )
    public void consume(UserRegistrationEvent message) {
        try {
            log.info("New User Registration: {}", message);

            var user = userService.findUserByUserId(message.getUserId());
            if (ObjectUtils.isEmpty(user))
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with userId: " + message.getUserId());

            message.setFirstName(user.getFirstName());
            message.setLastName(user.getLastName());
            message.setEmail(user.getEmail());

            amqpGateway.publish(message);
            log.info("Published UserRegistrationEvent: {}", message);
        } catch (Exception e) {
            log.error("Error while consuming UserRegistrationEvent: {}", message, e);
            throw new AmqpRejectAndDontRequeueException(e);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserRegistrationEvent implements Serializable, IRabbitFanoutPublisher {

        @Serial
        private static final long serialVersionUID = -2192461924304841222L;
        private String id;
        private long time;
        private String realmId;
        private String realmName;
        private String clientId;
        private String userId;
        private String firstName;
        private String lastName;
        private String email;
        private String error;
        private Map<String, String> details;
    }
}
package com.scube.auth.events.get_user_by_username;

import com.scube.auth.services.UserService;
import com.scube.rabbit.core.RabbitResult;
import com.scube.rabbit.core.fanout.subscriber.FanoutListenerRpc;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

@Component
@RequiredArgsConstructor
public class GetUserByUsernameQueryHandler extends FanoutListenerRpc<GetUserByUsernameQuery, GetUserByUsernameQueryResponse> {
    private final UserService userService;

    @Override
    public RabbitResult<GetUserByUsernameQueryResponse> consume(GetUserByUsernameQuery event) {
        return RabbitResult.of(() -> {
            var user = userService.findUserByUsername(event.username());
            if (ObjectUtils.isEmpty(user)) return new GetUserByUsernameQueryResponse(event.username());
            if (ObjectUtils.isEmpty(user.getFirstName()) || ObjectUtils.isEmpty(user.getLastName()))
                return new GetUserByUsernameQueryResponse(event.username());

            var name = user.getFirstName() + " " + user.getLastName();
            return new GetUserByUsernameQueryResponse(name);
        });
    }
}
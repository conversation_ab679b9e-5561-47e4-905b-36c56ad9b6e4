package com.scube.auth.websocket;

import org.springframework.web.socket.*;

import java.io.IOException;

public class <PERSON><PERSON>ebSocketHandler implements WebSocketHandler {

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String username = (String) session.getAttributes().get("username");
        System.out.println("Connected: " + username);
    }

    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws IOException {
        String username = (String) session.getAttributes().get("username");
        String tenant = (String) session.getAttributes().get("tenant");

        session.sendMessage(new TextMessage(" User :" +username +" TENANT: "+tenant+" Echo: " + message.getPayload()));
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws IOException {
        session.close();
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) {
        System.out.println("Disconnected");
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }
}
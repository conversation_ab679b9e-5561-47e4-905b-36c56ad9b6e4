package com.scube.auth.controllers;

import com.scube.auth.dto.AllRealmDto;
import com.scube.auth.dto.RealmDto;
import com.scube.auth.services.RealmService;
import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/public/realm")
@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.AUTH_SERVICE)
@Validated
public class PublicRealmController {
    private final RealmService realmService;

    @GetMapping(name = "Get all realms")
    @Operation(summary = "Get all realms", description = "Returns all realms with their identity providers")
    @ApiResponse(responseCode = "200", description = "Realms found")
    public AllRealmDto getAllRealms(@RequestParam(name = "excludeRealmName", required = false) String... excludeRealmNames) {
        //TODO: create a table in database to hide or show tenants on the UI
        if (ObjectUtils.isEmpty(excludeRealmNames))
            excludeRealmNames = new String[]{"master", "ai-sandbox"};
        return realmService.getAllRealms(excludeRealmNames);
    }

    @GetMapping(value = "/names", name = "Get all realm names")
    @Operation(summary = "Get all realm names", description = "Returns all realm names")
    @ApiResponse(responseCode = "200", description = "Realm names found")
    public List<String> getAllRealmNames(@RequestParam(name = "excludeRealmName", required = false) String... excludeRealmNames) {
        //TODO: create a table in database to hide or show tenants on the UI
        if (ObjectUtils.isEmpty(excludeRealmNames))
            excludeRealmNames = new String[]{"master", "ai-sandbox"};
        return realmService.getAllRealmNames(excludeRealmNames);
    }

    @GetMapping("/{realmName}")
    @Operation(summary = "Get realm by id", description = "Returns realm with its identity providers")
    @ApiResponse(responseCode = "200", description = "Realm found")
    public RealmDto getRealm(@PathVariable @Size(max = 100) String realmName) {
        return realmService.getRealm(realmName);
    }

    @GetMapping("/{realmName}/name")
    @Operation(summary = "Get realm name by id", description = "Returns realm name")
    @ApiResponse(responseCode = "200", description = "Realm name found")
    public String getRealmName(@PathVariable @Size(max = 100) String realmName) {
        return realmService.getRealmName(realmName);
    }
}
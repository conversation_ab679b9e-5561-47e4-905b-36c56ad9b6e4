server:
  port: 9001
  servlet:
    context-path: /api/auth
  forward-headers-strategy: framework

spring:
  application:
    name: AuthService
  servlet:
    multipart:
      enabled: true
      file-size-threshold: 2KB
      max-file-size: 200MB
      max-request-size: 200MB

logging:
  level:
    org.springframework.web: "info"



keycloak:
  authoritiesConverterType: NATIVE
  host: http://localhost:8443/realms/master
  public-host: http://localhost:8443/realms/master
  admin:
    url: http://localhost:8443
    realm: master
    client-id: test
    client-secret: test
  swagger:
    url: http://localhost:8443
    realm: clerkXpress
    client-id: test

com.c4-soft.springaddons.oidc:
  ops:
    - iss: http://localhost:8443/realms/master
      jwkSetUri: http://localhost:8443/realms/master/protocol/openid-connect/certs
      username-claim: preferred_username
      authorities:
        - path: $.realm_access.roles
        - path: $.resource_access.*.roles
  resource-server:
    permit-all:
      - "/"
      - "/v3/api-docs/**"
      - "/swagger-ui/**"
      - "/swagger-ui.html"
      - "/public/**"
      - "/actuator/**"
    cors:
      - path: /**
        allowed-origin-patterns:
          - http://localhost:3000
          - https://localhost:3000
          - http://localhost:3030

multi-tenancy:
  enabled: false
  keycloak:
    enabled: false
  database:
    enabled: false


package com.scube.auth.permissions;

import com.c4_soft.springaddons.security.oauth2.test.annotations.WithJwt;
import com.scube.auth.SharedTestConfig;
import com.scube.auth.permission.Permissions;
import org.junit.jupiter.api.Test;

import static com.scube.auth.permissions.MockMvcHelper.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;

class RealmControllerTest extends SharedTestConfig {
    @Test
    @WithJwt(json = START_JSON + Permissions.Realm.GET_REALM_REPRESENTATION + END_JSON)
    void testGetRealmRepresentation_Success() throws Exception {
        var result = performGet(mockMvc, "/realm/test/representation");
        assertNotEquals(403, result.getResponse().getStatus());
    }

    @Test
    void testGetRealmRepresentation_Failure() throws Exception {
        var result = performGet(mockMvc, "/realm/test/representation");
        assertEquals(403, result.getResponse().getStatus());
    }

}